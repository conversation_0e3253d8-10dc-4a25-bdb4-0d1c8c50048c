import { styled } from "@linaria/react";
import { <PERSON><PERSON> } from "antd";
import { <PERSON><PERSON>, SearchPopup, SearchResult } from "../../components";
import { ReactComponent as MessageIcon } from "../../assets/message.svg";
import {
  ContainerOutlined,
  HistoryOutlined,
  PushpinOutlined,
} from "@ant-design/icons";
import {
  GridContextProvider,
  GridDropZone,
  GridItem,
  swap,
} from "react-grid-dnd";
import LogoImage from "../../assets/images/logo.png";
import { useEffect, useState } from "react";
import { useTheme } from "../../utils/useTheme";
import debounce from "lodash.debounce";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { useNavigate } from "react-router-dom";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { GET_LOCAL_SETTINGS_KEY, GET_SEARCH_RESULTS } from "../../constants";
import { ILocalSettings } from "../../interfaces";
import { saveLocalSettings } from "../../services";
import { useNotification } from "../../utils/functions/customHooks";
import { setMask } from "../../store/features";
import { getSearchResults } from "../../services/search";

const HomePage = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [displaySaveButton, setDisplaySaveButton] = useState(false);
  const [search, setSearch] = useState("");
  const [searchText, setSearchText] = useState("");
  const [width, setWidth] = useState(window.innerWidth);
  const [searchResult, setSearchResult] = useState(null);
  const [cards, setCards] = useState([]);
  const [dragging, setDragging] = useState(false);

  const { showDescription, application_name, logo, showLogo } = useSelector(
    (root: RootState) => root.localSettings
  );
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const CARD_ITEMS = [
    {
      label: t("Message"),
      icon: <MessageIcon />,
      url: "/message",
    },
    {
      label: t("Pinned"),
      icon: <PushpinOutlined />,
      url: "/pinned",
    },
    {
      label: t("History"),
      icon: <HistoryOutlined />,
      url: "/history",
    },
    // {
    //   icon: <StarOutlined />,
    //   label: t("My Grades"),
    //   url: "/grades",
    // },
    {
      icon: <ContainerOutlined />,
      label: t("My Comments"),
      url: "/comments",
    },
  ];

  function onChange(_, sourceIndex, targetIndex) {
    setDragging(true);
    const nextState = swap(cards, sourceIndex, targetIndex);
    detectChange();
    setCards(nextState);
    setTimeout(() => {
      setDragging(false);
    }, 400);
  }

  const getBoxPerRow = () => {
    if (width < 290) {
      return 1;
    }

    if (width < 565) {
      return 2;
    }
    if (width < 975) {
      return 3;
    }

    return 5;
  };

  useEffect(() => {
    const searchText = localStorage.getItem("home-search-text");
    if (searchText) {
      setSearchText(searchText);
      setSearch(searchText);
    }

    const handleResize = (entries) => {
      const entry = entries[0];
      setWidth(entry.contentRect.width);
    };
    const resizeObserver = new ResizeObserver(debounce(handleResize, 500));

    const element = document.getElementById("home-wrapper");
    if (element) {
      resizeObserver.observe(element);
    }

    return () => {
      if (element) {
        resizeObserver.unobserve(element);
      }
    };
  }, []);

  useEffect(() => {
    if (settingsData) {
      if (settingsData && settingsData?.body[0]?.value?.homev1Sections) {
        setCards(settingsData?.body[0]?.value?.homev1Sections);
      } else {
        setCards(CARD_ITEMS);
      }
    }
  }, [settingsData]);

  const handleCancel = () => {
    if (settingsData && settingsData?.body[0]?.value?.homev1Sections) {
      setCards(settingsData.body[0].value.homev1Sections);
    } else {
      setCards(CARD_ITEMS);
    }

    setDisplaySaveButton(false);
    dispatch(setMask(false));
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      setDisplaySaveButton(false);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      setDisplaySaveButton(false);
    },
  });

  const { data, isLoading } = useQuery(
    [GET_SEARCH_RESULTS, search],
    () => getSearchResults(search),
    {
      enabled: !!search,
    }
  );

  useEffect(() => {
    setSearchResult(data);
  }, [data]);

  const handlePublish = () => {
    if (settingsData) {
      const payload = [];
      cards?.forEach((card) => {
        payload.push({
          label: card.label,
          url: card.url,
        });
      });
      mutation.mutate({
        value: {
          ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
          homev1Sections: payload,
        },
      });
    }
  };

  const dispatch = useDispatch();

  const detectChange = () => {
    setDisplaySaveButton(true);
    dispatch(setMask(true));
  };

  const getIcon = (url) => {
    return CARD_ITEMS.find((item) => item.url === url)?.icon;
  };

  return (
    <Wrapper
      theme={theme}
      id="home-wrapper"
      style={displaySaveButton ? { border: "1px solid red" } : {}}
    >
      {displaySaveButton && (
        <div className="action-buttons">
          <Button
            className="breadcrumb-button cancel-button"
            type="primary"
            onClick={handleCancel}
          >
            {t("Cancel")}
          </Button>
          <Button
            className="breadcrumb-button save-button"
            type="primary"
            onClick={handlePublish}
            loading={mutation.isLoading}
          >
            {t("Save")}
          </Button>
        </div>
      )}
      <SearchWrapper theme={theme}>
        {contextHolder}
        <div className="logo-wrapper">
          {showLogo && <img src={logo || LogoImage} width={180} />}
          {showDescription && <p>{application_name}</p>}
        </div>
        <SearchPopup
          searchText={searchText}
          setSearchText={setSearchText}
          setSearch={(value) => {
            localStorage.setItem("home-search-text", searchText);
            setSearch(value);
          }}
          loading={isLoading}
        />
      </SearchWrapper>

      <Content theme={theme}>
        {search ? (
          <>
            {searchResult && (
              <SearchResult type="rows" searchResults={searchResult} />
            )}
          </>
        ) : (
          <Grid>
            <GridContextProvider onChange={onChange}>
              <GridDropZone
                id="items"
                rowHeight={150}
                boxesPerRow={getBoxPerRow()}
                style={{
                  height: 150 * Math.ceil(cards.length / getBoxPerRow()),
                }}
              >
                {cards.map((item) => (
                  <GridItem
                    key={item.label}
                    onClick={() => {
                      if (!dragging && !displaySaveButton) navigate(item.url);
                    }}
                  >
                    <Card theme={theme}>
                      {item.icon || getIcon(item.url)}
                      {item.label}
                    </Card>
                  </GridItem>
                ))}
              </GridDropZone>
            </GridContextProvider>
          </Grid>
        )}
      </Content>
      <Footer />
    </Wrapper>
  );
};

export default HomePage;

const SearchWrapper = styled.div<{ theme: any }>`
  min-height: 240px;
  background: ${({ theme }) => theme.background};
  display: flex;
  justify-content: center;
  flex-direction: column;

  & .version-info {
    color: white;
  }
  & .ant-input-suffix {
    cursor: pointer;

    & path {
      transition: all 0.5s;
    }
    &:hover path {
      fill: #185d9c;
    }
  }

  & .logo-wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;

    & > img {
      display: flex;
      margin-left: auto;
      margin-right: auto;
      object-fit: contain;
      max-height: 40px;
    }
    & p {
      color: #fff;
    }
  }
`;
const Grid = styled.div`
  & > div {
    height: 300px;
    width: 100%;
  }
`;
const Card = styled.div<{ theme: any }>`
  display: flex;
  height: 138px;
  margin-left: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 13px;
  color: ${({ theme }) => theme.colorPrimary};
  gap: 2px;
  cursor: pointer;
  border-radius: 10px;
  background: ${({ theme }) => theme.bgLight};
  &:hover {
    box-shadow: 0px 0px 5px rgb(17 86 151 / 63%);
  }

  & svg {
    width: 20px;
    height: 20px;

    & path {
      stroke: ${({ theme }) => theme.colorPrimary};
    }
  }
`;

const Content = styled.div<{ theme: any }>`
  margin-top: 50px;
  width: 60%;
  margin-left: auto;
  margin-right: auto;
  overflow: auto;

  @media (max-width: 1195px) {
    width: 70%;
    gap: 20px;
  }

  @media (max-width: 1025px) {
    width: 80%;
  }

  @media (max-width: 825px) {
    width: 90%;
    gap: 14px;
  }
`;
const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  height: 100%;
  background-color: #f3f3f3;
  display: flex;
  flex-direction: column;
  position: relative;

  & .action-buttons {
    position: absolute;
    right: 10px;
    display: flex;
    gap: 10px;
    top: 10px;

    & button {
      background-color: #fff;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  & .ant-input-group-wrapper {
    display: flex;

    & .ant-input-affix-wrapper {
      border: none;
    }

    & input {
      min-height: 36px;
      padding-left: 8px;
      font-family: "Poppins", sans-serif;
    }
    & .ant-input-group-addon {
      padding: 0px;
      border: 0px;
    }

    & button {
      height: 44px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      background: ${({ theme }) => theme.colorPrimary};
      padding: 4px 12px;

      & svg {
        width: 26px;
      }
      & path {
        fill: #fff;
      }
    }
  }
`;
