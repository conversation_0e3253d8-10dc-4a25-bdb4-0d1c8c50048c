import { API } from "../utils/api";

export const createObjectTemplate = (values) => {
  return API.post(`/metamodel/template/${values.parentId}/new`, {
    name: values.name,
  });
};

export const getWorkingVersion = (id) => {
  return API.get(`/metamodel/draft/${id}/get`);
};

export const publishWorkingVersion = (id) => {
  return API.post(`/metamodel/draft/${id}/publish`);
};

export const createWorkingVersion = (id) => {
  return API.post(`/metamodel/draft/${id}/new`);
};
