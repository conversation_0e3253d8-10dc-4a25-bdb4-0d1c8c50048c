import { Button, Flex } from "antd";
import { useMutation } from "react-query";
import {
  saveGlobalSettings,
  saveLocalSettings,
  saveTranslationsData,
} from "../../services";

// Personal use only, incase if you need to clear all data manually without backend help
// This is not a feature for production use
// This is a personal use only component to reset the database
const ResetDatabase = () => {
  const localMutation = useMutation(saveLocalSettings);
  const globalMutation = useMutation(saveGlobalSettings);
  const translationMutation = useMutation(saveTranslationsData);

  const handleResetLocal = () => {
    localMutation.mutate({
      value: {},
    });
  };

  const handleResetGlobal = () => {
    globalMutation.mutate({ value: {} });
  };

  const handleResetTranslations = () => {
    translationMutation.mutate({ value: {} });
  };

  return (
    <Flex gap={20} style={{ padding: 20 }}>
      <Button type="primary" onClick={handleResetLocal}>
        Reset Local settings
      </Button>
      <Button type="primary" onClick={handleResetGlobal}>
        Reset Global settings
      </Button>
      <Button type="primary" onClick={handleResetTranslations}>
        Reset Translations
      </Button>
    </Flex>
  );
};
export default ResetDatabase;
