import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

interface RouteProgressTrackerProps {
  isLoading?: boolean;
}

export const RouteProgressTracker: React.FC<RouteProgressTrackerProps> = ({
  isLoading = false,
}) => {
  const location = useLocation();
  const previousLocationRef = useRef(location.pathname);
  const routeLoadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Show top progress bar for route navigation (GitHub style)
  const showRouteProgress = () => {
    if (typeof window === "undefined") return;

    // Create or get existing route progress bar
    let routeProgressBar = document.getElementById("route-progress-bar");

    if (!routeProgressBar) {
      routeProgressBar = document.createElement("div");
      routeProgressBar.id = "route-progress-bar";
      routeProgressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 2px;
        background: linear-gradient(90deg, #4377A2 0%,rgb(144, 181, 224) 100%);
        z-index: 999998;
        transition: width 0.2s ease;
        opacity: 0;
        box-shadow: 0 0 5px rgba(67, 119, 162, 0.3);
      `;
      document.body.appendChild(routeProgressBar);
    }

    // Reset and show progress bar
    routeProgressBar.style.opacity = "1";
    routeProgressBar.style.width = "0%";

    // Immediate start
    requestAnimationFrame(() => {
      if (routeProgressBar) {
        routeProgressBar.style.width = "20%";
      }
    });

    // Progressive loading simulation
    setTimeout(() => {
      if (routeProgressBar) {
        routeProgressBar.style.width = "50%";
      }
    }, 100);

    setTimeout(() => {
      if (routeProgressBar) {
        routeProgressBar.style.width = "80%";
      }
    }, 300);

    return routeProgressBar;
  };

  const hideRouteProgress = () => {
    const routeProgressBar = document.getElementById("route-progress-bar");
    if (routeProgressBar) {
      // Complete the progress smoothly
      routeProgressBar.style.transition = "width 0.1s ease";
      routeProgressBar.style.width = "100%";

      // Hide after completion
      setTimeout(() => {
        routeProgressBar.style.opacity = "0";
        setTimeout(() => {
          routeProgressBar.style.width = "0%";
          routeProgressBar.style.transition = "width 0.2s ease"; // Reset transition
        }, 200);
      }, 100);
    }
  };

  // Track route changes
  useEffect(() => {
    const currentPath = location.pathname;
    const previousPath = previousLocationRef.current;

    // Only show progress for actual route changes (not initial load)
    if (previousPath !== currentPath && previousPath !== "") {
      showRouteProgress();

      // Clear any existing timeout
      if (routeLoadingTimeoutRef.current) {
        clearTimeout(routeLoadingTimeoutRef.current);
      }

      // Auto-hide progress bar after reasonable time
      routeLoadingTimeoutRef.current = setTimeout(() => {
        hideRouteProgress();
      }, 800);
    }

    previousLocationRef.current = currentPath;

    // Cleanup timeout on unmount
    return () => {
      if (routeLoadingTimeoutRef.current) {
        clearTimeout(routeLoadingTimeoutRef.current);
      }
    };
  }, [location.pathname]);

  // Handle loading state changes
  useEffect(() => {
    if (!isLoading) {
      // Clear timeout and hide progress when loading completes
      if (routeLoadingTimeoutRef.current) {
        clearTimeout(routeLoadingTimeoutRef.current);
      }
      // Small delay to ensure smooth completion
      setTimeout(() => {
        hideRouteProgress();
      }, 10);
    }
  }, [isLoading]);

  // Listen for when components are actually mounted/loaded
  useEffect(() => {
    const handleComponentLoad = () => {
      // Clear timeout and complete progress when component loads
      if (routeLoadingTimeoutRef.current) {
        clearTimeout(routeLoadingTimeoutRef.current);
      }
      hideRouteProgress();
    };

    // Listen for DOM content changes that indicate component loading
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          // Check if significant content was added
          const hasSignificantContent = Array.from(mutation.addedNodes).some(
            (node) =>
              node.nodeType === Node.ELEMENT_NODE &&
              (node as Element).children.length > 0
          );

          if (hasSignificantContent) {
            setTimeout(handleComponentLoad, 100);
          }
        }
      });
    });

    // Observe the main content area
    const mainContent = document.querySelector(".sidebar-wrapper");
    if (mainContent) {
      observer.observe(mainContent, {
        childList: true,
        subtree: true,
      });
    }

    return () => {
      observer.disconnect();
    };
  }, [location.pathname]);

  return null; // This component doesn't render anything
};

export default RouteProgressTracker;
