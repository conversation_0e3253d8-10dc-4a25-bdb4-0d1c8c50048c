import { Meta } from "@storybook/blocks";

<Meta title="Documentation/Introduction" />

<style>
  {`
    .subheading {
      --mediumdark: '#999999';
      font-weight: 700;
      font-size: 13px;
      color: #999;
      letter-spacing: 6px;
      line-height: 24px;

      text-transform: uppercase;
      margin-bottom: 12px;
      margin-top: 60px;
      margin-bottom: 30px;
    }

   

    .tip {
      display: inline-block;
      border-radius: 1em;
      font-size: 11px;
      line-height: 12px;
      font-weight: 700;
      background: #E7FDD8;
      color: #66BF3C;
      padding: 4px 12px;
      margin-right: 10px;
      vertical-align: top;
    }

    .tip-wrapper {
      font-size: 13px;
      line-height: 20px;
      margin-top: 40px;
      margin-bottom: 40px;
    }

    .tip-wrapper code {
      font-size: 12px;
      display: inline-block;
    }
  `}
</style>

# Welcome to CDO Tools Documentation

#### Naming convention guidelines

- Components follow `atomic design` inside `/src/components` separated into `atoms`, `molecules`, `organisms`
- Components are named with PascalCase. Your Awesome Component will be named as `YourAwesomeComponent`.
- Components contains
  - `index.tsx` describing components.
  - `index.test.tsx` for unit test files.
- Typscript `ts` files are named with `camelCasing` convention.
- Pages are stored in `pages` folder inside `src`. Page Components are named with PascalCase.
- Variables are named with camel case.
- Interfaces and Class names are pascal cased.
- Globals are all `CAPITALIZED`.

<div className="subheading">Package Scripts</div>

- `yarn install` to install dependencies and initialize repository
- `yarn dev` to run project,
- `yarn build` build the project,
- `yarn storybook` to run and view storybook documentations,
- `yarn build-storybook` to build storybook,
- `yarn lint` to run linting,

<div className="subheading">Folder Structure</div>

- `components` contains custom components built for project.
- `hooks` contains custom hooks that are used in project.
- `pages` contains pages layout which are imported in `App` for routing
- `static` contains static files like; images, audios etc.
- `utils` contains utility functions like imported from `lodash` or `date-fns`.
- `locales` for localization support.
- `assets` for images

For more idea on folder structure, refer [this article](https://medium.com/@aiska.basnet/better-way-to-structure-react-project-dbd478359ade) on medium by **Aiska Basnet**

<div className="subheading">What this project contains</div>

- React with typescript support.
- Folder structure defined for react.
- Ant Design Dependency (frontend library).
- Internationalization support i18n.
- Linting.
- Styling with linaria
- Public Private Routes (Guarded Routes).
- Proper error handling ( includes proper error format from backend and frontend error handling )
- File and variable naming convention and consistency.
- Typescript interfaces and type management

<div className="tip-wrapper">
  <span className="tip">Tip</span>To edit this Markdown , visit
  <code>stories/Introduction.mdx</code>
</div>
