import { styled } from "@linaria/react";
import { StyledModal } from "../../molecules";
import { useTranslation } from "react-i18next";
import { highlight } from "sql-highlight";

const ShowCodeModal = ({ isOpen, onCancel, value }) => {
  const { t } = useTranslation();

  return (
    <StyledModal
      open={isOpen}
      size={{ height: "300px", width: "625px" }}
      onCancel={onCancel}
      title={t("SQL Query")}
      contentClassName="padding-0"
    >
      <Wrapper>
        {" "}
        <span
          className="editor-content"
          dangerouslySetInnerHTML={{
            __html: highlight(value, {
              html: true,
            }),
          }}
        />
      </Wrapper>
    </StyledModal>
  );
};

export { ShowCodeModal };

const Wrapper = styled.div`
  height: 100%;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;

  & .editor-content {
    white-space: break-spaces;
  }
  & * {
    font-family: "courier new", courier, monospace;
  }

  & .sql-hl-keyword {
    color: #905;
    text-transform: uppercase;
  }

  & .sql-hl-special {
    color: black;
  }
  & .sql-hl-identifier {
    color: black;
  }

  & .sql-hl-string {
    color: brown;
  }
  & .sql-hl-number {
    color: red;
  }
`;
