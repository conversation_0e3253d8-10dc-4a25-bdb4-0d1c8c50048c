import { styled } from "@linaria/react";
import React, { useEffect, useState } from "react";
import { Button, Checkbox, Divider, Tooltip } from "antd";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { setDisplaySaveButton, setMask } from "../../../store/features/sidebar";
import { useSchedule } from "../../../utils/functions/customHooks/useSchedule";
import { AddSchedule } from "./AddSchedule";
import { MyTable } from "../../organisms";
import dayjs from "dayjs";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

export interface ISchedule {
  id?: number;
  startAt: any;
  cronExpressionValues: any[];
  enabled: boolean;
  endAt: any;
}

const EditScheduleBase = ({ onEdit, value }) => {
  const { t } = useTranslation();
  const { getSchedule } = useSchedule();
  const dispatch = useDispatch();

  const [addSchedule, setAddSchedule] = useState(false);
  const [schedules, setSchedules] = useState([] as ISchedule[]);
  const [editSchedule, setEditSchedule] = useState(null as ISchedule);

  const COLUMNS = [
    {
      headerName: "",
      field: "enabled",
      isAction: true,
      width: 60,
      maxWidth: 60,
      cellRenderer: ({ data }) => {
        return (
          <div className={"enable-check"}>
            <Checkbox
              checked={data.enabled}
              onChange={(e) => {
                const schedule = [...schedules];
                schedule[data.id - 1].enabled = e.target.checked;
                setSchedules([...schedule]);
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                onEdit(schedule.map(({ id, ...rest }) => rest));
              }}
            ></Checkbox>
          </div>
        );
      },
    },

    {
      headerName: "Start at",
      field: "startAt",
      flex: 1,
      minWidth: 100,
      cellRenderer: ({ data }) =>
        dayjs(data.startAt).format("YYYY-MM-DD HH:mm"),
    },
    {
      headerName: "Repeat",
      field: "repeat",
      flex: 1,
      minWidth: 100,
      cellRenderer: ({ data: schedule }) => {
        return schedule.cronExpressionValues?.length > 0
          ? getSchedule(schedule)
          : "one-time";
      },
    },
    {
      headerName: "Until",
      field: "until",
      flex: 1,
      minWidth: 100,
      cellRenderer: ({ data: schedule }) =>
        schedule.endAt ? dayjs(schedule.endAt).format("YYYY-MM-DD HH:mm") : "-",
    },
    {
      headerName: "Set by",
      field: "user",
      flex: 1,
      minWidth: 100,
      cellRenderer: () => "-",
    },
    {
      headerComponent: () => (
        <Button
          className="add-button"
          type="primary"
          onClick={() => {
            setAddSchedule(true);
            setEditSchedule(null);
          }}
        >
          <PlusOutlined />
        </Button>
      ),
      field: "user",
      width: 70,
      cellRenderer: ({ data: schedule }) => {
        return (
          <Actions>
            <Tooltip title={t("Edit")}>
              <EditOutlined
                onClick={() => {
                  setEditSchedule(schedule);
                  setAddSchedule(false);
                }}
              />
            </Tooltip>
            <Tooltip title={t("Delete")}>
              <DeleteOutlined
                onClick={() => {
                  const s = schedules.filter((item) => item.id !== schedule.id);
                  setSchedules([...s]);
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  onEdit(s?.map(({ id, ...rest }) => rest));
                }}
              />
            </Tooltip>
          </Actions>
        );
      },
    },
  ];

  useEffect(() => {
    const schedules = [];
    value?.forEach((schedule, index) => {
      schedules.push({
        ...schedule,
        id: index + 1,
        startAt: schedule?.startAt,
      });
    });
    setSchedules([...schedules]);
  }, [value]);

  return (
    <ScheduleContainer>
      {editSchedule ? (
        <AddSchedule
          schedules={schedules}
          editSchedule={editSchedule}
          handleCancel={() => {
            setEditSchedule(null);
            setAddSchedule(false);
          }}
          handleAdd={(newScheduled) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            onEdit(newScheduled?.map(({ id, ...rest }) => rest));
            setSchedules([...newScheduled]);
            setAddSchedule(false);
            setEditSchedule(null);
            dispatch(setMask(true));
            dispatch(setDisplaySaveButton(true));
          }}
        />
      ) : (
        <MyTable
          hideEmpty
          noHeader
          noSelect
          columns={COLUMNS}
          data={schedules}
          emptyMessage={t("No schedules!")}
        />
      )}
      {addSchedule && (
        <>
          <Divider />
          <AddSchedule
            schedules={schedules}
            editSchedule={editSchedule}
            handleCancel={() => {
              setEditSchedule(null);
              setAddSchedule(false);
            }}
            handleAdd={(newScheduled) => {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              onEdit(newScheduled?.map(({ id, ...rest }) => rest));
              setSchedules([...newScheduled]);
              setAddSchedule(false);
              setEditSchedule(null);
              dispatch(setMask(true));
              dispatch(setDisplaySaveButton(true));
            }}
          />
        </>
      )}
    </ScheduleContainer>
  );
};

const EditSchedule = withErrorBoundary(
  React.memo(EditScheduleBase),
  "error.generic"
);

export { EditSchedule };

const Actions = styled.div`
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;

  & .anticon-edit {
    color: var(--color-text);
  }

  & .anticon-delete {
    color: red;
  }
`;

const ScheduleContainer = styled.section`
  display: flex;
  flex-direction: column;
  background: white;

  & .enable-check .ant-checkbox-inner {
    width: 20px;
    height: 20px;
  }

  & .ant-checkbox-wrapper {
    margin-top: 0px;
  }

  & .enable-check .ant-checkbox-inner::after {
    inset-inline-start: 33%;
    width: 6px;
  }

  & .add-button {
    border: 1px solid green;
    margin: auto;
    background-color: white;
    box-shadow: none;
    color: green;
    height: fit-content;
    padding: 4px 7px;

    &:hover {
      background-color: white !important;
      color: green !important;
      opacity: 0.8;
    }
  }

  & .repeat-end {
    display: flex;
    flex-direction: column;
    border: none;

    & label {
      border: none;
      margin-bottom: 0px !important;
      gap: 4px;

      & span {
        display: flex;
        gap: 6px;
        align-items: center;
      }

      & .anticon {
        color: unset;
        font-size: 14px;
      }
      & input {
        width: 50px;
      }
    }
  }
  & .repeat-type {
    width: 275px !important;
  }
  & .ant-picker-calendar {
    & .ant-select {
      width: fit-content;
    }

    & .ant-radio-group {
      border: none;
      display: block;
    }
    & th {
      padding: 4px 0px !important;
    }
    & label {
      padding: 0px 8px !important;
    }
  }
  & .ant-select-selection-item {
    font-size: 13px;
    color: #5f6367;
  }

  /* & .ant-picker {
    width: 130px !important;
  } */
  & .choose-time {
    font-size: 13px;
    color: #5f6367;
    padding: 4px 11px;
  }
  & .button-wrapper {
    display: flex;
    gap: 7px;

    & i {
      font-size: 14px;
    }

    & button {
      font-size: 12px;
      height: 28px;
      background-color: white;
      box-shadow: none;
      width: fit-content;
      border: none !important;
      &:hover {
        background-color: white !important;
      }

      &:disabled {
        color: #606060;
        opacity: 0.8;
        &:hover {
          color: #606060 !important;
        }
      }
    }
  }

  & .repeat-every {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    & > div {
      max-width: 100px;
      min-width: 100px;
      width: unset;
    }
  }
  & .ant-checkbox::after {
    border: none !important;
  }
  & .checkbox-wrapper {
    padding: 6px;
    gap: 6px;
    border-bottom: 1px solid #eee;
    position: relative;
    display: flex;
    align-items: center;

    & .anticon {
      font-size: 15px;
      cursor: pointer;
    }
    & .anticon-edit {
      margin-left: auto;
    }
    & .anticon-delete {
      color: #e66060;
    }
    & label {
      padding: 0px 10px;
      margin-top: 0px !important;
      margin-left: 0px !important;

      & > span:last-child {
        padding: 6px 10px;
        font-size: 13px;
      }
    }
  }

  & .weeks {
    display: flex;
    gap: 10px;

    margin-bottom: 5px;
    & p {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    & .not-selected {
      color: #80868b;
      background: #f1f3f4;
    }
    & .selected {
      color: #fff;
      background: #4277a2;
    }
  }

  & .checked {
    background-color: #edf2f8;
  }

  & .not-checked {
    background: #f5f6f7;
  }

  & .title {
    color: #5f6368;
    font-weight: 500;
    min-width: 130px;
    display: flex;
    gap: 7px;
    align-items: center;
  }

  & .subtitle {
    color: #5f6368;
    font-weight: 500;
  }
  & a {
    color: #aaa9a9;
    padding: 10px 10px;
    cursor: pointer;
    border-top: 1px solid #eee;
  }

  & .cron_builder {
    margin-top: 10px;
  }
  & .add-schedule {
    padding: 10px 10px;
    & label {
      margin-bottom: 10px;
    }
  }
`;
