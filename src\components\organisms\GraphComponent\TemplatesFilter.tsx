import { styled } from "@linaria/react";
import { Tree } from "antd";
import React, { useEffect, useState } from "react";
import { useParentHeight } from "../../../utils/functions/customHooks";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../withErrorBoundary";

const TemplatesFilterBase = ({
  selectedFilters,
  treeData,
  handleTemplateClick,
  selectedTemplateId,
}) => {
  const { t } = useTranslation();
  const [templatesTreeData, setTemplatesTreeData] = useState([]);
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  useEffect(() => {
    const allTreeDatas = [];
    treeData.forEach((template) => {
      allTreeDatas.push({
        ...template,
        disabled: template.id == selectedTemplateId,
      });
    });
    setTemplatesTreeData(allTreeDatas);
  }, [treeData, selectedTemplateId]);

  return (
    <Wrapper ref={containerRef} className="tree-container">
      <div className="title">{t("Object templates")}</div>
      <Tree
        checkable
        checkedKeys={selectedFilters.templates}
        multiple
        checkStrictly
        showLine
        treeData={templatesTreeData}
        showIcon
        selectable={false}
        virtual
        height={containerHeight}
        titleRender={(node) => {
          return <div onClick={(e) => e.stopPropagation()}>{node.title}</div>;
        }}
        onCheck={(values: any, info) => {
          handleTemplateClick(info.node);
          // let edgeFilters = [...selectedFilters.edges];
          // if (info.node.isEdge) {
          //   if (edgeFilters.includes(info.node.id)) {
          //     edgeFilters = edgeFilters.filter((item) => item !== info.node.id);
          //   } else {
          //     edgeFilters.push(info.node.id);
          //   }
          // }

          // setSelectedFilters({
          //   ...selectedFilters,
          //   templates: values.checked,
          //   edges: edgeFilters,
          // });
        }}
      />
    </Wrapper>
  );
};

export const TemplatesFilter = withErrorBoundary(
  React.memo(TemplatesFilterBase),
  "error.generic"
);

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
  position: relative;

  & .title {
    background-color: #ccdaec;
    padding: 3px 10px;
    color: #4277a2;
    position: sticky;
    top: 0px;
    z-index: 1;
  }

  & .ant-tree-iconEle {
    margin-right: 4px;
  }
  & .ant-tree-node-content-wrapper img {
    object-fit: contain;
  }
`;
