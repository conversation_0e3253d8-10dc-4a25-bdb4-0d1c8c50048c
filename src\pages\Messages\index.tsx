import { <PERSON><PERSON>, <PERSON><PERSON>, Flex } from "antd";
import { useTheme } from "../../utils/useTheme";
import { Suspense, useCallback, useEffect, useState } from "react";
import { Wrapper } from "../HistoryPage/style";
import { BreadCrumb, MyTable } from "../../components";
import { TableLoadingSkeleton } from "../../components/organisms/MyTable/TableLoadingSkeleton";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { saveLocalSettings } from "../../services";
import { ILocalSettings } from "../../interfaces";
import { useNotification } from "../../utils/functions/customHooks";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";
import { MESSAGES_DUMMY_DATA } from "../../constants/DUMMY_TABLE_DATAS";

const MessagePage = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [resetTrigger, setResetTrigger] = useState(0);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [columnsRequest, setColumnsRequest] = useState([]);

  const [loading, setLoading] = useState(true);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  useEffect(() => {
    dispatch(setParentBreadcrumbs([...breadcrumb]));
    dispatch(setBreadcrumb([]));
    setData([...MESSAGES_DUMMY_DATA]);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.messageTable &&
      localSettingsData?.body[0]?.value?.messageTable?.columns.length > 0
    ) {
      if (localSettingsData?.body[0]?.value?.messageTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.messageTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.messageTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.messageTable.columns?.forEach(
          (column) => {
            const index = COLUMNS.findIndex((item) => item.field === column);
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.messageTable?.columns
        );
        setPinned(localSettingsData?.body[0]?.value?.messageTable?.pinned);
        setSort(localSettingsData?.body[0]?.value?.messageTable?.sort);
        setFilters(localSettingsData?.body[0]?.value?.messageTable?.filters);
        setColumns(allColumns);
      }
    } else {
      setColumns(COLUMNS);
      setColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  const detectChange = useCallback(() => {
    if (!mask) {
      dispatch(setMask(true));
    }
  }, [mask]);

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        messageTable: {
          columns: columnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setMask(false));
    }, 200);
  };

  return (
    <Suspense fallback={null}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10}>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    type="primary"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    type="primary"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />

        {data.length === 0 || columns.length === 0 ? (
          <TableLoadingSkeleton />
        ) : (
          <div
            className="content"
            style={{ border: mask ? "1px solid red" : "none" }}
          >
            <MyTable
              loading={loading}
              height={"calc(100vh - 165px)"}
              columns={columns}
              data={data}
              detectChange={detectChange}
              excelFileName="messages"
              resetTrigger={resetTrigger}
              setPinned={setPinned}
              setColumnsRequest={setColumnsRequest}
              setFilters={setFilters}
              setSort={setSort}
              initialFilters={
                localSettingsData?.body[0]?.value?.messageTable?.filters || {}
              }
            />
          </div>
        )}
      </Wrapper>
    </Suspense>
  );
};

export default MessagePage;

const COLUMNS = [
  {
    headerName: "Asset Name",
    field: "title",
    minWidth: 250,
    flex: 1,
  },
  {
    headerName: "Description",
    field: "description",
    minWidth: 250,
    flex: 1,
  },

  {
    headerName: "Date",
    field: "date",
    isDate: true,
    width: 250,
  },
  {
    headerName: "Author",
    field: "user",
    width: 200,
    cellRenderer: ({ data }) => (
      <>
        <Avatar size={32} src={data.image} /> {data.user}
      </>
    ),
  },
];

const breadcrumb = [
  {
    title: "Messages",
    to: "/message",
    mockup: true,
  },
];
