import { styled } from "@linaria/react";
import React, { useState } from "react";
import { useTheme } from "../../../../utils/useTheme";
import { ColorPickerModal } from "../ColorPickerModal";
import { MyTooltip } from "../../../atoms";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../../withErrorBoundary";

interface Props {
  colors: any;
  setColors: any;
}

const CustomThemeBase = ({ colors, setColors }: Props) => {
  const theme = useTheme() as any;
  const [action, setAction] = useState("");
  const { t } = useTranslation();

  return (
    <Wrapper>
      <Item theme={theme}>
        <h6>{t("Homepage background")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("background")}>
            <Indicator color={colors.background} />
            <p>{colors.background}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Homepage shortcuts background")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("bgLight")}>
            <Indicator color={colors.bgLight} />
            <p>{colors.bgLight}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Breadcrumbs, relations area top border")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("colorSecondary")}>
            <Indicator color={colors.colorSecondary} />
            <p>{colors.colorSecondary}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Font & Icon Colors")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("colorPrimary")}>
            <Indicator color={colors.colorPrimary} />
            <p>{colors.colorPrimary}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Breadcrumb font color")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("breadcrumbsColor")}>
            <Indicator color={colors.breadcrumbsColor} />
            <p>{colors.breadcrumbsColor}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Attributes background")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("bgAttributes")}>
            <Indicator color={colors.bgAttributes} />
            <p>{colors.bgAttributes}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Trash breadcrumb background")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("trashBreadcrumbsColor")}>
            <Indicator color={colors.trashBreadcrumbsColor} />
            <p>{colors.trashBreadcrumbsColor}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Trash breadcrumb font color")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("trashBreadcrumbsFontColor")}>
            <Indicator color={colors.trashBreadcrumbsFontColor} />
            <p>{colors.trashBreadcrumbsFontColor}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Metamodel breadcrumb background")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("metamodelBreadcrumbsColor")}>
            <Indicator color={colors.metamodelBreadcrumbsColor} />
            <p>{colors.metamodelBreadcrumbsColor}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <Item theme={theme}>
        <h6>{t("Metamodel breadcrumb font color")}</h6>
        <MyTooltip title={"Edit"} placement="right">
          <ColorCard onClick={() => setAction("metamodelBreadcrumbsFontColor")}>
            <Indicator color={colors.metamodelBreadcrumbsFontColor} />
            <p>{colors.metamodelBreadcrumbsFontColor}</p>
          </ColorCard>
        </MyTooltip>
      </Item>

      <ColorPickerModal
        action={action}
        setColors={setColors}
        colors={colors}
        open={!!action}
        onCancel={() => setAction("")}
      />
    </Wrapper>
  );
};

export const CustomTheme = withErrorBoundary(
  React.memo(CustomThemeBase),
  "error.generic"
);

const Indicator = styled.div<{ color: string }>`
  height: 24px;
  border-radius: 4px;
  background-color: ${({ color }) => color};
  width: 24px;
`;

const ColorCard = styled.div`
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  padding: 3px 7px 3px 3px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 0.88);
  margin-left: 10px;
  font-size: 14px;
  text-transform: uppercase;

  &:hover {
    border-color: #1e5782;
  }
`;

const Wrapper = styled.div``;
const Item = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  border: 0.5px solid #eaeaea;
  margin-bottom: 4px;

  & h6 {
    background-color: ${({ theme }) => theme.bgAttributes};
    border-right: 1px solid #eaeaea;
    width: 28%;
    padding: 10px;
    font-size: 12px;
    font-weight: 400;
    color: ${({ theme }) => theme.colorPrimary};
  }
`;
