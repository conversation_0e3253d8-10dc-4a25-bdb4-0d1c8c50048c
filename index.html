<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="referrer" content="no-referrer" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap"
    rel="stylesheet" />
  <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains; preload" />
  <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com; connect-src 'self' http://**********:8080; script-src 'self' https://cdn.jsdelivr.net; img-src 'self' data:;"
    /> -->
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <!-- <meta http-equiv="X-Frame-Options" content="DENY" /> -->
  <title>CDO.tools</title>
</head>

<body>
  <div id="root"></div>

  <!-- Initial Loading Screen -->
  <div id="initial-loader" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      z-index: 999999;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-family: 'Poppins', sans-serif;
    ">


    <!-- Progress Container -->
    <div style="
        width: 300px;
        position: relative;
      ">
      <!-- Progress Bar Background -->
      <div style="
          width: 100%;
          height: 12px;
          background: #e8f0f8;
          border-radius: 6px;
          overflow: hidden;
          position: relative;
        ">
        <!-- Progress Bar Fill -->
        <div id="progress-fill" style="
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #4377A2 0%, #5a8bc4 100%);
            border-radius: 6px;
            transition: width 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
          ">
          <!-- Glass reflection effect -->
          <div style="
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
              animation: shimmer 2s infinite;
            "></div>

          <!-- Inline Percentage Display -->
          <span id="progress-text" style="
              color: #ffffff;
              font-size: 11px;
              font-weight: 600;
              position: relative;
              z-index: 1;
              text-shadow: 0 1px 2px rgba(0,0,0,0.3);
              min-width: 25px;
              text-align: center;
              padding: 2px 4px;
              line-height: 1.2;
              display: inline-block;
            ">0%</span>
        </div>
      </div>
    </div>


  </div>

  <!-- CSS Animations -->
  <style>
    @keyframes shimmer {
      0% {
        left: -100%;
      }

      100% {
        left: 100%;
      }
    }



    /* Hide initial loader when app is ready */
    .app-ready #initial-loader {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.5s ease, visibility 0.5s ease;
    }
  </style>

  <!-- Progress Tracking Script -->
  <script>
    (function () {
      let currentProgress = 0;
      let targetProgress = 0;
      let progressStage = 'bundle';
      let completedStages = new Set();

      const progressFill = document.getElementById('progress-fill');
      const progressText = document.getElementById('progress-text');

      // Stage definitions with weights (totaling 100%)
      const STAGES = {
        bundle: { weight: 20 },
        auth: { weight: 15 },
        templates: { weight: 25 },
        settings: { weight: 15 },
        properties: { weight: 10 },
        menus: { weight: 10 },
        ready: { weight: 5 }
      };

      // Smooth progress animation
      function animateProgress() {
        if (currentProgress < targetProgress) {
          currentProgress += 0.5;
          if (currentProgress > targetProgress) {
            currentProgress = targetProgress;
          }
          updateUI();
          requestAnimationFrame(animateProgress);
        }
      }

      function updateUI() {
        progressFill.style.width = currentProgress + '%';
        progressText.textContent = Math.round(currentProgress) + '%';
      }

      function setProgress(stage, completed = false) {
        // Mark stage as completed if specified
        if (completed) {
          completedStages.add(stage);
        }

        // Calculate progress from actually completed stages
        let baseProgress = 0;
        for (const [key, config] of Object.entries(STAGES)) {
          if (completedStages.has(key)) {
            baseProgress += config.weight;
          }
        }

        // Add current stage progress if not completed yet
        let stageProgress = 0;
        if (!completed && !completedStages.has(stage)) {
          stageProgress = STAGES[stage].weight * 0.1; // 10% when started
        }

        const newTargetProgress = Math.min(baseProgress + stageProgress, 100);

        // Ensure progress never goes backward
        if (newTargetProgress > targetProgress) {
          targetProgress = newTargetProgress;
        }

        if (currentProgress === 0) {
          currentProgress = 1; // Start animation
        }

        animateProgress();
      }

      // Only simulate initial bundle loading if React app doesn't take control quickly
      let reactTookControl = false;

      setTimeout(() => {
        if (!reactTookControl) {
          setProgress('bundle');
        }
      }, 100);

      setTimeout(() => {
        if (!reactTookControl) {
          setProgress('bundle', true);
        }
      }, 800);

      // Allow React to take control
      window.setReactControlled = () => {
        reactTookControl = true;
      };

      // Function to reset progress for login scenarios
      function resetProgress() {
        completedStages.clear();
        currentProgress = 0;
        targetProgress = 0;
        updateUI();
      }

      // Function to complete all stages at once (for login page)
      function completeAllStages() {
        for (const stage of Object.keys(STAGES)) {
          completedStages.add(stage);
        }
        targetProgress = 100;
        currentProgress = 100;
        updateUI();
      }

      // Expose global functions for React app to use
      window.updateLoadingProgress = setProgress;
      window.resetLoadingProgress = resetProgress;
      window.completeAllLoadingStages = completeAllStages;
      window.setReactControlled = window.setReactControlled || (() => { });

      // Auto-hide loader if React app takes too long to initialize
      setTimeout(() => {
        if (document.getElementById('initial-loader').style.display !== 'none') {
          setProgress('ready', true);
          setTimeout(() => {
            document.body.classList.add('app-ready');
          }, 1000);
        }
      }, 10000);
    })();
  </script>

  <script type="module" src="/src/main.tsx"></script>
</body>

</html>