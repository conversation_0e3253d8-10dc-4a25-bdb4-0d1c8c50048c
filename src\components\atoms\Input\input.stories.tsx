import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Input } from ".";

const meta: Meta<typeof Input> = {
  title: "Components/Atoms/Input",
  component: Input,
};

export default meta;
type Story = StoryObj<typeof Input>;

export const DefaultInput: Story = {
  args: {
    label: "My Label",
  },
};

export const ErrorInput: Story = {
  args: {
    error: "abc",
  },
};

export const PasswordInput: Story = {
  args: {
    type: "password",
  },
};

export const TextArea: Story = {
  args: {
    type: "textarea",
  },
};

export const RestrictedCharacters: Story = {
  args: {
    label: "Node Name (Pipe character restricted)",
    restrictedCharacters: ["|"],
  },
};
