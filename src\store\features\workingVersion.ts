import { createSlice } from "@reduxjs/toolkit";

export interface WorkingVersionState {
  publishWorkingVersion: any;
  toUpdateFlag: any;
  removeTemplate: any;
}

const initialState: WorkingVersionState = {
  publishWorkingVersion: null,
  toUpdateFlag: null,
  removeTemplate: null,
};

export const workingVersionSlice = createSlice({
  name: "workingVersion",
  initialState,
  reducers: {
    setPublishWorkingVersion: (state, action) => {
      state.publishWorkingVersion = action.payload;
    },
    setUpdateFlag: (state, action) => {
      state.toUpdateFlag = action.payload;
    },
    setRemoveTemplate: (state, action) => {
      state.removeTemplate = action.payload;
    },
  },
});

export const { setPublishWorkingVersion, setUpdateFlag, setRemoveTemplate } =
  workingVersionSlice.actions;

export default workingVersionSlice.reducer;
