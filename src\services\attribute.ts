import { API } from "../utils/api";

export const getHyperLinks = (values: string[]) => {
  return API.post(`/model/node/relation-candidates/get`, {
    allowedLinks: values,
    attributeTypeCode: "relation",
  });
};

export const getLifecycles = (
  values: string[],
  templateId,
  selectedValue,
  nodeId,
  parentId
) => {
  return API.post(`/model/node/relation-candidates/get`, {
    templateId: templateId,
    attributeId: values.toString(),
    attributeTypeCode: "lifecycle",
    currentValue: selectedValue || null,
    nodeId: nodeId,
    parentId: parentId,
  });
};

export const getSourceForLifecycle = () => {
  return API.post(`/model/node/relation-candidates/get`, {
    allowedLinks: ["-213"],
  });
};
