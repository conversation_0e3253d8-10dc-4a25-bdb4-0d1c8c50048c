import React, { memo } from "react";
import type { CustomCellEditorProps } from "ag-grid-react";
import { TinyEditor } from "../../atoms";
import { withErrorBoundary } from "../../withErrorBoundary";

export default withErrorBoundary(
  memo(({ value, onValueChange }: CustomCellEditorProps) => {
    // Use a local state to keep the editor value in sync during editing
    const [editorValue, setEditorValue] = React.useState(value);

    // Update local state and propagate change to ag-Grid on every keystroke
    const handleEditorChange = (val) => {
      setEditorValue(val);
      onValueChange(val); // This will trigger ag-Grid to update the row data
    };

    React.useEffect(() => {
      setEditorValue(value);
    }, [value]);

    return (
      <div
        tabIndex={1}
        style={{ height: 300, width: "100%", overflow: "auto", padding: 10 }}
      >
        <TinyEditor
          minimal
          noFocus
          value={editorValue}
          onEditorChange={handleEditorChange}
        />
      </div>
    );
  }),
  "error.generic"
);
