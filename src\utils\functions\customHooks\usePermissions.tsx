import {
  GET_ALL_PERMISSIONS,
  GET_PERMISSIONS_ACTIONS,
} from "../../../constants";
import { useQueryClient } from "react-query";
import { IPermissionActions } from "../../../interfaces";

const usePermissions = () => {
  const queryClient = useQueryClient();
  const permissionActions = queryClient.getQueryData(
    GET_PERMISSIONS_ACTIONS
  ) as IPermissionActions[];

  const allPermissions = queryClient.getQueryData(GET_ALL_PERMISSIONS) as any;

  const getGlobalPermissions = (globalPermissionsId) => {
    let globalPermissions = [];

    if (globalPermissionsId) {
      globalPermissions = permissionActions
        ?.filter(
          (permission) => (globalPermissionsId & permission.bitMask) !== 0
        )
        .map((permission) => permission.code);
    }

    return [...(globalPermissions || [])];
  };

  const getPermissions = (permissionId) => {
    let globalPermissions = [];
    let localPermissions = [];

    const globalPermissionsId = allPermissions?.global;
    if (globalPermissionsId) {
      globalPermissions = permissionActions
        ?.filter(
          (permission) => (globalPermissionsId & permission.bitMask) !== 0
        )
        .map((permission) => permission.code);
    }

    const permissionValue = allPermissions?.local[permissionId];
    if (permissionValue) {
      localPermissions = permissionActions
        ?.filter((permission) => (permissionValue & permission.bitMask) !== 0)
        .map((permission) => permission.code);
    }
    return [...globalPermissions, ...localPermissions];
  };

  return { getPermissions, getGlobalPermissions };
};

export { usePermissions };
