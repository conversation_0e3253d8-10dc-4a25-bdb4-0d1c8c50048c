import { EditOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { <PERSON><PERSON>, Divider, Select } from "antd";
import { useEffect, useState } from "react";
import { BreadCrumb, Input } from "../../components";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setBreadcrumb, setParentBreadcrumbs } from "../../store/features";
import { useTheme } from "../../utils";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNotification } from "../../utils/functions/customHooks";
import { useMutation } from "react-query";
import { changePasswordService } from "../../services/login";
import i18next, { changeLanguage } from "i18next";
import { i18n } from "../../utils/i18n";
import { RootState } from "../../store";
import { ReactComponent as DownIcon } from "../../assets/down.svg";

type FormData = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};

const Profile = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [language, setLanguage] = useState(null);
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const languages = useSelector(
    (state: RootState) => state.globalSettings.languages
  );

  const validationSchema = yup
    .object({
      currentPassword: yup.string().required("Required"),
      newPassword: yup
        .string()
        .required("Required")
        .min(8, "Password must be at least 8 characters")
        .max(30, "Password cannot be more than 30 characters"),

      confirmPassword: yup
        .string()
        .required("Required")
        .oneOf([yup.ref("newPassword")], "Passwords must match!"),
    })
    .required();

  useEffect(() => {
    setLanguage(i18next.language);
  }, [i18next.language]);

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
  });

  const mutation = useMutation(changePasswordService, {
    onSuccess: () => {
      showSuccessNotification("Password changed successfully!");
      reset();
    },
    onError: (e: any) => {
      showErrorNotification(e.data?.details);
    },
  });

  const onSubmit = (values) => {
    const payload = {
      current: values.currentPassword,
      _new: values.newPassword,
    };
    mutation.mutate(payload);
  };

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  // change locale
  const handleLanguageChange = (value: string) => {
    setLanguage(value);
    changeLanguage(value);
    i18n.changeLanguage(value);
  };

  return (
    <Wrapper theme={theme}>
      <BreadCrumb />
      {contextHolder}

      <div className="content">
        <section className="language-container">
          <h5>
            <EditOutlined /> {t("Change Language")}
          </h5>

          <Select
            id="language-tour-item"
            value={language}
            onChange={handleLanguageChange}
            options={languages?.map((item) => {
              return { ...item, title: null };
            })}
            suffixIcon={<DownIcon />}
          />
        </section>

        <section>
          <h5>
            <EditOutlined /> {t("Change Password")}
          </h5>
          <Divider />

          <form onSubmit={handleSubmit(onSubmit)}>
            <Input
              control={control}
              name="currentPassword"
              label="Current Password"
              type="password"
              error={errors.currentPassword?.message}
            />

            <Input
              control={control}
              name="newPassword"
              label="New Password"
              type="password"
              error={errors.newPassword?.message}
            />

            <Input
              control={control}
              name="confirmPassword"
              label="Confirm Password"
              type="password"
              error={errors.confirmPassword?.message}
            />
            <Button
              htmlType="submit"
              type="primary"
              loading={mutation.isLoading}
            >
              {t("Change")}
            </Button>
          </form>
        </section>
      </div>
    </Wrapper>
  );
};

export default Profile;

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "Personal profile",
    to: "/settings/profile",
  },
];

const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  display: flex;
  flex-direction: column;

  & .language-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    & h5 {
      margin-bottom: 0px;
    }

    & .ant-select {
      width: 20%;
    }
  }
  & .content {
    overflow: auto;
  }

  & form {
    max-width: 500px;
  }

  & .title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    & h5 {
      margin-bottom: 0px;
    }
    & a {
      color: #1677ff;
      text-decoration: underline;
      font-size: 12px;
      cursor: pointer;
    }
  }
  & section {
    background-color: #fff;
    padding: 16px 20px;
    margin: 20px;
    border-radius: 10px;
  }
  & h5 {
    color: ${({ theme }) => theme.colorPrimary};
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
  }
`;
