import { createSlice } from "@reduxjs/toolkit";

export interface MaskState {
  bottomDrawer: boolean;
  workingVersion: boolean;
  homeSectionMask: string;
  movingMask: boolean;
}

const initialState: MaskState = {
  bottomDrawer: false,
  workingVersion: false,
  homeSectionMask: null,
  movingMask: false,
};

export const maskSlice = createSlice({
  name: "mask",
  initialState,
  reducers: {
    setBottomDrawerMask: (state, action) => {
      state.bottomDrawer = action.payload;
    },
    setWorkingVersionMask: (state, action) => {
      state.workingVersion = action.payload;
    },
    setHomeSectionMask: (state, action) => {
      state.homeSectionMask = action.payload;
    },
    setMovingMask: (state, action) => {
      state.movingMask = action.payload;
    },
  },
});

export const {
  setBottomDrawerMask,
  setWorkingVersionMask,
  setHomeSectionMask,
  setMovingMask,
} = maskSlice.actions;

export default maskSlice.reducer;
