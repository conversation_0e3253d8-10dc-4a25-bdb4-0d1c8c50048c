import { memo } from "react";
import { Skeleton } from "antd";
import { styled } from "@linaria/react";

/**
 * Sidebar Tree Skeleton - matches the tree structure in sidebar
 */
export const SidebarTreeSkeleton = memo(function SidebarTreeSkeleton() {
  return (
    <SidebarTreeWrapper>
      {Array(6)
        .fill(0)
        .map((_, index) => (
          <TreeNode key={`tree-node-${index}`} level={index % 3}>
            <div className="tree-icon">
              <Skeleton.Button
                active
                size="small"
                style={{ width: 16, height: 16, minWidth: 16 }}
              />
            </div>
            <div className="tree-content">
              <Skeleton.Input
                active
                size="small"
                style={{
                  width: `${Math.random() * 40 + 60}%`,
                  height: 16,
                }}
              />
            </div>
          </TreeNode>
        ))}
    </SidebarTreeWrapper>
  );
});

/**
 * Attribute List Skeleton - matches the attribute layout in workspace
 */
export const AttributeListSkeleton = memo(function AttributeListSkeleton() {
  return (
    <AttributeListWrapper>
      {Array(3)
        .fill(0)
        .map((_, index) => (
          <AttributeRow key={`attr-${index}`}>
            <div className="attribute-label">
              <Skeleton.Input
                active
                size="small"
                style={{ width: "70%", height: 14 }}
              />
            </div>
            <div className="attribute-value">
              <Skeleton.Input
                active
                size="small"
                style={{ width: "85%", height: 14 }}
              />
            </div>
          </AttributeRow>
        ))}
    </AttributeListWrapper>
  );
});

/**
 * Content Area Skeleton - general purpose content skeleton
 */
export const ContentAreaSkeleton = memo(function ContentAreaSkeleton() {
  return (
    <ContentAreaWrapper>
      <div className="content-header">
        <Skeleton.Input
          active
          size="large"
          style={{ width: "40%", height: 24 }}
        />
      </div>
      <div className="content-body">
        <Skeleton active paragraph={{ rows: 3 }} />
      </div>
    </ContentAreaWrapper>
  );
});

/**
 * Icon Grid Skeleton - matches the icon selection grid
 */
export const IconGridSkeleton = memo(function IconGridSkeleton() {
  return (
    <IconGridWrapper>
      {Array(8)
        .fill(0)
        .map((_, index) => (
          <div key={`icon-${index}`} className="icon-placeholder">
            <Skeleton.Button
              active
              size="small"
              style={{ width: 32, height: 32, minWidth: 32 }}
            />
          </div>
        ))}
    </IconGridWrapper>
  );
});

/**
 * Chart Skeleton - matches chart loading layout
 */
export const ChartSkeleton = memo(function ChartSkeleton() {
  return (
    <ChartWrapper>
      <div className="chart-header">
        <Skeleton.Input
          active
          size="small"
          style={{ width: "30%", height: 20 }}
        />
      </div>
      <div className="chart-body">
        <Skeleton.Button active style={{ width: "100%", height: 200 }} />
      </div>
    </ChartWrapper>
  );
});

/**
 * Kanban/List View Skeleton - matches DQM component layout
 */
export const KanbanListSkeleton = memo(function KanbanListSkeleton() {
  return (
    <KanbanListWrapper>
      {Array(6)
        .fill(0)
        .map((_, index) => (
          <div key={`card-${index}`} className="card-skeleton">
            <div className="card-header">
              <Skeleton.Input
                active
                size="small"
                style={{ width: "70%", height: 16 }}
              />
            </div>
            <div className="card-content">
              <Skeleton active paragraph={{ rows: 2 }} />
            </div>
          </div>
        ))}
    </KanbanListWrapper>
  );
});

/**
 * Small Inline Skeleton - for small loading states like target location
 */
export const InlineSkeleton = memo(function InlineSkeleton() {
  return (
    <Skeleton.Input active size="small" style={{ width: 80, height: 16 }} />
  );
});

/**
 * Node Position Loader - minimalistic loader for graph node actions
 */
export const NodePositionLoader = memo(function NodePositionLoader({
  x,
  y,
  visible,
  primaryColor = "#4377A2",
}: {
  x: number;
  y: number;
  visible: boolean;
  primaryColor?: string;
}) {
  if (!visible) return null;

  return (
    <>
      {/* Overlay to block interactions */}
      <NodeLoaderOverlay />
      {/* Loader at node position */}
      <NodeLoaderWrapper style={{ left: x, top: y }}>
        <div
          className="loader-circle"
          style={{ borderColor: `${primaryColor}30` }}
        >
          <div
            className="spinner"
            style={{ borderTopColor: primaryColor }}
          ></div>
        </div>
        <div
          className="loader-pulse"
          style={{ backgroundColor: `${primaryColor}33` }}
        ></div>
      </NodeLoaderWrapper>
    </>
  );
});

/**
 * Page Table Skeleton - for full page table loading (like MyComments, Messages)
 */
export const PageTableSkeleton = memo(function PageTableSkeleton() {
  return (
    <PageTableWrapper>
      <div className="table-header">
        <Skeleton.Input
          active
          size="small"
          style={{ width: "20%", height: 20 }}
        />
      </div>
      <div className="table-content">
        {Array(8)
          .fill(0)
          .map((_, index) => (
            <div key={`table-row-${index}`} className="table-row">
              <div className="table-cell">
                <Skeleton.Button
                  active
                  size="small"
                  style={{ width: 16, height: 16 }}
                />
              </div>
              <div className="table-cell">
                <Skeleton.Input
                  active
                  size="small"
                  style={{ width: "90%", height: 16 }}
                />
              </div>
              <div className="table-cell">
                <Skeleton.Input
                  active
                  size="small"
                  style={{ width: "75%", height: 16 }}
                />
              </div>
              <div className="table-cell">
                <Skeleton.Input
                  active
                  size="small"
                  style={{ width: "60%", height: 16 }}
                />
              </div>
            </div>
          ))}
      </div>
    </PageTableWrapper>
  );
});

/**
 * Graph Skeleton - matches cytoscape graph with nodes and edges
 */
export const GraphSkeleton = memo(function GraphSkeleton({
  opacity = 1,
}: {
  opacity?: number;
}) {
  return (
    <GraphWrapper style={{ opacity }}>
      <svg width="100%" height="100%" viewBox="0 0 800 600">
        {/* Background */}
        <rect width="100%" height="100%" fill="#fafafa" />

        {/* Nodes */}
        <circle
          cx="150"
          cy="150"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="350"
          cy="100"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="550"
          cy="180"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="250"
          cy="300"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="450"
          cy="350"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="650"
          cy="280"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="100"
          cy="400"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />
        <circle
          cx="400"
          cy="480"
          r="20"
          fill="#e0e0e0"
          className="skeleton-node"
        />

        {/* Edges */}
        <line
          x1="150"
          y1="150"
          x2="350"
          y2="100"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="350"
          y1="100"
          x2="550"
          y2="180"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="150"
          y1="150"
          x2="250"
          y2="300"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="250"
          y1="300"
          x2="450"
          y2="350"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="550"
          y1="180"
          x2="650"
          y2="280"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="250"
          y1="300"
          x2="100"
          y2="400"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />
        <line
          x1="450"
          y1="350"
          x2="400"
          y2="480"
          stroke="#d0d0d0"
          strokeWidth="2"
          className="skeleton-edge"
        />

        {/* Node labels */}
        <rect
          x="125"
          y="185"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="325"
          y="135"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="525"
          y="215"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="225"
          y="335"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="425"
          y="385"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="625"
          y="315"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="75"
          y="435"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
        <rect
          x="375"
          y="515"
          width="50"
          height="8"
          fill="#f0f0f0"
          rx="2"
          className="skeleton-label"
        />
      </svg>
    </GraphWrapper>
  );
});

/**
 * Homepage Cards Skeleton - matches the card grid layout in homepage
 */
export const HomePageCardsSkeleton = memo(function HomePageCardsSkeleton() {
  return (
    <HomePageCardsWrapper>
      {Array(12)
        .fill(0)
        .map((_, index) => (
          <div key={`home-card-${index}`} className="home-card-skeleton">
            <div className="card-icon">
              <Skeleton.Button
                active
                size="large"
                style={{ width: 48, height: 48, minWidth: 48 }}
              />
            </div>
            <div className="card-title">
              <Skeleton.Input
                active
                size="small"
                style={{ width: "80%", height: 16 }}
              />
            </div>
          </div>
        ))}
    </HomePageCardsWrapper>
  );
});

/**
 * Details Page Skeleton - matches the details page layout with sidebar and content
 */
export const DetailsPageSkeleton = memo(function DetailsPageSkeleton() {
  return (
    <DetailsPageWrapper>
      <div className="details-sidebar">
        <div className="sidebar-header">
          <Skeleton.Input
            active
            size="small"
            style={{ width: "60%", height: 20 }}
          />
        </div>
        <div className="sidebar-content">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <div key={`sidebar-item-${index}`} className="sidebar-item">
                <Skeleton.Button
                  active
                  size="small"
                  style={{ width: 16, height: 16, minWidth: 16 }}
                />
                <Skeleton.Input
                  active
                  size="small"
                  style={{ width: "70%", height: 14 }}
                />
              </div>
            ))}
        </div>
      </div>
      <div className="details-content">
        <div className="content-header">
          <Skeleton.Input
            active
            size="large"
            style={{ width: "40%", height: 24 }}
          />
        </div>
        <div className="content-body">
          <AttributeListSkeleton />
        </div>
      </div>
    </DetailsPageWrapper>
  );
});

/**
 * TinyEditor Skeleton - matches the TinyMCE/HugeRTE editor layout
 */
export const TinyEditorSkeleton = memo(function TinyEditorSkeleton({
  minimal = false,
  height = 100,
}: {
  minimal?: boolean;
  height?: number;
}) {
  return (
    <TinyEditorWrapper style={{ height: height + 20 }}>
      {/* Editor Toolbar */}
      <div className="editor-toolbar">
        <div className="toolbar-group">
          {Array(minimal ? 3 : 6)
            .fill(0)
            .map((_, index) => (
              <Skeleton.Button
                key={`toolbar-${index}`}
                active
                size="small"
                style={{
                  width: 35,
                  height: 15,
                  minWidth: 35,
                  marginRight: 7,
                  borderRadius: 5,
                }}
              />
            ))}
        </div>
        {!minimal && (
          <div className="toolbar-group">
            {Array(4)
              .fill(0)
              .map((_, index) => (
                <Skeleton.Button
                  key={`toolbar-2-${index}`}
                  active
                  size="small"
                  style={{
                    width: 35,
                    height: 15,
                    minWidth: 35,
                    marginRight: 7,
                    borderRadius: 5,
                  }}
                />
              ))}
          </div>
        )}
      </div>

      {/* Editor Content Area */}
      <div className="editor-content" style={{ height }}>
        <div className="content-lines">
          {Array(5)
            .fill(0)
            .map((_, index) => (
              <Skeleton.Input
                key={`content-${index}`}
                active
                size="small"
                style={{
                  width: `${Math.random() * 30 + 60}%`,
                  height: 8,
                  marginBottom: 10,
                }}
              />
            ))}
        </div>
      </div>

      {/* Editor Status Bar */}
      <div className="editor-status">
        <Skeleton.Input
          active
          size="small"
          style={{ width: "15%", height: 12 }}
        />
      </div>
    </TinyEditorWrapper>
  );
});

/**
 * Action Dialog Skeleton - for modal dialogs with action loading
 */
export const ActionDialogSkeleton = memo(function ActionDialogSkeleton() {
  return (
    <ActionDialogWrapper>
      <div className="dialog-content">
        <Skeleton.Button
          active
          size="large"
          style={{ width: 48, height: 48, minWidth: 48, borderRadius: "50%" }}
        />
        <div className="dialog-text">
          <Skeleton.Input
            active
            size="small"
            style={{ width: "80%", height: 16, marginTop: 12 }}
          />
        </div>
      </div>
    </ActionDialogWrapper>
  );
});

// Styled components
const SidebarTreeWrapper = styled.div`
  padding: 12px;
  min-width: 250px;
`;

const TreeNode = styled.div<{ level: number }>`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  margin-left: ${(props) => props.level * 16}px;

  .tree-icon {
    margin-right: 8px;
  }

  .tree-content {
    flex: 1;
  }
`;

const AttributeListWrapper = styled.div`
  padding: 12px 16px;
`;

const AttributeRow = styled.div`
  display: flex;
  align-items: stretch;
  width: 100%;
  gap: 6px;
  background: #fff;
  border: 0.5px solid #eaeaea;
  border-radius: 4px;
  margin-bottom: 6px;
  min-height: 32px;

  .attribute-label {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-right: 1px solid #eaeaea;
    padding: 6px;
    width: calc(50% - 3px);
    max-width: 80%;
  }

  .attribute-value {
    display: flex;
    align-items: center;
    padding: 6px 6px 6px 0px;
    flex: 1;
  }
`;

const ContentAreaWrapper = styled.div`
  padding: 20px;

  .content-header {
    margin-bottom: 16px;
  }

  .content-body {
    margin-top: 16px;
  }
`;

const IconGridWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  padding: 16px;

  .icon-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
  }
`;

const ChartWrapper = styled.div`
  padding: 16px;

  .chart-header {
    margin-bottom: 12px;
  }

  .chart-body {
    margin-top: 12px;
  }
`;

const KanbanListWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  padding: 20px;

  .card-skeleton {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    background: #fff;

    .card-header {
      margin-bottom: 12px;
    }

    .card-content {
      margin-top: 8px;
    }
  }
`;

const PageTableWrapper = styled.div`
  padding: 16px;
  background: #fff;

  .table-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .table-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .table-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;

    &:nth-child(even) {
      background-color: #fafafa;
    }
  }

  .table-cell {
    flex: 1;
    padding: 0 8px;

    &:first-child {
      flex: 0 0 40px;
      text-align: center;
    }
  }
`;

const GraphWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;

  svg {
    max-width: 100%;
    max-height: 100%;
  }

  .skeleton-node {
    animation: pulse 2s infinite;
  }

  .skeleton-edge {
    animation: pulse 2s infinite;
    animation-delay: 0.5s;
  }

  .skeleton-label {
    animation: pulse 2s infinite;
    animation-delay: 1s;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const HomePageCardsWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;

  .home-card-skeleton {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background: #fff;
    height: 150px;

    .card-icon {
      margin-bottom: 12px;
    }

    .card-title {
      text-align: center;
    }
  }
`;

const DetailsPageWrapper = styled.div`
  display: flex;
  height: 100%;

  .details-sidebar {
    width: 300px;
    background: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    padding: 16px;

    .sidebar-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e0e0e0;
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .sidebar-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 0;
    }
  }

  .details-content {
    flex: 1;
    padding: 16px;

    .content-header {
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .content-body {
      flex: 1;
    }
  }
`;

const NodeLoaderOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.02);
  z-index: 999;
`;

const NodeLoaderWrapper = styled.div`
  position: absolute;
  z-index: 1000;
  pointer-events: none;
  transform: translate(-50%, -50%);

  .loader-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .loader-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.7;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.5);
      opacity: 0.3;
    }
  }
`;

const TinyEditorWrapper = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #ffffff;
  overflow: hidden;

  .editor-toolbar {
    background: #f5f5f5;
    border-bottom: 1px solid #d9d9d9;
    padding: 8px 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 2px;
    }
  }

  .editor-content {
    background: #ffffff;
    padding: 12px;
    overflow: hidden;

    .content-lines {
      display: flex;
      flex-direction: column;
    }
  }

  .editor-status {
    background: #f5f5f5;
    border-top: 1px solid #d9d9d9;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    height: 24px;
  }
`;

const ActionDialogWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  min-height: 120px;

  .dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .dialog-text {
    margin-top: 12px;
  }
`;
