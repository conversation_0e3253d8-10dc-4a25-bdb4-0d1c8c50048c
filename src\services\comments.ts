import { COMMENT_TEMPLATE_ID } from "../constants";
import { IRelations, ITreeData } from "../interfaces";
import { API } from "../utils/api";

export const getComments = async (id): Promise<any[]> => {
  const response = (await API.post(`/model/node/get`, {
    parentIds: [id],
    templateIds: [COMMENT_TEMPLATE_ID],
    addBody: true,
  })) as ITreeData[];
  return response?.sort((a, b) => a.visualOrder - b.visualOrder);
};

export const getAllComments = async (id: string): Promise<IRelations[]> => {
  const response = (await API.get(
    `/model/node/${id}/related-nodes/get?permissions=FALSE`
  )) as IRelations[];
  return response?.filter((res) => res.nodeTemplateId === COMMENT_TEMPLATE_ID);
};

// export const getAllComments = async (): Promise<any[]> => {
//   const response = (await API.post(`/model/node/get`, {
//     templateIds: [COMMENT_TEMPLATE_ID],
//     addBody: true,
//   })) as ITreeData[];
//   return response?.sort((a, b) => a.visualOrder - b.visualOrder);
// };
