import { styled } from "@linaria/react";
import { notification, Radio, RadioChangeEvent } from "antd";
import { BreadCrumb } from "../../components";
import Theme1Icon from "../../assets/images/homeTheme1.png";
import Theme2Icon from "../../assets/images/homeTheme2.png";
import Theme3Icon from "../../assets/images/homeTheme3.png";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setHomeVersion } from "../../store/features/localSettings";
import { RootState } from "../../store";
import { useEffect } from "react";
import { setBreadcrumb, setParentBreadcrumbs } from "../../store/features";

const ChangeHomeLayout = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const homeVersion = useSelector(
    (root: RootState) => root.localSettings.homeVersion
  );

  const onChange = (e: RadioChangeEvent) => {
    dispatch(setHomeVersion(e.target.value));
    setTimeout(() => {
      notification.success({
        message: t("Home layout updated successfully!"),
      });
    }, 500);
  };

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  return (
    <Wrapper>
      <BreadCrumb />
      <Grid>
        <Radio.Group onChange={onChange} defaultValue={homeVersion}>
          <Radio value={"v2"}>
            <img src={Theme3Icon} />
            <p>{t("Default")}</p>
          </Radio>
          <Radio value={"v1"}>
            <div>
              <img src={Theme1Icon} />
              <p>{t("Shortcuts")}</p>
            </div>
          </Radio>
          <Radio value={"v3"}>
            <img src={Theme2Icon} />
            <p>{t("Basic")}</p>
          </Radio>
        </Radio.Group>
      </Grid>
    </Wrapper>
  );
};

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "Change home layout",
    to: "/settings/home-layout",
  },
];

export default ChangeHomeLayout;

const Grid = styled.div`
  margin-top: 30px;
  margin-left: 20px;
  margin-right: 20px;

  & .ant-radio-wrapper {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
  }

  & p {
    color: #094375;
    font-size: 13px;
    margin-top: 6px;
  }

  & img {
    border: 2px solid #eee;
    width: 100%;
  }

  & > .ant-radio-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
  }

  & a {
    text-decoration: none;
  }

  & label {
    display: flex;
    align-items: center;

    & > span:last-child {
      width: 100%;
    }
  }
`;

const Wrapper = styled.div`
  flex: 1;
`;
