import { <PERSON><PERSON>, Dropdown, Flex } from "antd";
import { useTheme } from "../../utils/useTheme";
import { Suspense, useCallback, useEffect, useState } from "react";
import { Wrapper } from "../HistoryPage/style";
import {
  BreadCrumb,
  DetailsContainer,
  MyTable,
  SimpleAttributeValue,
} from "../../components";
import { TableLoadingSkeleton } from "../../components/organisms/MyTable/TableLoadingSkeleton";
import {
  GET_COMMENTS,
  NODES_MENU_ITEMS,
  COMMENT_TEMPLATE_ID,
  TRASH_NODES_MENU_ITEMS,
  COMMENT_AUTHOR_ID,
  COMMENT_RATING_ID,
  GET_LOCAL_SETTINGS_KEY,
} from "../../constants";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  useHyperlinkActions,
  useNotification,
} from "../../utils/functions/customHooks";
import {
  DELETED_FLAG,
  ILocalSettings,
  INodeDetails,
  IRelations,
} from "../../interfaces";
import { getAllComments, saveLocalSettings } from "../../services";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";
import { getParentID, transformObjectPath } from "../../utils";
import { getAllCommentsDetails, getAllNodeDetails } from "../../services/node";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../components/withErrorBoundary";

const MyComments = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  const userInfo = useSelector((root: RootState) => root.auth.userInfo);

  const queryClient = useQueryClient();

  const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [loading, setLoading] = useState(true);
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [displayedColumns, setDisplayedColumns] = useState([]);

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(breadcrumb));
  }, []);

  const { data, isLoading, isError } = useQuery(GET_COMMENTS, () =>
    getAllComments(userInfo?.id)
  );

  const mask = useSelector((state: RootState) => state.sidebar.mask);
  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      columns?.length > 0 &&
      localSettingsData?.body[0]?.value?.myCommentsTable &&
      localSettingsData?.body[0]?.value?.myCommentsTable?.columns.length > 0
    ) {
      if (localSettingsData?.body[0]?.value?.myCommentsTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.myCommentsTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.myCommentsTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.myCommentsTable.columns?.forEach(
          (column) => {
            const index = columns.findIndex((item) => item.field === column);
            allColumns.push({
              ...columns[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.myCommentsTable?.columns
        );
        setPinned(localSettingsData?.body[0]?.value?.myCommentsTable?.pinned);
        setSort(localSettingsData?.body[0]?.value?.myCommentsTable?.sort);
        setFilters(localSettingsData?.body[0]?.value?.myCommentsTable?.filters);
        setDisplayedColumns(allColumns);
      }
    } else {
      setDisplayedColumns(columns);
      setColumnsRequest(columns?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger, columns]);

  useEffect(() => {
    if (!data) {
      return;
    }

    getParentDetails();
  }, [data]);

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        const parentID = await getParentID(id);

        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const generateRowsAndColumns = (
    allComments: INodeDetails[],
    allParents: INodeDetails[]
  ) => {
    const _columns = [];

    const _rows = [];
    const selectedTemplateAttributes =
      templatesData[COMMENT_TEMPLATE_ID]?.attributeTemplates || [];
    selectedTemplateAttributes?.forEach((attribute) => {
      if (attribute?.id !== COMMENT_AUTHOR_ID)
        _columns.push({
          headerName: attribute.name,
          field: attribute.id?.toString(),
          width: attribute?.id === COMMENT_RATING_ID ? 130 : 250,
          cellRenderer: ({ data }) => {
            const isRating = attribute?.id === COMMENT_RATING_ID;
            const ratingValue = isRating
              ? (Object.values(data[attribute.name])?.[0] as string)
              : "";
            const isEmptyRating = ratingValue?.trim() === "-";

            if (isRating && isEmptyRating) {
              return <div style={{ textAlign: "center" }}>{ratingValue}</div>;
            }

            return isRating && !isEmptyRating ? (
              <div
                className={`comment-rating ${
                  Object.values(data[attribute.name])[0] === "Negatywna"
                    ? "negative-rating"
                    : "positive-rating"
                }`}
              />
            ) : (
              <SimpleAttributeValue
                key={attribute.id}
                attributeType={attribute.type}
                attributeValue={data[attribute.name] || "-"}
              />
            );
          },
        });
    });

    _columns.push(
      {
        headerName: "Commented in",
        field: "nodeName",
        minWidth: 170,
        flex: 1,
        cellRenderer: (event) => {
          const record = event?.data;
          return (
            <Dropdown
              menu={{
                items: record.inTrash
                  ? TRASH_NODES_MENU_ITEMS
                  : NODES_MENU_ITEMS,
                onClick: (e) =>
                  handleNodeClick(e.key, record.nodeId, record.nodeName),
              }}
              trigger={["contextMenu"]}
            >
              <a
                className="title-container"
                onClick={async (e) => {
                  e.stopPropagation();
                  handleHyperlinkAction({
                    id: record.nodeId,
                    inTrash: record.inTrash,
                  });
                }}
              >
                <p className={` ${record.inTrash ? "trash-hyperlink" : ""}`}>
                  {record.nodeName}
                </p>
              </a>
            </Dropdown>
          );
        },
      },

      {
        headerName: "Path",
        field: "nodePath",
        minWidth: 240,
        flex: 1,
        cellRenderer: ({ data }) => (
          <p className="right-align">
            {data?.nodePath
              ? transformObjectPath(data?.nodePath, data.inTrash)
              : "-"}
          </p>
        ),
      }
    );

    setColumns(_columns);

    allComments?.forEach((node: INodeDetails) => {
      const row = {};
      row["id"] = node.id;
      node?.body?.forEach((attribute) => {
        if (attribute?.id !== COMMENT_AUTHOR_ID) {
          row[attribute?.name] = attribute?.value || "-";
          row[attribute.id?.toString()] = attribute?.value || "-";
        }
      });
      const parentNode = allParents?.find(
        (parent) => parent.id === node?.parentId
      );

      row["nodeName"] = parentNode?.name;
      row["nodeId"] = parentNode?.id;
      row["inTrash"] = parentNode?.flag?.includes(DELETED_FLAG);
      row["nodePath"] = parentNode?.pathName;

      _rows.push(row);
    });

    // By Deafault, the rows are sorted by date in decending order.
    const dateAttr = selectedTemplateAttributes.find(
      (attr) => attr.type === "date" || attr.type === "dateTime"
    );
    if (dateAttr) {
      const dateKey = dateAttr.id?.toString();
      _rows.sort((a, b) => {
        const dateA = new Date(a[dateKey] ?? "").getTime();
        const dateB = new Date(b[dateKey] ?? "").getTime();
        return dateB - dateA;
      });
    }

    setRows(_rows);

    setLoading(false);
  };

  const getParentDetails = async () => {
    const parentIds = [];
    const nodeIds = [];
    data?.forEach((node: IRelations) => {
      if (!nodeIds.includes(node.nodeId)) {
        nodeIds.push(node.nodeId);
      }
    });

    const allCommentsDetails = await getAllCommentsDetails(nodeIds);

    allCommentsDetails?.forEach((node) => {
      if (!parentIds.includes(node?.parentId)) {
        parentIds.push(node.parentId);
      }
    });

    const allParentDetails = await getAllNodeDetails(parentIds?.join());

    generateRowsAndColumns(allCommentsDetails, allParentDetails);
  };

  const detectChange = useCallback(() => {
    if (!mask) {
      dispatch(setMask(true));
    }
  }, [mask]);

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        myCommentsTable: {
          columns: columnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setMask(false));
    }, 200);
  };

  return (
    <Suspense fallback={null}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10}>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    type="primary"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    type="primary"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />

        {loading ? (
          <TableLoadingSkeleton />
        ) : (
          <div
            className="content"
            style={{ border: mask ? "1px solid red" : "none" }}
          >
            <MyTable
              isError={isError}
              loading={loading || isLoading}
              height={"calc(100vh - 165px)"}
              data={rows}
              columns={displayedColumns}
              emptyMessage="No comments"
              excelFileName="mycomments"
              detectChange={detectChange}
              resetTrigger={resetTrigger}
              setPinned={setPinned}
              setColumnsRequest={setColumnsRequest}
              setFilters={setFilters}
              setSort={setSort}
              initialFilters={
                localSettingsData?.body[0]?.value?.myCommentsTable?.filters ||
                {}
              }
            />
          </div>
        )}

        {!!isDetailsOpen && (
          <DetailsContainer
            id={isDetailsOpen.id}
            isOpen={!!isDetailsOpen}
            onClose={() => setDetailsOpen(null)}
            title={isDetailsOpen.name}
          />
        )}
      </Wrapper>
    </Suspense>
  );
};

export default withErrorBoundary(MyComments, "error.generic");

const breadcrumb = [
  {
    title: "My Comments",
    to: "/comments",
    mockup: true,
  },
];

// const TitleWrapper = styled.div`
//   display: flex;
//   cursor: pointer;
// `;

// const COLUMNS = [
//   {
//     label: "Title",
//     key: "title",
//     width: 220,
//     render: (value) => (
//       <Dropdown
//         trigger={["contextMenu"]}
//         menu={{
//           items: [
//             {
//               key: "edit",
//               label: "Edit",
//               icon: <EditOutlined />,
//             },
//           ],
//         }}
//       >
//         <TitleWrapper>{value.title}</TitleWrapper>
//       </Dropdown>
//     ),
//   },
//   {
//     label: "Description",
//     key: "description",
//     width: 250,
//     render: (value) => (
//       <Typography.Paragraph
//         ellipsis={{ rows: 2, expandable: true, symbol: "more" }}
//       >
//         {value.description}
//       </Typography.Paragraph>
//     ),
//   },
//   {
//     label: "Date",
//     key: "date",
//     isDate: true,
//     width: 200,
//     render: (rowData) => {
//       return dayjs(rowData?.date).format("YYYY/MM/DD HH:mm");
//     },
//   },
//   {
//     label: "Author",
//     key: "user",
//     width: 200,
//     render: (value) => (
//       <>
//         <Avatar size={32} src={value.image} /> {value.user}
//       </>
//     ),
//   },
// ];
