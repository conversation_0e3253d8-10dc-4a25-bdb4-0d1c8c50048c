import { styled } from "@linaria/react";
import { Checkbox, Radio, Select, Tabs } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Hourly } from "./Hourly";
import dayjs from "dayjs";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

const CustomCronBase = ({
  setCronExpression,
  startAt,
  activeHeader,
  setActiveHeader,
  defaultValue,
}) => {
  const { t } = useTranslation();
  const [minute, setMinute] = useState("0");
  const [hour, setHour] = useState("0");
  const [hourMinutes, setHourMinutes] = useState("0");
  const [selectedDailyOption, setSelectedDailyOption] = useState(null);
  const [selectedMonthlyOption, setSelectedMonthlyOption] = useState(null);
  const [day, setDay] = useState("0");
  const [monthOptions, setMonthOptions] = useState({
    xDayOfMonth: "1",
    xDaysBeforeEnd: "1",
    daysOfEveryMonth: [],
  });
  const [weekly, setWeekly] = useState([]);
  const [history, setHistory] = useState({});

  useEffect(() => {
    if (defaultValue) {
      setHistory({ ...history, [activeHeader]: defaultValue });

      if (activeHeader === "WEEKLY") {
        const weekValue = defaultValue?.split(" ")[4] || "";
        setWeekly(weekValue.split(",").filter(Boolean));
      }
    }
  }, [defaultValue, activeHeader, history]);

  const OPTIONS = [
    {
      key: "MINUTES",
      label: t("Minutes"),
      children: (
        <label>
          {t("Every")}
          <input
            type="number"
            value={minute}
            onChange={(e) => {
              setMinute(e.target.value);
              setCronExpression(`*/${e.target.value} * * * *`);
              setHistory({
                ...history,
                MINUTES: `*/${e.target.value} * * * *`,
              });
            }}
            min="1"
          />
          {t("minute(s)")}
        </label>
      ),
    },
    {
      key: "HOURLY",
      label: t("Hourly"),
      children: (
        <Hourly
          hour={hour}
          minute={hourMinutes}
          onHourChange={(e) => {
            setHour(e.target.value);
            setCronExpression(
              `${hourMinutes || "0"} */${e.target.value} * * *`
            );
            setHistory({
              ...history,
              HOURLY: `${hourMinutes || "0"} */${e.target.value} * * *`,
            });
          }}
          onMinuteChange={(e) => {
            setHourMinutes(e.target.value);
            setCronExpression(`${e.target.value} */${hour} * * *`);
            setHistory({
              ...history,
              HOURLY: `${e.target.value} */${hour} * * *`,
            });
          }}
        />
      ),
    },
    {
      key: "DAILY",
      label: t("Daily"),
      children: (
        <Radio.Group
          onChange={(e) => {
            let startDate = ["0", "0"];
            if (startAt) {
              startDate = dayjs(startAt).format("HH:mm").split(":");
            }
            let cron = "";
            if (e.target.value === "daily") {
              cron = `${startDate[1]} ${startDate[0]} */${day} * *`;
            } else {
              cron = `${startDate[1]} ${startDate[0]} * * 1-5`;
            }
            setCronExpression(cron);
            setHistory({ ...history, DAILY: cron });
            setSelectedDailyOption(e.target.value);
          }}
          value={selectedDailyOption}
          options={[
            {
              value: "daily",
              label: (
                <label>
                  {t("Every")}
                  <input
                    type="number"
                    min="1"
                    value={day}
                    onChange={(e) => {
                      let startDate = ["0", "0"];
                      if (startAt) {
                        startDate = dayjs(startAt).format("HH:mm").split(":");
                      }
                      if (selectedDailyOption !== "daily") {
                        setSelectedDailyOption("daily");
                      }
                      setDay(e.target.value);
                      setCronExpression(
                        `${startDate[1]} ${startDate[0]} */${e.target.value} * *`
                      );
                      setHistory({
                        ...history,
                        DAILY: `${startDate[1]} ${startDate[0]} */${e.target.value} * *`,
                      });
                    }}
                  />
                  {t("day(s)")}
                </label>
              ),
            },
            {
              value: "every-weekday",
              label: t("Every weekday"),
            },
          ]}
        />
      ),
    },
    {
      key: "MONTHLY",
      label: t("Monthly"),
      children: (
        <div className="options">
          <Radio.Group
            value={selectedMonthlyOption}
            options={[
              {
                value: "day-x-of-month",
                label: (
                  <label>
                    {t("Day")}
                    <input
                      type="number"
                      min="1"
                      value={monthOptions.xDayOfMonth}
                      onChange={(e) => {
                        if (selectedMonthlyOption !== "day-x-of-month") {
                          setSelectedMonthlyOption("day-x-of-month");
                        }
                        setDay(e.target.value);
                        let startDate = ["0", "0"];
                        if (startAt) {
                          startDate = dayjs(startAt).format("HH:mm").split(":");
                        }
                        setMonthOptions({
                          ...monthOptions,
                          xDayOfMonth: e.target.value,
                        });
                        setCronExpression(
                          `${startDate[1]} ${startDate[0]} ${e.target.value} * *`
                        );
                        setHistory({
                          ...history,
                          MONTHLY: `${startDate[1]} ${startDate[0]} ${e.target.value} * *`,
                        });
                      }}
                    />
                    {t("of every month")}
                  </label>
                ),
              },
              {
                value: "last-day-of-month",
                label: t("Last day of every month"),
              },
              {
                value: "last-weekend-of-every-month",
                label: t("On the last weekday of every month"),
              },
              {
                value: "days-before-end-of-month",
                label: (
                  <label>
                    <input
                      type="number"
                      min="1"
                      value={monthOptions.xDaysBeforeEnd}
                      onChange={(e) => {
                        if (
                          selectedMonthlyOption !== "days-before-end-of-month"
                        ) {
                          setSelectedMonthlyOption("days-before-end-of-month");
                        }
                        setMonthOptions({
                          ...monthOptions,
                          xDaysBeforeEnd: e.target.value,
                        });
                        let startDate = ["0", "0"];
                        if (startAt) {
                          startDate = dayjs(startAt).format("HH:mm").split(":");
                        }
                        setCronExpression(
                          `${startDate[1]} ${startDate[0]} L-${e.target.value} * *`
                        );
                        setHistory({
                          ...history,
                          MONTHLY: `${startDate[1]} ${startDate[0]} L-${e.target.value} * *`,
                        });
                      }}
                    />
                    {t("days before the end of the month")}
                  </label>
                ),
              },
              {
                value: "days-of-every-month",

                label: (
                  <label>
                    <Select
                      mode="multiple"
                      value={monthOptions.daysOfEveryMonth}
                      onChange={(value: string[]) => {
                        if (selectedMonthlyOption !== "days-of-every-month") {
                          setSelectedMonthlyOption("days-of-every-month");
                        }
                        setMonthOptions({
                          ...monthOptions,
                          daysOfEveryMonth: value,
                        });

                        if (value.length === 0) {
                          setCronExpression(null);
                          setHistory({ ...history, MONTHLY: null });
                          return;
                        }

                        let startDate = ["0", "0"];
                        if (startAt) {
                          startDate = dayjs(startAt).format("HH:mm").split(":");
                        }
                        setCronExpression(
                          `${startDate[1]} ${startDate[0]} ${value.join()} * *`
                        );
                        setHistory({
                          ...history,
                          MONTHLY: `${startDate[1]} ${
                            startDate[0]
                          } ${value.join()} * *`,
                        });
                      }}
                      options={[...Array(31).keys()].map((day) => {
                        return {
                          value: day + 1,
                          label: (day + 1).toString(),
                        };
                      })}
                    />
                    {t("Days of every month")}
                  </label>
                ),
              },
            ]}
            onChange={(e) => {
              setSelectedMonthlyOption(e.target.value);
              let startDate = ["0", "0"];
              if (startAt) {
                startDate = dayjs(startAt).format("HH:mm").split(":");
              }
              switch (e.target.value) {
                case "last-day-of-month":
                  setCronExpression(`${startDate[1]} ${startDate[0]} L * *`);
                  setHistory({
                    ...history,
                    MONTHLY: `${startDate[1]} ${startDate[0]} L * *`,
                  });
                  return;
                case "day-x-of-month":
                  setCronExpression(
                    `${startDate[1]} ${startDate[0]} ${monthOptions.xDayOfMonth} * *`
                  );
                  setHistory({
                    ...history,
                    MONTHLY: `${startDate[1]} ${startDate[0]} ${monthOptions.xDayOfMonth} * *`,
                  });
                  return;
                case "last-weekend-of-every-month":
                  setCronExpression(`${startDate[1]} ${startDate[0]} LW * *`);
                  setHistory({
                    ...history,
                    MONTHLY: `${startDate[1]} ${startDate[0]} LW * *`,
                  });
                  return;
                case "days-before-end-of-month":
                  setCronExpression(
                    `${startDate[1]} ${startDate[0]} L-${monthOptions.xDaysBeforeEnd} * *`
                  );
                  setHistory({
                    ...history,
                    MONTHLY: `${startDate[1]} ${startDate[0]} L-${monthOptions.xDaysBeforeEnd} * *`,
                  });
                  return;
                case "days-of-every-month":
                  if (monthOptions.daysOfEveryMonth.length > 0)
                    setCronExpression(
                      `${startDate[1]} ${
                        startDate[0]
                      } ${monthOptions.daysOfEveryMonth.join()} * *`
                    );
                  setHistory({
                    ...history,
                    MONTHLY: `${startDate[1]} ${
                      startDate[0]
                    } ${monthOptions.daysOfEveryMonth.join()} * *`,
                  });
              }
            }}
          />
        </div>
      ),
    },
    {
      key: "WEEKLY",
      label: t("Weekly"),
      children: (
        <Checkbox.Group
          onChange={(values) => {
            setWeekly(values);
            let startDate = ["0", "0"];
            if (startAt) {
              startDate = dayjs(startAt).format("HH:mm").split(":");
            }
            if (values.length === 0) {
              setCronExpression(null);
              setHistory({ ...history, WEEKLY: null });
            } else
              setCronExpression(
                `${startDate[1]} ${startDate[0]} * * ${values.join()}`
              );
            setHistory({
              ...history,
              WEEKLY: `${startDate[1]} ${startDate[0]} * * ${values.join()}`,
            });
          }}
          value={weekly}
          options={[
            { value: "1", label: t("Monday") },
            { value: "2", label: t("Tuesday") },
            { value: "3", label: t("Wednesday") },
            { value: "4", label: t("Thursday") },
            { value: "5", label: t("Friday") },
            { value: "6", label: t("Saturday") },
            { value: "0", label: t("Sunday") },
          ]}
        />
      ),
    },
  ];

  const handleChange = (key) => {
    setActiveHeader(key);
    setCronExpression(history[key] || null);
    // switch (key) {
    //   case "MINUTES":
    //     setMinute("");
    //     return;
    //   case "HOURLY":
    //     setMinute("");
    //     setHour("");
    //     return;
    //   case "DAILY":
    //     setSelectedDailyOption(null);
    //     return;
    //   case "MONTHLY":
    //     setSelectedMonthlyOption(null);
    //     return;
    //   case "WEEKLY":
    //     setWeekly([]);
    // }
  };

  return (
    <Wrapper>
      <Tabs activeKey={activeHeader} items={OPTIONS} onChange={handleChange} />
      {/* <h4>
        Generated Cron: <code>{cronExpression}</code>
      </h4>
      <h4>
        Cron:{" "}
        <code>
          {cronExpression &&
            cronstrue.toString(cronExpression, {
              use24HourTimeFormat: true,
            })}
        </code>
      </h4> */}
    </Wrapper>
  );
};

const CustomCron = withErrorBoundary(
  React.memo(CustomCronBase),
  "error.generic"
);

export { CustomCron };

const Wrapper = styled.div`
  padding-bottom: 10px;

  & .ant-radio-group {
    flex-direction: column;
    display: flex;
    gap: 16px;

    & label {
      padding: 0px;
      margin-bottom: 0px !important;
    }
  }

  & .ant-tabs-tab-btn {
    color: var(--color-text);
    font-size: 13px;
  }

  & label {
    color: var(--color-text);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    & > input {
      width: 150px;
    }
  }

  & .ant-select-selector {
    min-height: unset;
  }

  & svg {
    font-size: 11px;
  }
  & .ant-select {
    width: 150px;
    margin-right: 10px;
  }

  & input {
    border-radius: 5px;
    outline: none;
    border: 1px solid #cecece;
    font-size: 13px;
    padding: 2px 0px 2px 8px;
  }
`;
