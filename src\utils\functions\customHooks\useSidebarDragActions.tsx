import { useQueryClient } from "react-query";
import { checkForUniqueness } from "../checkForUniqueness";
import { notification } from "antd";
import { getAllNodes, getNodeDetails } from "../../../services/node";
import { searchRecursivelyBy<PERSON><PERSON> } from "../recursives";
import {
  DISABLED_METAMODEL_NODE_ID,
  GET_ALL_TEMPLATES_KEY,
  GET_HEADER_MENUS,
  GET_NODE_ATTRIBUTES_DETAILS,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
  TEMP_GROUPING_NODE_ID,
  getAttributeIcon,
} from "../../../constants";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { DELETED_FLAG, INodeDetails, ITemplates } from "../../../interfaces";
import { useTranslation } from "react-i18next";
import {
  setBreadcrumb,
  setExpandedKeys,
  setMovePayload,
} from "../../../store/features/sidebar";
import { RootState } from "../../../store";
import { setMovingMask } from "../../../store/features";

export function validateSidebarDrop(
  info: any,
  data: any[],
  templatesData: any,
  nodeId: any,
  metamodel: boolean,
  queryClient: any
): { valid: boolean; message?: string } {
  // const dragKey = info.dragNode.key;
  const dropKey = info.node.key;

  if (info?.dragNode?.flag?.includes(DELETED_FLAG)) {
    return {
      valid: false,
      message:
        "You can’t drag and drop node from the trashcan. Please use node context menu",
    };
  }

  let newParent = null;
  let newParentId = null;
  if (!info.dropToGap) {
    newParent = info.node;
    newParentId = info.node.key;
  } else {
    newParentId = info.node.parentId;

    if (info.node.parentId == nodeId) {
      if (metamodel) {
        // Not needed for validation
      } else {
        // Not needed for validation
      }
    } else {
      // Not needed for validation
    }
  }

  if (
    dropKey === DISABLED_METAMODEL_NODE_ID ||
    newParentId === DISABLED_METAMODEL_NODE_ID ||
    newParent?.flag?.includes(DELETED_FLAG)
  ) {
    return {
      valid: false,
      message:
        "You can’t drag and drop node into the trashcan. Please use node context menu",
    };
  }

  if (info.dragNode.parentId !== newParentId) {
    // Uniqueness check
    const isUnique = checkForUniqueness(
      info.dragNode?.name,
      info.dragNode?.templateId,
      data,
      newParentId
    );
    if (!isUnique) {
      return {
        valid: false,
        message:
          "Node with name already exists! Please try again with different name",
      };
    }

    let parentAllowedChildrens = null;
    let parentTemplate = null;
    if (!newParent) {
      return {
        valid: false,
        message: "Parent node not found",
      };
    }
    if (newParentId == nodeId) {
      parentAllowedChildrens = newParent.allowedChildren?.map(
        (allowedChild) => allowedChild?.id
      );
      parentTemplate = newParent;
    } else {
      const parentTemplateId = newParent?.templateId;
      //Grouping folder
      if (parentTemplateId === TEMP_GROUPING_NODE_ID) {
        const nodeDetails = queryClient.getQueryData([
          GET_NODE_ATTRIBUTES_DETAILS,
          newParent?.key?.toString(),
        ]) as INodeDetails;
        // For validation, if not in cache, treat as invalid
        if (!nodeDetails) {
          return {
            valid: false,
            message: "Parent node details not loaded",
          };
        }
        const allowedChildren = nodeDetails?.body?.find(
          (attr) => attr.type === "allowedChildren"
        )?.value;
        parentAllowedChildrens = allowedChildren?.map(
          (allowedChild) => allowedChild?.id
        );
      } else {
        parentTemplate = templatesData[Number(parentTemplateId)];
        parentAllowedChildrens = parentTemplate?.allowedChildren?.map(
          (allowedChild) => allowedChild?.id
        );
      }
    }

    //const childTemplateType = templatesData[Number(info.dragNode.templateId)];

    if (!parentAllowedChildrens?.includes(info.dragNode.templateId)) {
      return {
        valid: false,
        message: "Move operation restricted! Type mismatch or not allowed.",
      };
    }
  }

  if (info.dragNode?.templateId == TEMPLATES_ATTRIBUTE_TEMPLATE_ID) {
    return {
      valid: false,
      message:
        "Cannot drag attribute templates! Please use draft version funtionality!",
    };
  }

  // If all checks pass
  return { valid: true };
}

const useSidebarDragActions = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const metamodel =
    location.pathname.includes("metamodel") ||
    location.pathname.includes("data-sources");

  const params = useParams();
  const nodeId = params?.nodeId;
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const { selected, movePayload } = useSelector(
    (state: RootState) => state.sidebar
  );
  const expandedKeys = useSelector(
    (state: RootState) => state.sidebar.expandedKeys
  );
  const templatesArray = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  const updateBreadcrumbsForChildren = (data, parentBreadcrumb) => {
    data?.forEach((item) => {
      item.breadcrumb = [...parentBreadcrumb, ...item.breadcrumb.slice(-1)];
      if (item.children && item.children.length > 0) {
        updateBreadcrumbsForChildren(item.children, item.breadcrumb);
      }
    });
  };

  const updateChildren = (treeData, item) => {
    const key = item.key;
    const index = treeData?.findIndex((node) => node.key === key);
    if (index !== -1) {
      treeData[index] = { ...item };
    } else {
      treeData?.forEach((node) => {
        if (node.children?.length > 0) {
          updateChildren(node.children, item);
        }
      });
    }
  };
  const handleSidebarDrag = async (
    info: any,
    data,
    setTreeData,
    tempTreeData,
    setTempTreeData
  ) => {
    const newMovePayload = { ...movePayload };
    const treeData = [...data];
    const tempTreeDataPayload = [...(tempTreeData || [])];
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const dropKey = info.node.key;

    if (info?.dragNode?.flag?.includes(DELETED_FLAG)) {
      notification.open({
        message: t("Move operation restricted!"),
        type: "error",
        description: t(
          "You can’t drag and drop node from the trashcan. Please use node context menu"
        ),
      });
      return;
    }

    // if (dragKey < 0) {
    //   notification.open({
    //     message: t("Error while moving"),
    //     type: "error",
    //     description: t("Cannot drag system nodes"),
    //   });
    //   return;
    // }

    const loop = (data, key, callback) => {
      data.forEach((item, index, arr) => {
        if (item.key === key) {
          return callback({ ...item }, index, arr);
        }
        if (item.children) {
          return loop(item.children, key, callback);
        }
      });
    };

    try {
      let newParent = null;
      let newParentId = null;
      if (!info.dropToGap) {
        newParent = info.node;
        newParentId = info.node.key;
      } else {
        newParentId = info.node.parentId;

        if (info.node.parentId == nodeId) {
          if (metamodel) {
            newParent = searchRecursivelyByKey(
              templatesArray,
              info.node.parentId,
              "id"
            );
          } else {
            const menuData = queryClient.getQueryData(GET_HEADER_MENUS) as any;
            newParent = searchRecursivelyByKey(
              menuData?.menu,
              info.node.parentId,
              "id"
            );
          }
        } else {
          newParent = searchRecursivelyByKey(
            treeData,
            info.node.parentId,
            "key"
          );
          newParentId = info.node.parentId;
        }
      }

      if (
        dropKey === DISABLED_METAMODEL_NODE_ID ||
        newParentId === DISABLED_METAMODEL_NODE_ID ||
        newParent?.flag?.includes(DELETED_FLAG)
      ) {
        notification.open({
          message: t("Move operation restricted!"),
          type: "error",
          description: t(
            "You can’t drag and drop node into the trashcan. Please use node context menu"
          ),
        });
        return;
      }

      if (info.dragNode.parentId !== newParentId) {
        const isUnique = checkForUniqueness(
          info.dragNode?.name,
          info.dragNode?.templateId,
          treeData,
          newParentId
        );

        if (!isUnique) {
          notification.error({
            message: t("Node with name already exists!"),
            description: t("Please try again with different name"),
          });
          return;
        }

        let parentAllowedChildrens = null;
        let parentTemplate = null;
        if (newParentId == nodeId) {
          parentAllowedChildrens = newParent.allowedChildren?.map(
            (allowedChild) => allowedChild?.id
          );
          parentTemplate = newParent;
        } else {
          const parentTemplateId = newParent?.templateId;
          //Grouping folder
          if (parentTemplateId === TEMP_GROUPING_NODE_ID) {
            let nodeDetails = queryClient.getQueryData([
              GET_NODE_ATTRIBUTES_DETAILS,
              newParent?.key?.toString(),
            ]) as INodeDetails;
            if (!nodeDetails) {
              nodeDetails = await getNodeDetails(newParent?.key);
            }
            const allowedChildren = nodeDetails?.body?.find(
              (attr) => attr.type === "allowedChildren"
            )?.value;
            parentAllowedChildrens = allowedChildren?.map(
              (allowedChild) => allowedChild?.id
            );
          } else {
            parentTemplate = templatesData[Number(parentTemplateId)];
            parentAllowedChildrens = parentTemplate.allowedChildren?.map(
              (allowedChild) => allowedChild?.id
            );
          }
        }

        const childTemplateType =
          templatesData[Number(info.dragNode.templateId)];

        if (!parentAllowedChildrens?.includes(info.dragNode.templateId)) {
          notification.error({
            message: t("Move operation restricted!"),
            description:
              Object.keys(parentAllowedChildrens).length === 0
                ? t("DRAG_RESTRICTED_NO_ALLOWED_CHILDREN", {
                    name: parentTemplate?.name,
                  })
                : t("DRAG_RESTRICTED_TYPE_MISMATCHED", {
                    parent: parentTemplate?.name,
                    child: childTemplateType?.name,
                  }),
          });
          return;
        }
      }

      if (info.dragNode?.templateId == TEMPLATES_ATTRIBUTE_TEMPLATE_ID) {
        notification.open({
          message: t("Cannot drag attribute templates!"),
          type: "error",
          description: t("Please use draft version funtionality!"),
        });
        return;
      }

      dispatch(setMovingMask(true));
      if (info.dragNode.parentId !== newParentId) {
        newMovePayload[dragKey] = newParentId;
      }

      // await moveNode(
      //   dragKey,
      //   info.dragNode.parentId,
      //   newParentId,
      //   dropPosition === -1 || !info.dropToGap ? -1 : info.node.key
      // );

      let dragObj;

      loop(treeData, dragKey, (item, index, arr) => {
        arr.splice(index, 1);
        dragObj = item;
      });

      if (!info.dropToGap) {
        // Drop on the content, add to first
        loop(treeData, dropKey, async (selected: any) => {
          const item = { ...selected };
          if (!item.isLeaf && item.children.length === 0) {
            // dispatch(setExpandedKeys([...expandedKeys, item.key]));

            const response = await getAllNodes(item.key);
            const newLoadData = searchRecursivelyByKey(
              treeData,
              item.key,
              "key"
            );

            const childNodes = [];
            response?.forEach((value) => {
              let icon = "_30_folder";
              let templateName = "";
              let allowedChildrens = {};
              const selectedTemplate = templatesData[Number(value?.templateId)];
              if (selectedTemplate) {
                icon = selectedTemplate.icon;
                templateName = selectedTemplate.name;
                allowedChildrens = selectedTemplate.allowedChildren;
              }

              childNodes.push({
                key: value.id,
                parentId: item.key,
                name: value.name,
                title: value.name,
                templateId: value.templateId,
                allowedChildrens: value?.allowedChildrens,
                isLeaf: item.children.length === 0,
                breadcrumb: [
                  ...newLoadData.breadcrumb,
                  {
                    id: value?.id,
                    title: value?.name,
                    parentId: item.key,
                    templateName: templateName,
                    templateId: value.templateId,
                    allowedChildrens: allowedChildrens,
                  },
                ],
                icon: getAttributeIcon(icon),
              });
            });
            item.children = [...childNodes];
            item.countChildren = item.children.length;

            updateChildren(tempTreeDataPayload, item);
            setTempTreeData([...tempTreeDataPayload]);
            updateChildren(treeData, {
              ...item,
              children: [dragObj, ...childNodes],
            });
            dispatch(setExpandedKeys([...expandedKeys, item.key]));
            setTreeData([...treeData]);
          } else {
            dragObj.parentId = info.node.key;
            dragObj.breadcrumb = [
              ...item.breadcrumb,
              ...dragObj.breadcrumb.slice(-1),
            ];
            if (selected?.keys?.includes(dragKey)) {
              dispatch(setBreadcrumb([...dragObj.breadcrumb]));
            }

            if (dragObj.children && dragObj.children.length > 0) {
              updateBreadcrumbsForChildren(
                dragObj.children,
                dragObj.breadcrumb
              );
            }

            item.children = item.children || [];
            item.children.unshift(dragObj);
            item.countChildren = item.children.length;
            item.isLeaf = item.children.length === 0;
            updateChildren(treeData, item);

            dispatch(setExpandedKeys([...expandedKeys, item.key]));
          }
        });
      } else {
        // above some target node
        dragObj.parentId = info.node.parentId;
        let ar: any[] = [];
        let i: number;
        loop(treeData, dropKey, (_item, index, arr) => {
          ar = arr;
          i = index;
        });

        if (info.node.parentId == nodeId) {
          dragObj.breadcrumb = dragObj.breadcrumb.slice(-1);

          if (dragObj.children && dragObj.children.length > 0) {
            updateBreadcrumbsForChildren(dragObj.children, dragObj.breadcrumb);
          }

          if (selected?.keys?.includes(dragKey)) {
            dispatch(setBreadcrumb(dragObj.breadcrumb));
          }
        } else {
          const breadcrumbs =
            ar.length > 0 ? ar[0].breadcrumb.slice(0, -1) : [];

          dragObj.breadcrumb = [
            ...breadcrumbs,
            ...dragObj.breadcrumb.slice(-1),
          ];
          if (dragObj.children && dragObj.children.length > 0) {
            updateBreadcrumbsForChildren(dragObj.children, dragObj.breadcrumb);
          }
          if (selected.keys.includes(dragKey)) {
            dispatch(setBreadcrumb(dragObj.breadcrumb));
          }
        }

        if (dropPosition === -1) {
          // above
          ar.splice(i, 0, dragObj!);
        } else {
          // below
          ar.splice(i + 1, 0, dragObj!);
        }
      }

      const oldParent = searchRecursivelyByKey(
        treeData,
        info.dragNode.parentId,
        "key"
      );

      if (oldParent && oldParent.children && oldParent.children.length === 0) {
        oldParent.isLeaf = true;
      }

      const newParentNode = searchRecursivelyByKey(
        treeData,
        newParentId,
        "key"
      );

      if (
        newParentNode &&
        newParentNode.children &&
        newParentNode.children.length > 0 &&
        newParentNode.isLeaf
      ) {
        newParentNode.isLeaf = false;
      }

      setTreeData([...treeData]);
      dispatch(setMovePayload(newMovePayload));
      // setTimeout(async () => {
      //   const newParentData = await getAllNodes(newParentId);
      //   queryClient.setQueryData(
      //     [GET_CHILDRENS, newParentId.toString()],
      //     newParentData
      //   );
      //   if (info.dragNode.parentId !== newParentId) {
      //     const newParentData = await getAllNodes(info.dragNode.parentId);
      //     queryClient.setQueryData(
      //       [GET_CHILDRENS, info.dragNode.parentId.toString()],
      //       newParentData
      //     );
      //   }
      // }, 300);

      // const childrensOfOldParent = getChildrens(
      //   treeData,
      //   info.dragNode.parentId
      // );
      // updateQueryCache(childrensOfOldParent, info.dragNode.parentId);
      // const childrensOfNewParent = getChildrens(treeData, newParentId);
      // updateQueryCache(childrensOfNewParent, newParentId);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      notification.open({
        message: t("Error while moving"),
        type: "error",
        description: t("Please try again!"),
      });
    }
  };
  return { handleSidebarDrag };
};

export { useSidebarDragActions };
