import { CloseOutlined, PlusOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Button, Input } from "antd";
import { Checkbox } from "primereact/checkbox";
import { memo, useEffect, useRef, useState } from "react";
import ReactDragListView from "react-drag-listview";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

interface IDropdownItems {
  key: string;
  name: string;
  default: boolean;
}
// Add dropdown items from metamodel, while defining dropdown attribute

const AddDropdownItemsAttributeBase = ({ val, setVal, onEdit }) => {
  const { t } = useTranslation();
  const [add, setAdd] = useState(false);
  const [name, setName] = useState("");
  const inputRef = useRef(null);

  useEffect(() => {
    // initial focus
    if (add) {
      inputRef.current?.focus();
    }
  }, [add]);

  // delete
  const handleDelete = (value) => {
    const newValue = { ...val };
    delete newValue[value];
    setVal(newValue);
    onEdit(newValue);
  };

  // support for drag and drop and rearrange
  const onDragEnd = (fromIndex, toIndex) => {
    const newValues = [...val];
    const item = newValues.splice(fromIndex - 1, 1)[0];
    newValues.splice(toIndex - 1, 0, item);
    setVal(newValues);
    onEdit(newValues);
  };

  return (
    <CustomDropdown>
      <ReactDragListView
        nodeSelector="section"
        handleSelector="section"
        onDragEnd={onDragEnd}
      >
        {val && (
          <Row>
            <p>{t("Default")}</p>
            <p>{t("Values")}</p>
          </Row>
        )}
        {(val || [])?.map((item: IDropdownItems) => (
          <Row key={item.key}>
            <Checkbox
              checked={item?.default}
              onChange={(e) => {
                if (e.checked) {
                  const newValues = val?.map((_val) => {
                    return { ..._val, default: _val.key === item.key };
                  });
                  setVal(newValues);
                  onEdit(newValues);
                } else {
                  const newValues = val?.map((_val) => {
                    return { ..._val, default: false };
                  });
                  setVal(newValues);
                  onEdit(newValues);
                }
              }}
            />
            <p className="drag-item">
              {item.name}{" "}
              <CloseOutlined onClick={() => handleDelete(item.key)} />
            </p>
          </Row>
        ))}
      </ReactDragListView>
      {add ? (
        <Input
          ref={inputRef}
          value={name}
          onChange={(e) => setName(e.target.value)}
          onKeyDown={(e) => {
            if (e.code === "Enter") {
              const items = [...(val || [])];
              items.push({
                key: "key" + Math.floor(1 + Math.random() * 999),
                name: name,
                default: false,
              });

              setAdd(false);
              setVal(items);
              onEdit(items);
            }
          }}
          addonAfter={
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                const items = [...(val || [])];
                items.push({
                  key: "key" + Math.floor(1 + Math.random() * 999),
                  name: name,
                  default: false,
                });
                setAdd(false);
                setVal(items);
                onEdit(items);
              }}
            >
              {t("Add")}
            </Button>
          }
        />
      ) : (
        <a
          className="add-item"
          onClick={() => {
            setAdd(true);
            setName("");
          }}
        >
          <PlusOutlined /> {t("Add new item")}
        </a>
      )}
    </CustomDropdown>
  );
};

export const AddDropdownItemsAttribute = withErrorBoundary(
  memo(AddDropdownItemsAttributeBase),
  "error.generic"
);

const Row = styled.section`
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;

  & .p-checkbox-box {
    height: 20px;
    width: 20px;
  }
  & > p {
    padding: 0px 10px;
  }

  & > :first-child {
    width: 60px;
    justify-content: center;
  }

  & > :last-child {
    flex: 1;
  }
`;

const CustomDropdown = styled.div`
  margin-top: 10px;

  & .ant-input-wrapper {
    display: flex;
  }

  & input {
    box-shadow: none !important;
  }

  & .ant-input-group-addon {
    display: contents;

    & button {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      box-shadow: none;
    }
  }
  & .drag-item {
    background: #fff;
    border: 1px solid #edf2f8;
    padding: 5px 10px;
    position: relative;
    cursor: move;

    & > span {
      position: absolute;
      cursor: pointer;
      right: 0px;
      & svg {
        fill: #a49e9e;
      }
    }
  }
`;
