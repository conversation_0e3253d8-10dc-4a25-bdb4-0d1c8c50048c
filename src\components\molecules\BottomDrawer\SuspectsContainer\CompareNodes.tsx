import { styled } from "@linaria/react";
import { Button, Carousel, Empty } from "antd";
import { Dialog } from "primereact/dialog";
import { AttributeItem } from "../../../atoms";
import { useTranslation } from "react-i18next";
import { memo } from "react";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

const TITLE_CLASSNAME = "compare";

// Comparing suspects
const CompareNodesBase = ({
  visible,
  onHide,
  onNotFound,
  onRename,
  record,
}) => {
  const { t } = useTranslation();

  return (
    <Dialog
      modal
      visible={visible}
      onHide={onHide}
      className="export-modal draggable-modal"
      style={{ width: "75%" }}
      header={"Compare"}
    >
      <Wrapper>
        <div className="content">
          <div style={{ paddingRight: 13, borderRight: "1px solid #eee" }}>
            <p className="title">{record?.name}</p>
            {record?.body?.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t("No attributes")}
              />
            ) : (
              record?.body?.map((item, index) => (
                <div key={index} className="attribute-wrapper">
                  <AttributeItem
                    readOnly
                    key={index}
                    {...item}
                    title={item.name}
                    titleClassName={TITLE_CLASSNAME}
                  />
                </div>
              ))
            )}
          </div>
          <CarouselWrapper>
            <Carousel
              arrows={record?.children?.length > 2}
              infinite
              dots={false}
            >
              {record?.children?.map((children) => (
                <div className="slide" key={children.id}>
                  {" "}
                  <p className="title">{children?.name}</p>
                  {children?.body?.length === 0 ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={t("No attributes")}
                    />
                  ) : (
                    children?.body?.map((item, index) => (
                      <div key={index} className="attribute-wrapper">
                        <AttributeItem
                          readOnly
                          key={index}
                          {...item}
                          title={item.name}
                          titleClassName={TITLE_CLASSNAME}
                        />
                      </div>
                    ))
                  )}
                  <Button
                    className="rename"
                    type="primary"
                    onClick={() => onRename(children.id, children.name)}
                  >
                    {t("Rename")}
                  </Button>
                </div>
              ))}
            </Carousel>
          </CarouselWrapper>
        </div>

        <div className="footer">
          <Button type="primary" onClick={onNotFound}>
            {t("Mark as not found")}
          </Button>
        </div>
      </Wrapper>
    </Dialog>
  );
};

const CompareNodes = withErrorBoundary(memo(CompareNodesBase), "error.generic");

export { CompareNodes };

const CarouselWrapper = styled.div`
  width: 400px;

  & .slick-prev {
    left: 0px;
  }

  & .slick-next {
    right: 0px;
  }

  & .slide {
    max-width: 90%;
    width: 90% !important;
    display: flex !important;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
  }
`;

const Wrapper = styled.div`
  & .slick-arrow {
    color: #094375;
  }

  & .rename {
    margin-top: 10px;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
    background: green;
    font-size: 13px;

    &:hover {
      background: green !important;
      opacity: 0.8;
    }
  }
  & .content {
    display: flex;
    gap: 10px;

    & > div {
      width: 49%;
    }
  }

  & .attribute-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  & .title {
    color: #094375;
    margin-bottom: 10px;
    font-size: 13px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
  & .p-splitter {
    border: none;
  }

  & .content-wrapper {
    cursor: pointer;
    padding: 0px 10px 10px 10px;
    height: 100%;
    border: 1px solid #fff;
    border-radius: 5px;
    padding-top: 4px;
    margin-left: 5px;
    margin-right: 5px;

    &:hover {
      border: 1px solid #eee;
    }
  }

  & .footer {
    gap: 10px;
    display: flex;
    justify-content: right;
    margin-bottom: 20px;
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 11px;

    & button {
      box-shadow: none;
      font-size: 13px;
    }
  }
`;
