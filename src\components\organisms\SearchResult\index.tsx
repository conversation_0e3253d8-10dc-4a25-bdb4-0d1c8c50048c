import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import {
  useHyperlinkActions,
  useNotification,
} from "../../../utils/functions/customHooks";
import {
  getAttributeTitleWidth,
  getParentID,
  transformObjectPath,
} from "../../../utils";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Button, Divider, Dropdown, Flex } from "antd";
import { ILocalSettings, ISearchResults } from "../../../interfaces";
import { DetailsContainer } from "../DetailsContainer";
import { GET_LOCAL_SETTINGS_KEY, getAttributeIcon } from "../../../constants";
import { MyTable } from "../MyTable";
import { useMutation, useQueryClient } from "react-query";
import { setHomeSectionMask } from "../../../store/features";
import { saveLocalSettings } from "../../../services";
import { withErrorBoundary } from "../../withErrorBoundary";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;
const TITLE_CLASSNAME = "search-results-table-title";

type Props = {
  searchResults: ISearchResults[];
  type: "rows" | "table";
};

const SearchResultBase = ({ searchResults, type }: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const [data, setData] = useState([]);
  const [triggerChange, setTriggerChange] = useState(0);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [columns, setColumns] = useState([]);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  useEffect(() => {
    setData([...searchResults]);
    setTriggerChange((prev) => prev + 1);
  }, [searchResults]);

  const HYPERLINKS_ACTIONS_TRASH = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("View in trashcan"),
      key: "view-in-trashcan",
    },
  ];

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.searchTable &&
      localSettingsData?.body[0]?.value?.searchTable?.columns?.length > 0
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.searchTable?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.searchTable?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.searchTable.columns?.forEach((column) => {
        const index = COLUMNS.findIndex((item) => item.field === column);
        allColumns.push({
          ...COLUMNS[index],
          pinned: pinned?.includes(column) ? "left" : null,
          sort: sort?.find((val) => val.colId === column)?.sort || null,
        });
      });
      setAllColumnsRequest(
        localSettingsData?.body[0]?.value?.searchTable?.columns
      );
      setPinned(localSettingsData?.body[0]?.value?.searchTable?.pinned);
      setSort(localSettingsData?.body[0]?.value?.searchTable?.sort);
      setFilters(localSettingsData?.body[0]?.value?.searchTable?.filters);
      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
      setAllColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData]);

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const detectChange = () => {
    dispatch(setHomeSectionMask("search"));
  };

  const COLUMNS = [
    {
      headerName: t("Asset Name"),
      field: "name",
      flex: 1,
      cellRenderer: ({ data: record }) => {
        return (
          <Flex vertical gap={8}>
            <Dropdown
              menu={{
                items: record?.inTrash
                  ? HYPERLINKS_ACTIONS_TRASH
                  : HYPERLINKS_ACTIONS,
                onClick: (e) => handleNodeClick(e.key, record.id, record.name),
              }}
              trigger={["contextMenu"]}
            >
              <p
                className={`title-container title ${
                  record?.inTrash ? "trash-hyperlink" : ""
                }`}
                onClick={async (e) => {
                  e.stopPropagation();
                  handleHyperlinkAction({
                    id: record.id,
                    inTrash: record?.inTrash,
                  });
                }}
              >
                {record?.name}
              </p>
            </Dropdown>
          </Flex>
        );
      },
    },
    {
      headerName: "Object Template",
      field: "templateId",
      width: 200,
      flex: 1,
      cellRenderer: ({ data: record }) => {
        const selectedTemplate = templatesData[Number(record?.templateId)];
        if (!selectedTemplate) {
          return "-";
        }
        const templateIcon = selectedTemplate?.icon || "_30_folder";

        return (
          <p className="title-container">
            {getAttributeIcon(templateIcon)}
            {selectedTemplate.name}
          </p>
        );
      },
    },
    {
      headerName: "Path",
      field: "pathName",
      width: 200,
      flex: 1,
      cellRenderer: ({ data }) => (
        <p className="right-align">
          {data?.pathName
            ? transformObjectPath(data?.pathName, data?.inTrash)
            : "-"}
        </p>
      ),
    },
  ];

  useEffect(() => {
    const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  }, [data]);

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setHomeSectionMask(null));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setHomeSectionMask(null));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        searchTable: {
          columns: allColumnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const handleCancel = () => {
    setTriggerChange((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setHomeSectionMask(null));
    }, 200);
  };

  return (
    <Results
      style={
        homeSectionMask === "search"
          ? {
              border: "1px solid red",
              zIndex: "6000",
              position: "relative",
              padding: 8,
              background: "white",
              borderRadius: 6,
            }
          : {}
      }
    >
      {contextHolder}
      <h4>
        {t("SEARCH RESULTS")} ({data?.length})
      </h4>
      <Divider />
      {homeSectionMask === "search" && (
        <Flex gap={10} justify="right">
          <Button
            className="breadcrumb-button cancel cancel-button"
            onClick={handleCancel}
          >
            {t("Cancel")}
          </Button>
          <Button
            className="breadcrumb-button save-button"
            onClick={handleSave}
            loading={mutation.isLoading}
          >
            {t("Save")}
          </Button>
        </Flex>
      )}

      {data?.length === 0 ? (
        <p className="no-results">{t("No results found!")}</p>
      ) : type === "table" ? (
        <MyTable
          excelFileName="search-results"
          noHeader
          columns={columns}
          data={data}
          noSelect
          resetTrigger={triggerChange}
          noDownload
          setSort={setSort}
          setPinned={setPinned}
          setFilters={setFilters}
          setColumnsRequest={setAllColumnsRequest}
          detectChange={detectChange}
          initialFilters={
            localSettingsData?.body[0]?.value?.searchTable?.filters || {}
          }
        />
      ) : (
        <Records>
          {data?.map((record) => (
            <Flex key={record?.id} justify="space-between">
              <Dropdown
                menu={{
                  items: record?.inTrash
                    ? HYPERLINKS_ACTIONS_TRASH
                    : HYPERLINKS_ACTIONS,
                  onClick: (e) =>
                    handleNodeClick(e.key, record.id, record.name),
                }}
                trigger={["contextMenu"]}
              >
                <div
                  className={`title-container title ${
                    record.inTrash ? "trash-hyperlink" : ""
                  }`}
                  onClick={async (e) => {
                    e.stopPropagation();
                    handleHyperlinkAction({
                      id: record.id,
                      inTrash: record.inTrash,
                    });
                  }}
                >
                  {record.name}
                </div>
              </Dropdown>
              <p className="path">
                {transformObjectPath(record?.pathName, record?.inTrash)}
              </p>
            </Flex>
          ))}
        </Records>
      )}

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Results>
  );
};

export const SearchResult = withErrorBoundary(
  SearchResultBase,
  "error.generic"
);

const Results = styled.div`
  margin-bottom: 14px;

  & .ant-flex {
    margin-bottom: 10px;
    & button {
      color: #fff;
      &:hover {
        color: #fff !important;
      }
    }
  }

  & h4 {
    font-weight: 500;
    color: var(--color-text);
  }
  & .ant-divider {
    margin: 5px 0px;
  }

  & .loader {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  & .title {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  & .title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    text-align: left;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }

  & h5 {
    color: var(--color-text);
    font-size: 13px;
    border-bottom: 2px solid;
    width: fit-content;
    padding-bottom: 6px;
    margin-bottom: 12px;
  }
`;

const Records = styled.div`
  margin-top: 10px;

  & .path {
    font-size: 12px;
    color: #808080;
  }
  & > div {
    border: 1px solid #cfdce8;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    padding: 10px;
    transition: all 0.5s ease-in;
    &:hover {
      box-shadow: 0px 0px 2px rgb(17 86 151 / 63%);
    }
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    color: #094f8b;
    gap: 4px;

    & svg {
      width: 20px;
      height: 20px;
      fill: #cdcdcd;
    }
  }
`;
