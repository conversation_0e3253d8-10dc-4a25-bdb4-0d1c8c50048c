import { styled } from "@linaria/react";

export const FormItem = styled.div`
  padding: 8px 0px;
  & > label {
    font-size: 13px;
    display: block;
    color: #094f8b;
    margin-bottom: 7px;

    & span {
      background: #e9edf5;
      padding: 1px 7px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 11px;
    }
  }

  & .hint {
    font-size: 11px;
    margin-top: 3px;
    color: #717171;
    font-style: italic;

    & span {
      color: #4277a2;
      font-size: 12px;
      background: #e9edf5;
      font-style: normal;
      padding: 0px 5px;
      border-radius: 4px;
      margin-right: 2px;
    }
  }

  & .error {
    margin-top: 5px;
    color: #f43939;
  }

  & .success {
    margin-top: 5px;
    color: #4277a2;
  }
`;
