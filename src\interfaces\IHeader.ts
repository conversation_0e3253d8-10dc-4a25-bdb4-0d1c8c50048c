export interface IHeaderResponseType {
  id: string;
  last: boolean;
  name: string;
  parentId: string;
  children: IHeaderResponseType[];
  templateId?: 1 | 2;
  allowedChildren: any;
}

export interface IHeaderResponse {
  home: IHeaderResponseType;
  menu: IHeaderResponseType[];
}
export interface IHeaderData {
  id: string;
  last: boolean;
  name: string;
  parentId: string;
  children: IHeaderData[];
  templateId: number;
  allowedChildren: any;
}
