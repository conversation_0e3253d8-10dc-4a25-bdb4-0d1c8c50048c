/**
 * Utility functions for text manipulation and measurement
 */

/**
 * Extract plain text content from HTML string
 * @param html HTML string to extract text from
 * @returns Plain text content
 */
export const extractTextFromHTML = (html: string): string => {
  if (!html || typeof html !== "string") return "";
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

/**
 * Truncate text to fit within specified width using binary search
 * @param text Text to truncate
 * @param maxWidth Maximum width in pixels
 * @param measurer HTML element used for measuring text width
 * @returns Truncated text with ellipsis if needed
 */
export const truncateTextToWidth = (
  text: string,
  maxWidth: number,
  measurer: HTMLElement
): string => {
  if (!text || !measurer || maxWidth <= 0) return text || "";

  measurer.textContent = text;
  if (measurer.offsetWidth <= maxWidth) return text;

  let left = 0;
  let right = text.length;
  let bestFit = "";

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const testText = text.substring(0, mid) + "...";
    measurer.textContent = testText;

    if (measurer.offsetWidth <= maxWidth) {
      bestFit = testText;
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return bestFit;
};
