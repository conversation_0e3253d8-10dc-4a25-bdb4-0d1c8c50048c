import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  deleteMultipleNodeService,
  deleteNodeService,
  getAllNodes,
} from "../../../../services/node";
import React, { useEffect, useRef } from "react";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_ASSETS,
  GET_CHILDRENS,
  OBJECT_FOLDER_TEMPLATE_ID,
  TRASHCAN_PARENT_NODE_ID,
  getAttributeIcon,
} from "../../../../constants";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { DELETED_FLAG } from "../../../../interfaces";
import { useLocation } from "react-router-dom";
import { getAssets } from "../../../../services";
import { MyTable } from "../../../organisms";
import { transformObjectPath, useTheme } from "../../../../utils";
import { Dialog } from "primereact/dialog";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

const DeleteNodeModalBase = ({
  onClose,
  isOpen,
  id,
  label,
  fromMenuCreator,
  onDeleteClick,
  afterDelete,
  isMultiple,
  templateId,
  deleteAssets,
  action,
  selectedAssets,
}: Props) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const location = useLocation();
  const queryClient = useQueryClient();

  const ref = useRef<any>();

  const selected = useSelector((state: RootState) => state.sidebar.selected);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );
  const metamodel = location.pathname?.includes("metamodel") && !deleteAssets;

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const { data } = useQuery([GET_ASSETS, id], () => getAssets(id), {
    enabled: !!id && action?.key === "delete-template-permanently",
  });

  const invalidateParents = (parentIds: string[]) => {
    parentIds.forEach(async (parentId) => {
      await getAllNodes(parentId).then((childrens) => {
        queryClient.setQueryData(
          [GET_CHILDRENS, parentId.toString()],
          childrens
        );
      });
    });
  };

  const mutation = useMutation(
    isMultiple ? deleteMultipleNodeService : deleteNodeService,
    {
      onSuccess: () => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        afterDelete && afterDelete();
        if (metamodel) {
          queryClient.invalidateQueries(GET_ALL_TEMPLATES_KEY);
        } else {
          queryClient.invalidateQueries([
            GET_CHILDRENS,
            TRASHCAN_PARENT_NODE_ID,
          ]);

          if (!deleteAssets) {
            const toInvalidateParents = [];
            selected?.info?.forEach((item) => {
              toInvalidateParents.push(item.parentId);
            });

            invalidateParents(toInvalidateParents);
          }
        }

        if (action?.key === "delete-template-permanently") {
          queryClient.invalidateQueries(GET_ALL_TEMPLATES_KEY);
        }

        notification.success({
          message: t("Success!"),
          description: metamodel
            ? t("Template disabled successfully!")
            : t("Node moved to trash successfully!"),
        });

        onClose();
      },
      onError: () => {
        notification.error({
          message: metamodel
            ? t("Error Occurred!")
            : t("Error in moving to trash!"),
          description: t("Please try again after sometime"),
        });
      },
    }
  );

  const handleDelete = () => {
    if (onDeleteClick) {
      onDeleteClick();
      onClose();
    } else {
      mutation.mutate(
        isMultiple
          ? { keys: deleteAssets ? selectedAssets : selected.keys }
          : { id: id }
      );
    }
  };

  const hasAllDisabledTemplates = () => {
    const disabledTemplates = selected?.info?.filter((node) =>
      node.flag?.includes(DELETED_FLAG)
    );
    return disabledTemplates.length === selected.info.length;
  };

  const getModelBody = () => {
    if (templateId == OBJECT_FOLDER_TEMPLATE_ID) {
      return `${t("Are you sure you want to remove this object folder?")}`;
    }
    if (fromMenuCreator) {
      return `${t("Are you sure to remove this menu item?")}`;
    }
    if (isMultiple) {
      if (metamodel) {
        if (hasAllDisabledTemplates()) {
          return t(
            "Are you sure you want to delete selected templates permanently?"
          );
        }
        return t("Are you sure you want to disable selected templates?");
      }

      return t("Are you sure you want to move it to trash bin?");
    }

    if (metamodel) {
      if (action && action?.key === "delete-template-permanently") {
        return `${t("Are you sure to delete this template permanently?")}`;
      }

      return t("Are you sure to disable this template?");
    }
    return (
      <>
        {t("Are you sure you want to move it to trash bin?")}
        {!action?.isLeaf && (
          <span>{t("Be minded that the entire branch will be moved")}</span>
        )}
      </>
    );
  };

  const getButtonLabel = () => {
    if (fromMenuCreator || templateId == OBJECT_FOLDER_TEMPLATE_ID) {
      return t("Delete");
    }
    if (isMultiple) {
      if (metamodel) {
        if (hasAllDisabledTemplates()) {
          return t("Delete selected");
        }
        return t("Disable selected");
      }
      return t("Move selected to trash");
    }

    if (metamodel) {
      if (action && action?.key === "delete-template-permanently") {
        return `${t("Delete")}`;
      }

      return t("Disable");
    }

    return t("Move to trash can");
  };

  const COLUMNS = [
    {
      headerName: "Name",
      field: "name",
      width: 250,
      flex: 1,
      cellRenderer: ({ data: record }) => {
        return (
          <p
            className={` ${
              record?.flag?.includes(DELETED_FLAG) ? "trash-hyperlink" : ""
            }`}
          >
            {record.name}
          </p>
        );
      },
    },

    {
      headerName: "Path",
      field: "pathName",
      width: 100,
      flex: 1,
      cellRenderer: ({ data: values }) =>
        transformObjectPath(
          values?.pathName,
          values?.flag?.includes(DELETED_FLAG)
        ),
    },
  ];

  let templateIcon = null;
  if (!isMultiple) {
    templateIcon = templatesData[Number(templateId)]?.icon || "_30_folder";
  }

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      footer={null}
      className="export-modal draggable-modal"
      header={t("Trash bin")}
    >
      {action?.key === "delete-template-permanently" && data?.length > 0 ? (
        <AssetContainer theme={theme}>
          <p>
            {t(
              "You cannot remove template which is being used. Remove assets, then try again."
            )}
          </p>

          <MyTable columns={COLUMNS} data={data} noHeader noSelect />

          <small>
            {t("Make sure to delete assets before deleting a template!")}
          </small>
        </AssetContainer>
      ) : (
        <Wrapper>
          {!isMultiple && (
            <p>
              {getAttributeIcon(templateIcon)} {label}
            </p>
          )}

          <h5>{getModelBody()}</h5>

          <Button
            onClick={handleDelete}
            type="primary"
            ref={ref}
            loading={mutation.isLoading}
          >
            {getButtonLabel()}
          </Button>
        </Wrapper>
      )}
    </Dialog>
  );
};

const DeleteNodeModal = withErrorBoundary(
  React.memo(DeleteNodeModalBase),
  "error.generic"
);

export { DeleteNodeModal };

interface Props {
  onClose: () => void;
  selectedAssets?: any[];
  isOpen: boolean;
  id: string;
  deleteAssets?: boolean;
  label: string;
  onDeleteClick?: () => void;
  afterDelete?: () => void;
  isMultiple?: boolean;
  selectedKeys?: number[];
  hasAssetTypeNode?: boolean;
  templateId?: number;
  action?: any;
  fromMenuCreator?: boolean;
}

const AssetContainer = styled.div<{ theme: any }>`
  padding-bottom: 20px;

  & > p {
    padding-bottom: 20px;
    padding-top: 10px;
    color: #f43939;
    font-size: 13px;
  }

  & .trash-hyperlink {
    color: ${({ theme }) => theme.trashBreadcrumbsColor} !important;
  }

  & small {
    margin-top: 10px;
    display: block;
  }
`;
const Wrapper = styled.form`
  padding-bottom: 20px;
  display: flex;
  padding-top: 10px;
  flex-direction: column;
  align-items: center;

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      display: block;
      font-size: 12px;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
