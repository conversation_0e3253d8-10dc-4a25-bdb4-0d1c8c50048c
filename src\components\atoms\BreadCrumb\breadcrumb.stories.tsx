import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { BreadCrumb } from ".";

const meta: Meta<typeof BreadCrumb> = {
  title: "Components/Atoms/Breadcrumb",
  component: BreadCrumb,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof BreadCrumb>;

export const Basic: Story = {
  args: {
    extra: <h3>Extra</h3>,
  },
};
