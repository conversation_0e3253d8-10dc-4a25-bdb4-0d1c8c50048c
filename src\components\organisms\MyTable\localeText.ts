export const localeText = {
  // Filter options translations
  selectAll: "Zaznacz wszystko",
  searchOoo: "Szukaj...",
  blanks: "<PERSON>uste",
  filterOoo: "Filtruj...",
  equals: "<PERSON><PERSON>na się",
  notEqual: "Nie równa się",
  lessThan: "<PERSON><PERSON><PERSON><PERSON><PERSON> niż",
  greaterThan: "<PERSON><PERSON><PERSON><PERSON><PERSON> niż",
  inRange: "W zakresie",
  contains: "Zawiera",
  notContains: "Nie zawiera",
  startsWith: "Zaczyna się od",
  endsWith: "Kończy się na",
  blank: "Puste",
  notBlank: "Nie puste",
  resetFilter: "Resetuj",
  applyFilter: "Zast<PERSON>uj",

  noMatches: "<PERSON>rak wyników",
  loadingOoo: "Ładowanie...",
  columns: "Kolumny",
  rowGroupColumns: "Grupa wierszy",
  pivotColumns: "Pivot",
  valueColumns: "<PERSON><PERSON><PERSON> warto<PERSON>",
  filterColumns: "Filtruj kolumny",
};
