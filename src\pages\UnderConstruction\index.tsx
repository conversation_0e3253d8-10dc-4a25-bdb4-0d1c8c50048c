import { Button } from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Image from "../../assets/images/maintain.png";
import { css } from "@linaria/core";

const UnderConstruction = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <div className={wrapperStyle}>
      <img src={Image} alt="Under Construction" width={500} />
      <h2>{t("Page Under Construction")}</h2>
      <p>{t("We are working on this page. Please check back later.")}</p>
      <Button onClick={() => navigate("/")} type="primary">
        {t("Back Home")}
      </Button>
    </div>
  );
};

export default UnderConstruction;

const wrapperStyle = css`
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;
  padding: 20px;

  & img {
    width: 100%;
    max-width: 550px;
  }

  & h2 {
    color: var(--color-text);
    margin-bottom: 6px;
    font-size: 20px;
    margin-top: -20px;
  }
  & p {
    font-size: 14px;
    margin-bottom: 28px;
    color: #7d7d7d;
    text-align: center;
  }
`;
