import { GET_BITFLAGS, GET_NODE_ATTRIBUTES_DETAILS } from "../../../constants";
import { useQueryClient } from "react-query";
import { IBitFlags, INodeDetails } from "../../../interfaces";

/**
 * Custom hook for working with bit flags
 * @returns Object with utility functions for working with bit flags
 */
const useFlags = () => {
  const queryClient = useQueryClient();
  const bitFlags = queryClient.getQueryData(GET_BITFLAGS) as IBitFlags;

  /**
   * Get all flags set in a bitFlag value
   * @param bitFlag The bitFlag value to check
   * @returns Array of flag names that are set in the bitFlag
   */
  const getFlags = (bitFlag: number) => {
    const flags = Object.keys(bitFlags || {})
      .map(Number)
      .filter((key) => (bitFlag & key) !== 0)
      .map((key) => bitFlags[key]);

    return (flags || []) as string[];
  };

  /**
   * Get attribute bitFlags for a specific node
   * @param nodeId The ID of the node to get attribute bitFlags for
   * @returns Object mapping attribute IDs to their bitFlag values
   */
  const getAttributeBitFlags = (
    nodeId?: string | null
  ): Record<number, number> => {
    const attributeBitFlags: Record<number, number> = {};

    if (!nodeId) return attributeBitFlags;

    // Get node details to check for attribute flags
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      nodeId,
    ]) as INodeDetails;

    // Get templates data
    const templatesData = queryClient.getQueryData("templates") as Record<
      number,
      any
    >;

    // If we have node details and a template, populate attributeBitFlags
    if (templatesData?.[nodeDetails.templateId]) {
      const template = templatesData[nodeDetails.templateId];

      // For each attribute template, store its bitFlag if it has one
      template.attributeTemplates?.forEach(
        (attr: { id?: number; bitFlag?: number }) => {
          attributeBitFlags[attr.id] = attr.bitFlag;
        }
      );
    }

    return attributeBitFlags;
  };

  return { getFlags, getAttributeBitFlags };
};

export { useFlags };
