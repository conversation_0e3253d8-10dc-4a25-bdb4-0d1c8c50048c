import { Avatar } from "antd";
import DummyIcon from "../assets/images/dummy.jpeg";
import { ReactComponent as FolderIcon } from "../assets/folder.svg";
import { isBefore } from "date-fns";

export const pinnedData = [
  {
    key: 2,
    id: 2,
    date: "2022-02-15 18:16:54",
    title: "Title",
  },
  {
    key: 3,
    id: 3,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 4,
    id: 4,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 5,
    id: 5,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 6,
    id: 6,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 21,
    id: 21,
    date: "2022-02-15 18:16:54",
    title: "Title",
  },
  {
    key: 31,
    id: 31,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 41,
    id: 41,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 51,
    id: 51,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
  {
    key: 61,
    id: 61,
    date: "2022-02-14 18:16:54",
    title: "Where does it come from?",
  },
];

export const pinnedColumns = [
  {
    label: "Name",
    key: "title",
    width: 240,
    render: (value) => {
      return (
        <div className="name-wrapper">
          <FolderIcon /> {value}
        </div>
      );
    },
  },

  {
    label: "Date",
    dataIndex: "date",
    width: 200,
    key: "date",
  },
];

export const commentsData = [
  {
    key: 2,
    id: 2,
    date: "2022-02-15 18:16:54",
    user: "user",
    title: "Title",
    description: "Lorem Ipsum ",
    image: DummyIcon,
  },
  {
    key: 3,
    id: 3,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 4,
    id: 4,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 5,
    id: 5,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 6,
    id: 6,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
];

export const commentsColumn = [
  {
    label: "Title",
    key: "title",
    width: 200,
  },

  {
    label: "Author",
    key: "user",
    width: 150,
    render: (value, data) => (
      <>
        <Avatar size={32} src={data.image} /> {value}
      </>
    ),
  },
  {
    label: "Date",
    dataIndex: "date",
    width: 120,
    key: "date",
  },
];

export const messageData = [
  {
    key: 2,
    id: 2,
    date: "2022-02-15 18:16:54",
    user: "user",
    title: "Title",
    description: "Lorem Ipsum ",
    image: DummyIcon,
  },
  {
    key: 3,
    id: 3,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 4,
    id: 4,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 5,
    id: 5,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 6,
    id: 6,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 7,
    id: 7,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
  {
    key: 8,
    id: 8,
    date: "2022-02-14 18:16:54",
    user: "Title",
    image: DummyIcon,
    title: "Where does it come from?",
    description: "Lorem Ipsum ",
  },
];

export const messageColumns = [
  {
    label: "Title",
    key: "title",
    width: 200,
  },
  {
    label: "Date",
    dataIndex: "date",
    width: 140,
    key: "date",
  },
  {
    label: "Author",
    key: "user",
    width: 150,
    render: (value, data) => (
      <>
        <Avatar size={32} src={data.image} /> {value}
      </>
    ),
  },
];

export const historyData = [
  {
    key: 2,
    id: 2,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 3,
    id: 3,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 4,
    id: 4,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 5,
    id: 5,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 6,
    id: 6,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 41,
    id: 41,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 51,
    id: 51,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
  {
    key: 61,
    id: 61,
    date: "2022-02-15 18:16:54",
    user: "user",
    new: "For acceptance",
    old: "In progress",
    image: DummyIcon,
    object: "Status",
  },
];

export const historyColumns = [
  {
    label: "Old Value",
    key: "old",
    width: 120,
  },
  {
    label: "New Value",
    key: "new",
    width: 120,
  },

  {
    label: "Commented By",
    key: "user",
    width: 130,
    render: (value, data) => (
      <>
        <Avatar size={32} src={data.image} /> {value}
      </>
    ),
  },
  {
    label: "Date",
    key: "date",
    width: 120,
    sorter: (a, b) => (isBefore(new Date(a.date), new Date(b.date)) ? -1 : 1),
  },
];
