import { styled } from "@linaria/react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Empty } from "antd";
import { Hyperlink } from "../../atoms/AttributeItem/Hyperlink";
import { useQueryClient } from "react-query";
import { GET_NODE_ATTRIBUTES_DETAILS } from "../../../constants";
import { useSearchParams } from "react-router-dom";
import { INodeDetails } from "../../../interfaces";
import { MoreOutlined } from "@ant-design/icons";
import React, { useRef, useEffect } from "react";
import { withErrorBoundary } from "../../withErrorBoundary";

const CompoundBase = ({ id, val }) => {
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();
  const containerRef = useRef(null);
  const leftPanelRef = useRef(null);
  const rightPanelRef = useRef(null);
  const splitterRef = useRef(null);
  const isDraggingRef = useRef(false);
  const startPositionRef = useRef(0);
  const leftWidthRef = useRef(0);
  const leftRatioRef = useRef(0.5); // Default to 50%

  // Initialize and handle resizing
  useEffect(() => {
    if (
      !containerRef.current ||
      !leftPanelRef.current ||
      !rightPanelRef.current ||
      !splitterRef.current
    ) {
      return;
    }

    const container = containerRef.current;
    const leftPanel = leftPanelRef.current;
    const rightPanel = rightPanelRef.current;
    const splitter = splitterRef.current;

    // Function to update layout based on container width and stored ratio
    const updateLayout = () => {
      const containerWidth = container.clientWidth;
      const newLeftWidth = containerWidth * leftRatioRef.current;
      const newRightWidth = containerWidth - newLeftWidth;

      leftPanel.style.width = `${newLeftWidth}px`;
      rightPanel.style.width = `${newRightWidth}px`;
      splitter.style.left = `${newLeftWidth}px`;
    };

    // Initial setup
    updateLayout();

    const handleMouseDown = (e) => {
      e.preventDefault();
      isDraggingRef.current = true;
      startPositionRef.current = e.clientX;
      leftWidthRef.current = leftPanel.offsetWidth;

      document.body.style.cursor = "col-resize";
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

    const handleMouseMove = (e) => {
      if (!isDraggingRef.current) return;

      const containerWidth = container.clientWidth;
      const dx = e.clientX - startPositionRef.current;
      let newLeftWidth = leftWidthRef.current + dx;

      // Apply constraints (min 20%, max 80%)
      const minWidth = containerWidth * 0.2;
      const maxWidth = containerWidth * 0.8;
      newLeftWidth = Math.max(minWidth, Math.min(newLeftWidth, maxWidth));

      // Calculate right panel width
      const newRightWidth = containerWidth - newLeftWidth;

      // Update the stored ratio for future resizes
      leftRatioRef.current = newLeftWidth / containerWidth;

      // Apply new widths
      leftPanel.style.width = `${newLeftWidth}px`;
      rightPanel.style.width = `${newRightWidth}px`;

      // Update splitter position
      splitter.style.left = `${newLeftWidth}px`;
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      document.body.style.cursor = "";
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    // Add event listeners
    splitter.addEventListener("mousedown", handleMouseDown);

    // Use ResizeObserver to detect container size changes
    const resizeObserver = new ResizeObserver(() => {
      if (!isDraggingRef.current) {
        // Only update during non-drag operations
        updateLayout();
      }
    });

    resizeObserver.observe(container);

    // Also handle window resize events
    window.addEventListener("resize", updateLayout);

    return () => {
      splitter.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("resize", updateLayout);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      resizeObserver.disconnect();
    };
  }, []);

  let templateId = null;
  if (searchParams.get("template")) {
    templateId = searchParams.get("template");
  } else {
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]) as INodeDetails;
    templateId = nodeDetails?.templateId;
  }

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const selectedTemplate = templatesData[templateId]?.attributeTemplates;

  const attributes = selectedTemplate?.find((attr) => attr?.id === id);

  return (
    <Wrapper>
      <SplitContainer ref={containerRef}>
        <LeftPanel ref={leftPanelRef}>
          <div className="panel-content">
            {/* Header */}
            <div className="header">
              <h6>{attributes?.nameList1}</h6>
            </div>

            {/* Content */}
            {val.length === 0 ? (
              <div className="content empty">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={false}
                />
              </div>
            ) : (
              <div className="content">
                {val?.map((selected, index) => (
                  <div
                    key={`left-${selected.id}-${index}`}
                    className="item"
                    style={{
                      borderBottom:
                        index < val.length - 1 ? "1px solid #eee" : "none",
                    }}
                  >
                    <div className="hyperlink">
                      <Hyperlink
                        key={`table-${selected.id}-${index}`}
                        val={[selected]}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </LeftPanel>

        <SplitterHandle ref={splitterRef}>
          <MoreOutlined />
        </SplitterHandle>

        <RightPanel ref={rightPanelRef}>
          <div className="panel-content">
            {/* Header */}
            <div className="header">
              <h6>
                {attributes?.nameList2} ({attributes?.multiplicityList2})
              </h6>
            </div>

            {/* Content */}
            {val.length === 0 ? (
              <div className="content empty">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={false}
                />
              </div>
            ) : (
              <div className="content">
                {val?.map((selected, index) => (
                  <div
                    key={`right-${selected.id}-${index}`}
                    className="item"
                    style={{
                      borderBottom:
                        index < val.length - 1 ? "1px solid #eee" : "none",
                    }}
                  >
                    <div className="hyperlink">
                      {selected.value?.length > 0 ? (
                        <Hyperlink
                          key={`table-child-${selected.id}-${index}`}
                          val={[...selected.value]}
                        />
                      ) : (
                        <p className="no-data">--</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </RightPanel>
      </SplitContainer>
    </Wrapper>
  );
};

export const Compound = withErrorBoundary(
  React.memo(CompoundBase),
  "error.generic"
);

const Wrapper = styled.div`
  margin: -6px;
  padding: 6px 6px 1px 12px;
  background-color: var(--color-light);

  & .ant-tree-switcher {
    display: none;
  }
`;

const SplitContainer = styled.div`
  display: flex;
  position: relative;
  width: 100%;
  overflow: hidden;
`;

const LeftPanel = styled.div`
  background-color: white;
  overflow: auto;

  & .panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    & .header {
      padding: 5px;

      & h6 {
        margin: 0;
        max-width: 100%;
      }
    }

    & .content {
      flex: 1;

      &.empty {
        padding: 5px;
      }

      & .item {
        padding: 5px;
      }

      & .hyperlink {
        cursor: pointer;
        overflow: auto;

        & .ag-root-wrapper {
          border-radius: 0px;
        }
      }
    }
  }
`;

const RightPanel = styled.div`
  background-color: white;
  overflow: auto;

  & .panel-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    & .header {
      padding: 5px;

      & h6 {
        margin: 0;
        max-width: 100%;
      }
    }

    & .content {
      flex: 1;

      &.empty {
        padding: 5px;
      }

      & .item {
        padding: 5px;
      }

      & .hyperlink {
        cursor: pointer;
        overflow: auto;

        & .ag-root-wrapper {
          border-radius: 0px;
        }
      }

      & .no-data {
        padding: 6px;
        font-size: 12px;
      }
    }
  }
`;

const SplitterHandle = styled.div`
  position: absolute;
  top: 0;
  bottom: 0;
  width: 10px;
  margin-left: -5px;
  cursor: col-resize;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;

  & .anticon {
    font-size: 30px;
    color: #e0dbdb;
    pointer-events: none;
  }
`;
