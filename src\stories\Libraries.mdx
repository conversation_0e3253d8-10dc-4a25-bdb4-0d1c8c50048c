import { Meta } from "@storybook/blocks";

<Meta title="Documentation/Dependencies" />

<style>
  {`
  thead td{
    background: #eee;
    font-weight: 600;
  }
    .subheading {
      --mediumdark: '#999999';
      font-weight: 700;
      font-size: 13px;
      color: #999;
      letter-spacing: 6px;
      line-height: 24px;
      text-transform: uppercase;
      margin-bottom: 12px;
      margin-top: 60px;
      margin-bottom: 30px;
    }
  `}
</style>

<div className="subheading">Dependencies</div>

<table>
  <thead>
    <tr>
      <td>Library</td>
      <td>Description</td>
    </tr>
  </thead>
  <tbody>
  
  <tr>
      <td>[cytoscape-context-menus](https://www.npmjs.com/package/cytoscape-context-menus)</td>
      <td>Library used for displaying context menu on right click on graph node</td>
    </tr>
     <tr>
      <td>[cytoscape-cose-bilkent](https://www.npmjs.com/package/cytoscape-cose-bilkent)</td>
      <td>Used for drawing hierarchical graphs.</td>
    </tr>
    <tr>
      <td>[Cytoscape](https://cytoscape.org/)</td>
      <td> An open source library for visualizing complex graphs</td>
    </tr>

    <tr>
      <td>[Ant Design](https://ant.design/)</td>
      <td>A popular UI (User Interface) library or design system for building web applications.</td>
    </tr>
    <tr>
      <td>[Linaria](https://github.com/callstack/linaria)</td>
      <td>A CSS-in-JS library that allows developers to write CSS styles directly in their JavaScript code</td>
    </tr>

     <tr>
      <td>[apexcharts](https://apexcharts.com/)</td>
      <td>A modern JavaScript charting library that allows developers to create interactive and visually appealing charts for web applications.</td>
    </tr>

  <tr>
      <td>[axios](https://axios-http.com/docs/intro)</td>
      <td>  A popular JavaScript library used for making HTTP requests from a web browser or Node.js environment, 
       provides an easy-to-use and efficient way to interact with RESTful APIs or perform other HTTP-based operations</td>
    </tr>
      <tr>
      <td>[cytoscape](https://cytoscape.org/)</td>
      <td>An open-source JavaScript library used for graph visualization and analysis. It provides a flexible and powerful platform for displaying and interacting with complex networks or graphs in web applications.y</td>
    </tr>
      <tr>
      <td>[date-fns](https://date-fns.org/)</td>
      <td>A popular lightweight JavaScript library for manipulating, formatting, and parsing dates in a browser or Node.js environment.</td>
    </tr>
      <tr>
      <td>[i18next](https://www.i18next.com/)</td>
      <td>A widely used internationalization framework for JavaScript applications. It provides a comprehensive solution for managing translations and localizations in web and mobile applications.</td>
    </tr>
      <tr>
      <td>[re-resizable](https://www.npmjs.com/package/re-resizable)</td>
      <td>A library that helps in resizing</td>
    </tr>
      <tr>
      <td>[react-color](https://www.npmjs.com/package/react-color)</td>
      <td>A library that provides to use color picker</td>
    </tr>
      <tr>
      <td>[react-drag-listview](https://www.npmjs.com/package/react-drag-listview)</td>
      <td>A library to support dragging</td>
    </tr>
     <tr>
      <td>[react-query](https://tanstack.com/query/v3/)</td>
      <td>A powerful data-fetching library for React applications. It simplifies the process of managing and synchronizing data between a server and a React component. It focuses on providing a great developer experience by abstracting away the complexities of handling asynchronous data fetching, caching, and state management.</td>
    </tr>
     <tr>
      <td>[react-quill](https://github.com/zenoamaro/react-quill)</td>
      <td> A rich text editor component for React applications. It is a wrapper around the Quill.js library, which is a powerful and customizable WYSIWYG (What You See Is What You Get) editor.</td>
    </tr>
     <tr>
      <td>[react-resizable](https://www.npmjs.com/package/react-resizable)</td>
      <td>A React component that allows users to resize elements on a web page. It provides a simple and flexible way to implement resizable functionality for various UI components such as panels, divs, tables, or any other HTML element.</td>
    </tr>
     <tr>
      <td>[react-waypoint](https://www.npmjs.com/package/react-waypoint)</td>
      <td>A React component that helps developers implement scroll-based triggers in their applications. It allows you to detect when an element enters or leaves the viewport during scrolling, enabling you to perform actions or trigger events based on these scroll positions.</td>
    </tr>
     <tr>
      <td>[universal-cookie](https://www.npmjs.com/package/universal-cookie)</td>
      <td>A JavaScript library that provides a universal approach for working with HTTP cookies in both the browser and server environments. It offers a consistent API for creating, reading, updating, and deleting cookies, regardless of whether your code is running on the client-side or server-side.</td>
    </tr>
     <tr>
      <td>[yup](https://www.npmjs.com/package/yup)</td>
      <td>A JavaScript schema validation library that is commonly used in form validation in React applications. It provides a simple and intuitive API for defining and validating schemas for data objects.</td>
    </tr>
   
  </tbody>
</table>

<div className="subheading">Dev Dependencies</div>

<table>
  <thead>
    <tr>
      <td>Library</td>
      <td>Description</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>[storybook](https://storybook.js.org/)</td>
      <td>
        An open-source development environment for building UI components in
        isolation. It provides a way to showcase and interact with components in
        different states and variations without having to navigate through the
        entire application.
      </td>
    </tr>
    <tr>
      <td>[eslint](https://eslint.org/)</td>
      <td>
        A popular open-source JavaScript linter that is widely used in the
        development community. It helps developers find and fix code errors,
        enforce coding styles, and improve code quality in JavaScript and JSX
        (JavaScript XML) files.
      </td>
    </tr>
    <tr>
      <td>[husky](https://www.npmjs.com/package/husky)</td>
      <td>
        A popular open-source Git hook manager for JavaScript projects. Git
        hooks are scripts that run at certain points in the Git workflow,
        allowing you to automate actions or enforce specific rules before or
        after certain Git events, such as committing or pushing code.
      </td>
    </tr>
    <tr>
      <td>
        [lint-staged](https://www.npmjs.com/package/lint-staged?activeTab=readme)
      </td>
      <td>
        An open-source tool that works in conjunction with Husky to run linters
        and other code quality tools on staged files in a Git repository. It
        allows you to run specific tasks on files that are staged for commit,
        helping you ensure code quality and consistency before the code is
        committed to the repository.
      </td>
    </tr>
    <tr>
      <td>[prettier](https://prettier.io/)</td>
      <td>
        A popular opinionated code formatter that automatically enforces a
        consistent code style across your entire codebase. It supports several
        programming languages, including JavaScript, TypeScript, CSS, HTML,
        JSON, and more.
      </td>
    </tr>
    <tr>
      <td>[typescript](https://www.typescriptlang.org/)</td>
      <td>
        An open-source programming language developed by Microsoft. It is a
        strict syntactical superset of JavaScript and adds optional static
        typing to the language. This means that TypeScript extends JavaScript by
        providing additional features and tools for writing more reliable and
        maintainable code.
      </td>
    </tr>
  </tbody>
</table>
