import React, { useRef, useEffect, useState } from "react";
import { Dialog } from "primereact/dialog";
import { Button } from "antd";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { MyTable, Item } from "../../../components";
import { useTheme } from "../../../utils/useTheme";

interface AddToGroupDialogProps {
  visible: boolean;
  onCancel: () => void;
  onAdd: (selected: any[]) => void;
  groups: Array<{ id: number; resourceName: string; objectTemplate: string }>;
}

const columns = [
  {
    headerName: "Nazwa zasobu",
    field: "resourceName",
    minWidth: 200,
  },
  {
    headerName: "Szablon obiektu",
    field: "objectTemplate",
    minWidth: 180,
  },
];

const AddToGroupDialog: React.FC<AddToGroupDialogProps> = ({
  visible,
  onCancel,
  onAdd,
  groups,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const dialogRef = useRef<any>(null);
  const addButtonRef = useRef<any>(null);
  const closeButtonRef = useRef<any>(null);
  const [selected, setSelected] = useState<any[]>([]);
  const [focusedElementId, setFocusedElementId] = useState<string | null>(null);

  // Focus logic (borrowed from AddGroupDialog)
  useEffect(() => {
    if (visible) {
      setFocusedElementId("table");
      setTimeout(() => {
        addButtonRef.current?.focus();
      }, 100);
    } else {
      setSelected([]);
      setFocusedElementId(null);
    }
  }, [visible]);

  // Keyboard navigation (Tab/Shift+Tab)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Tab") {
        event.preventDefault();
        setFocusedElementId((current) => {
          const order = ["table", "addButton", "closeButton"];
          let idx = order.indexOf(current || "table");
          if (idx === -1) idx = 0;
          const dir = event.shiftKey ? -1 : 1;
          let nextIdx = idx + dir;
          if (nextIdx < 0) nextIdx = order.length - 1;
          if (nextIdx >= order.length) nextIdx = 0;
          return order[nextIdx];
        });
      }
    };
    if (visible) document.addEventListener("keyup", handleKeyDown);
    return () => document.removeEventListener("keyup", handleKeyDown);
  }, [visible]);

  useEffect(() => {
    if (focusedElementId === "addButton") {
      setTimeout(() => addButtonRef.current?.focus(), 0);
    } else if (focusedElementId === "closeButton") {
      setTimeout(() => closeButtonRef.current?.focus(), 0);
    }
  }, [focusedElementId]);

  return (
    <Dialog
      ref={dialogRef}
      header={t("Add to group")}
      visible={visible}
      onHide={onCancel}
      style={{ width: "50vw", height: "auto" }}
      className="export-modal draggable-modal"
    >
      <Wrapper>
        <Item theme={theme} className="attribute-item">
          <h6
            className="comment-attribute-title"
            style={{ minWidth: 180, maxWidth: 220 }}
          >
            <p className="attribute-title" style={{ margin: 0 }}>
              {t("Grupy (0..n)")}
            </p>
          </h6>
          <div style={{ flex: 1, padding: 6 }}>
            <MyTable
              columns={columns}
              data={groups}
              height="220px"
              noHeader
              onSelect={setSelected}
              multiplicity="0..n"
            />
          </div>
        </Item>
        <div
          className="buttons"
          style={{ display: "flex", justifyContent: "flex-end", marginTop: 32 }}
        >
          <Button
            ref={addButtonRef}
            type="primary"
            shape="round"
            className="primary-button"
            onClick={() => onAdd(selected)}
            disabled={selected.length === 0}
            style={{ minWidth: 100 }}
            id="addButton"
          >
            {t("Dodaj")}
          </Button>
        </div>
      </Wrapper>
    </Dialog>
  );
};

export default AddToGroupDialog;

const Wrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  & .buttons {
    display: flex;
    justify-content: right;
    margin-top: 20px;
    & button {
      font-size: 13px;
      box-shadow: none;
    }
  }
`;
