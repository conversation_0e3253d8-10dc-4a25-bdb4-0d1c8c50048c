import { ITreeData } from "../../../interfaces";
import { useTemplateActions } from "./useTemplateActions";

const useWorkingVersionActions = () => {
  const { getTemplateIcon, getTemplateName } = useTemplateActions();

  const generateAttributeTemplates = (data: ITreeData[]) => {
    return (
      data?.map((node: ITreeData) => {
        return {
          key: node?.id,
          templateId: node.templateId,
          name: node.name,
          title: node.name,
          icon: getTemplateIcon(node.templateId),
          parentId: node.parentId,
          isLeaf: node?.countChildren === 0,
          children: [],
        };
      }) || null
    );
  };
  const generateWorkingVersionData = (data: ITreeData) => {
    return [
      {
        key: data?.id,
        templateId: data.templateId,
        name: data.name,
        title: getTemplateName(data.templateId),
        icon: getTemplateIcon(data.templateId),
        parentId: data.parentId,
        isLeaf: data?.countChildren === 0,
        permissionsId: data?.permissionsId,
        countChildren: data?.countChildren,
        children:
          data?.countChildren === 0
            ? null
            : generateAttributeTemplates(data?.children),
      },
    ];
  };

  return { generateWorkingVersionData };
};

export { useWorkingVersionActions };
