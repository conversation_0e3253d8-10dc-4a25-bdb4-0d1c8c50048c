import { createSlice } from "@reduxjs/toolkit";

export interface AuthState {
  authenticated: boolean;
  userInfo: any;
  globalPermissions: string[];
  role: "admin" | "user" | "no-person" | null;
  profileId: number;
}

const initialState: AuthState = {
  authenticated: null,
  userInfo: null,
  globalPermissions: [],
  role: null,
  profileId: null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.authenticated = false;
      state.userInfo = null;
      state.globalPermissions = [];
      state.role = null;
      state.profileId = null;
    },
    setRole: (state, action) => {
      state.role = action.payload;
    },
    setProfileId: (state, action) => {
      state.profileId = action.payload;
    },
    setAuthenticated: (state, action) => {
      state.authenticated = action.payload;
    },
    setUserInfo: (state, action) => {
      state.userInfo = action.payload;
    },

    setGlobalPermissions: (state, action) => {
      state.globalPermissions = action.payload;
    },
  },
});

export const {
  setRole,
  setProfileId,
  setAuthenticated,
  setGlobalPermissions,
  setUserInfo,
  logout,
} = authSlice.actions;

export default authSlice.reducer;
