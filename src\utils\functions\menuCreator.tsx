import { Dropdown, notification } from "antd";
import { checkForUniqueness } from "./checkForUniqueness";
import { getDropdownListForMenuCreator1 } from "./generateTreeData";
import { ReactComponent as FileIcon } from "../../assets/file.svg";
import { DELETED_FLAG, ITemplates } from "../../interfaces";

// generate initial response
export const generateRecursiveResponse = (
  data,
  templatesData: ITemplates[]
) => {
  const response = [];
  let afterNodeId = -1;
  data.forEach((item, index) => {
    delete item.title;
    delete item.key;
    // delete item.parentId;
    const hasAllowedChildrens =
      item.children?.length > 0 && item.children[0]?.type === "asset";

    if (hasAllowedChildrens) {
      const allowedChildrens = [];
      item.children.forEach((child, childIndex) => {
        const selectedTemplate = templatesData?.find(
          (temp) => temp.id == child.templateId
        );
        allowedChildrens.push({
          id: child.templateId,
          name: child.name,
          inTrash: selectedTemplate?.flag?.includes(DELETED_FLAG),
        });
        // allowedChildrens[child.templateId] = child.name;
        child.visualOrder = childIndex + 1;
      });
      item.allowedChildren = allowedChildrens;
    }
    response.push({
      ...item,
      body: [],
      afterNodeId: afterNodeId,
      nodeType: "MENU",
      templateId: Number(item.templateId),
      visualOrder: index + 1,
      children:
        !hasAllowedChildrens && item.children.length > 0
          ? generateRecursiveResponse(
              getChildren([...item.children]),
              templatesData
            )
          : [],
    });
    afterNodeId = item.id;
  });
  return response;
};

// get children of parent
const getChildren = (children) => {
  const newArray = [];
  children?.forEach((item) => newArray.push({ ...item }));
  return newArray;
};

// update dropdown if more than one item is selected
export const updateDropDownForMultipleSelect = (
  selectedKeys: string[],
  data: any[],
  handleActionsSelect,
  setOpen
) => {
  data.forEach((item: any) => {
    if (item.children && item.children.length > 0) {
      updateDropDownForMultipleSelect(
        selectedKeys,
        item.children,
        handleActionsSelect,
        setOpen
      );
    }
  });
  return data;
};

// add to parent menu
const addToParent = (
  data,
  name,
  template,
  action,
  defaultExpanded,
  setDefaultExpanded,
  handleActionsSelect
) => {
  const index = data?.findIndex((item) => item?.key === action.id);
  if (index !== -1) {
    const randomKey = Math.floor(Math.random() * 1000).toString();
    setDefaultExpanded([...defaultExpanded, action.id]);
    data[index].hasAllowedChildren = false;
    data[index].children = [
      {
        id: 0,
        key: randomKey,
        parentId: action.id,
        name: name,
        type: action.type,
        templateId: action.type === "asset" ? template : null,

        children: [],
      },
      ...data[index].children,
    ];

    return [data, randomKey];
  }

  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      addToParent(
        item.children,
        name,
        template,
        action,
        defaultExpanded,
        setDefaultExpanded,
        handleActionsSelect
      );
    }
  });
  return [data, null];
};

const updateAllowedChildrens = (
  data,
  PERMISSIONS,
  parentId,
  allowedChildrens,
  handleActionsSelect,
  defaultExpanded,
  setDefaultExpanded
) => {
  if (data) {
    const index = data && data?.findIndex((item) => item?.key == parentId);
    if (index !== -1) {
      const childArray = [];

      allowedChildrens?.forEach((allowedChild) => {
        childArray.push({
          id: `${parentId}-${allowedChild.id}`,
          key: `${parentId}-${allowedChild.id}`,
          parentId: parentId,
          type: "asset",
          templateId: allowedChild.id,
          name: allowedChild.name || "",
          title: (
            <Dropdown
              menu={{
                items: getDropdownListForMenuCreator1(
                  `${parentId}-${allowedChild.id}`,
                  PERMISSIONS,
                  "asset",
                  [],
                  false,
                  false
                ),
                onClick: (e) =>
                  handleActionsSelect(
                    e.key,
                    `${parentId}-${allowedChild.name}`,
                    allowedChild.name,
                    parentId,
                    allowedChildrens
                  ),
              }}
              trigger={["contextMenu"]}
            >
              <p>
                <FileIcon className="folder-icon" />
                {allowedChild.name}
              </p>
            </Dropdown>
          ),
        });
      });
      setDefaultExpanded([...defaultExpanded, parentId]);
      data[index].children = [...childArray];
      data[index].hasAllowedChildren = childArray.length > 0;

      return data;
    }

    data?.forEach((item) => {
      updateAllowedChildrens(
        item?.children,
        PERMISSIONS,
        parentId,
        allowedChildrens,
        handleActionsSelect,
        defaultExpanded,
        setDefaultExpanded
      );
    });
    return data;
  }
};

export const handleAssetAdd = (
  parentId: string,
  PERMISSIONS: string[],
  allowedChildrens: any[],
  treeData,
  setTreeData,
  handleActionsSelect,
  defaultExpanded,
  setDefaultExpanded
) => {
  let newArray = [...treeData];
  newArray = updateAllowedChildrens(
    newArray,
    PERMISSIONS,
    parentId,
    allowedChildrens,
    handleActionsSelect,
    defaultExpanded,
    setDefaultExpanded
  );
  setTreeData([...newArray]);
};
// add as root level menu
export const handleParentMenuAdd = (
  name: string,
  template: string,
  treeData,
  setTreeData,
  action,
  defaultExpanded,
  setDefaultExpanded,
  handleActionsSelect,
  setSelected
) => {
  const isUnique = checkForUniqueness(
    name,
    template,
    treeData,
    action.id !== "root" ? action.id : "root"
  );

  if (isUnique) {
    if (action.id !== "root") {
      let newArray = [...treeData];
      let newId = null;
      [newArray, newId] = addToParent(
        newArray,
        name,
        template,
        action,
        defaultExpanded,
        setDefaultExpanded,
        handleActionsSelect
      );
      setSelected({
        keys: [newId],
        info: [
          {
            id: newId,
            isNew: true,
            name: name,
            parentId: 0,
            type: action.type,
          },
        ],
      });
      setTreeData([...newArray]);
    } else {
      const randomKey = Math.floor(Math.random() * 1000).toString();
      setTreeData([
        {
          id: 0,
          key: randomKey,
          parentId: 0,
          nodeTypeId: 4,
          name: name,
          children: [],
          type: action.type,
          templateId: action.type === "asset" ? template : null,
        },
        ...treeData,
      ]);
      setSelected({
        keys: [randomKey],
        info: [
          {
            id: randomKey,
            isNew: true,
            name: name,
            parentId: 0,
            type: action.type,
          },
        ],
      });
    }
  } else {
    notification.error({
      message: "Node with name already exists!",
      description: "Please try again with different name",
    });
  }
  return isUnique;
};

// function to rename menu
export const handleMenuRename = (
  newName: string,
  treeData,
  setTreeData,
  action,
  handleActionsSelect,
  templateId
) => {
  const isUnique = checkForUniqueness(
    newName,
    templateId,
    treeData,
    action.parentId
  );

  if (isUnique) {
    let newTreeData = [...treeData];
    newTreeData = renameRecursively(
      newTreeData,
      newName,
      action,
      handleActionsSelect,
      templateId
    );
    setTreeData([...newTreeData]);
  } else {
    notification.error({
      message: "Node with name already exists!",
      description: "Please try again with different name",
    });
  }
  return isUnique;
};

// recursive function for renaming, called by => handleMenuRename()
const renameRecursively = (
  data,
  newName: string,
  action,
  handleActionsSelect,
  templateId
) => {
  data.forEach((item) => {
    if (item.key === action.id) {
      item.name = newName;
      item.templateId = Number(templateId);
    } else if (item.children && item.children.length > 0) {
      renameRecursively(
        item.children,
        newName,
        action,
        handleActionsSelect,
        templateId
      );
    }
  });
  return data;
};

// recursive function for deleting, called by => handleMenuDelete()
const deleteRecursively = (data, action, isMultiple?: boolean) => {
  let index;
  if (isMultiple) {
    index = data?.findIndex((item) => item?.key === action);
  } else {
    index = data?.findIndex((item) => item?.key === action.id);
  }
  if (index !== -1) {
    data.splice(index, 1);
    return data;
  }
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      deleteRecursively(item.children, action, isMultiple);
    }
  });
  return data;
};

// function to delete menu
export const handleMenuDelete = (
  treeData,
  setTreeData,
  action,
  handleActionsSelect
) => {
  let newTreeData = [...treeData];
  newTreeData = deleteRecursively(newTreeData, action);
  const data = updateDropDown(
    action.parentId,
    newTreeData,
    handleActionsSelect
  );

  setTreeData([...data]);
};

// function to delete multiple menu
export const handleMultipleMenuDelete = (
  treeData,
  setTreeData,
  selected: any,
  handleActionsSelect
) => {
  let newTreeData = [...treeData];

  let data = [];
  selected.info?.forEach((item) => {
    newTreeData = deleteRecursively(newTreeData, item.id, true);
    data = updateDropDown(item.parentId, newTreeData, handleActionsSelect);
  });
  setTreeData([...data]);
};

// update dropdown after actions performed
const updateDropDown = (parentId: string, data: any[], handleActionsSelect) => {
  const index = data?.findIndex((item) => item?.key === parentId);

  if (index !== -1) {
    data[index].hasAllowedChildren = data[index].children.length > 0;

    return data;
  }
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      updateDropDown(parentId, item.children, handleActionsSelect);
    }
  });
  return data;
};
