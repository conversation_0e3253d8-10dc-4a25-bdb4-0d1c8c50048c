import { memo, useEffect, useState } from "react";
import { Radio } from "antd";
import {
  _20_ATTRIBUTE_ICONS,
  _30_ATTRIBUTE_ICONS,
  GET_ICONS,
} from "../../../constants";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import { useQuery } from "react-query";
import { getFileList } from "../../../services";
import { withErrorBoundary } from "../../../components/withErrorBoundary";
import { IconGridSkeleton } from "../SkeletonComponents";

const AddIconAttributeBase = ({ val, setVal, onEdit }) => {
  const [imageUrl, setImageUrl] = useState<string>();
  const { t } = useTranslation();
  const [loadMoreIcons, setLoadMoreIcons] = useState(false);

  useEffect(() => {
    if (val?.startsWith("_20")) {
      setLoadMoreIcons(true);
    }
  }, [val]);

  // fetching custom icons
  const { data: uploadedIcons, isLoading } = useQuery([GET_ICONS, "ICON"], () =>
    getFileList("ICON")
  );

  return (
    <AddIconWrapper>
      <Radio.Group
        onChange={(e) => {
          setVal(e.target.value);
          onEdit(e.target.value);
          if (imageUrl) setImageUrl(null);
        }}
        value={val}
      >
        {isLoading ? (
          <IconGridSkeleton />
        ) : (
          uploadedIcons?.length > 0 && (
            <div>
              {uploadedIcons?.map((icon) => (
                <Radio
                  key={icon.id}
                  value={`data:image/png;base64, ${icon.content}`}
                >
                  <img src={`data:image/png;base64, ${icon.content}`} />
                </Radio>
              ))}
            </div>
          )
        )}
        <div>
          {_30_ATTRIBUTE_ICONS.map((icon, index) => (
            <Radio key={`_30_${icon.value}-${index}`} value={icon.value}>
              {icon.icon}
            </Radio>
          ))}
        </div>
        {loadMoreIcons && (
          <div>
            {_20_ATTRIBUTE_ICONS.map((icon, index) => (
              <Radio key={`_20_${icon.value}-${index}`} value={icon.value}>
                {icon.icon}
              </Radio>
            ))}
          </div>
        )}
      </Radio.Group>

      {!val?.startsWith("_20") && (
        <p
          className="load-more"
          onClick={() => setLoadMoreIcons(!loadMoreIcons)}
        >
          {loadMoreIcons ? t("Browse less") : t("Browse more icons")}
        </p>
      )}
    </AddIconWrapper>
  );
};

export const AddIconAttribute = withErrorBoundary(
  memo(AddIconAttributeBase),
  "error.generic"
);

const AddIconWrapper = styled.section`
  & .loader {
    padding: 20px;
  }
  & .load-more {
    margin-top: 10px;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  & .ant-radio-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  & .ant-radio-group > div {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #eee;
  }
`;
