### CDO 3.0

#### Naming convention guidelines

- Components follow `atomic design` inside `/src/components` separated into `atoms`, `molecules`, `organisms`
- Components are named with <PERSON><PERSON><PERSON>. Your Awesome Component will be named as `YourAwesomeComponent`.
- Components contains
  - `index.tsx` describing components.
  - `index.test.tsx` for unit test files.
- Typscript `ts` files are named with `camelCasing` convention.
- Pages are stored in `pages` folder inside `src`. Page Components are named with Pascal<PERSON>ase.
- Variables are named with camel case.
- Interfaces and Class names are pascal cased.
- Globals are all `CAPITALIZED`.

CHECK THIS: https://github.com/airbnb/javascript

#### Package Scripts

- `yarn install` to install dependencies and initialize repository
- `yarn dev` to run project,
- `yarn build` build the project,

#### The Folder Structure

- `components` contains custom components built for project.
- `hooks` contains custom hooks that are used in project.
- `pages` contains pages layout which are imported in `App` for routing
- `static` contains static files like; images, audios etc.
- `utils` contains utility functions like imported from `lodash` or `date-fns`.
- `locales` for localization support.
- `assets` for images

### What this project contains

- [x] React with typescript support.
- [x] Folder structure defined for react.
- [x] Ant Design Dependency (frontend library).
- [x] Internationalization support i18n.
- [x] Linting.
- [x] Styling with linaria
- [x] Public Private Routes (Guarded Routes).
- [ ] CI/CD Support for project.
- [ ] Proper error handling ( includes proper error format from backend and frontend error handling )
- [x] File and variable naming convention and consistency.
- [ ] Sentry Support.
- [x] Typescript interfaces and type management
- [x] Route loading progress bars. [nprogress](https://dev.to/vvo/show-a-top-progress-bar-on-fetch-and-router-events-in-next-js-4df3)
