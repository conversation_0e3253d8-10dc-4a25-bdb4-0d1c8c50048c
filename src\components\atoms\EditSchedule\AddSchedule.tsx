import React, { useEffect, useState } from "react";
import { ISchedule } from ".";
import {
  <PERSON><PERSON>,
  Config<PERSON><PERSON><PERSON>,
  DatePicker,
  Divider,
  Input,
  Switch,
  Tooltip,
} from "antd";
import cronValidator from "cron-expression-validator";
import cronstrue from "cronstrue";
import i18next from "i18next";
import en from "antd/locale/en_US";
import pl from "antd/locale/pl_PL";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { styled } from "@linaria/react";
import { useTheme } from "../../../utils";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

dayjs.extend(utc);

// Schedule component for adding and editing schedules
// It includes a date picker for selecting the start date and time,
// a switch for enabling/disabling repeat, and an input field for custom cron expressions.
const AddScheduleBase = ({
  editSchedule,
  handleAdd,
  schedules,
  handleCancel,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const [repeat, setRepeat] = useState(false);
  const [customCronExpression, setCustomCronExpression] = useState(null);
  const [scheduleInstance, setScheduleInstance] = useState({
    startAt: null,
    cronExpressionValues: [],
    enabled: true,
  } as ISchedule);

  useEffect(() => {
    if (editSchedule) {
      setScheduleInstance(editSchedule);
      setRepeat(editSchedule?.cronExpressionValues?.length > 0);
      if (editSchedule?.cronExpressionValues?.length > 0) {
        setCustomCronExpression(
          editSchedule?.cronExpressionValues[0]?.cronExpressionString
        );
      }
    }
  }, [editSchedule]);

  return (
    <ConfigProvider locale={i18next.language.startsWith("pl") ? pl : en}>
      <Container className="add-schedule" theme={theme}>
        <div className="content">
          <div className="row">
            <p className="title">{t("Start at")}</p>
            <DatePicker
              placeholder={t("Date'n'time")}
              showTime
              value={
                scheduleInstance?.startAt
                  ? dayjs(scheduleInstance?.startAt)
                  : null
              }
              format="YYYY-MM-DD HH:mm"
              onPickerValueChange={(value) => {
                const dateValue = value
                  ?.set("second", 0)
                  ?.set("millisecond", 0);
                setScheduleInstance({
                  ...scheduleInstance,
                  startAt: dateValue?.utc()?.toISOString(),
                });
              }}
              disabledDate={(current) => {
                return (
                  current &&
                  (current.isBefore(dayjs().startOf("day")) ||
                    (scheduleInstance?.endAt &&
                      current.isAfter(scheduleInstance?.endAt, "day")))
                );
              }}
              onChange={(value) => {
                const dateValue = value
                  ?.set("second", 0)
                  ?.set("millisecond", 0);
                setScheduleInstance({
                  ...scheduleInstance,
                  startAt: dateValue?.utc()?.toISOString(),
                });
              }}
            />
          </div>

          <div className="row">
            <p className="title">{t("Repeat")}</p>
            <Switch
              value={repeat}
              onChange={(value) => {
                setScheduleInstance({
                  ...scheduleInstance,
                  cronExpressionValues: [],
                  endAt: null,
                });
                setRepeat(value);

                setCustomCronExpression(null);
              }}
            />
            {!repeat && <span className="info">{t("one time")}</span>}
          </div>

          {repeat && (
            <>
              <div className="row">
                <p className="title">{t("Cron expression")}</p>
                <Input
                  style={
                    !customCronExpression ||
                    cronValidator.isValidCronExpression(customCronExpression)
                      ? null
                      : { border: "1px solid red" }
                  }
                  value={customCronExpression}
                  onChange={(e) => {
                    setCustomCronExpression(e.target.value);
                  }}
                />
                <span className="cron-string">
                  {customCronExpression &&
                  cronValidator.isValidCronExpression(customCronExpression)
                    ? cronstrue.toString(customCronExpression, {
                        use24HourTimeFormat: true,
                      })
                    : ""}
                </span>
              </div>

              <div className="row">
                <p className="title">{t("Until")}</p>
                <DatePicker
                  placeholder={t("Date'n'time")}
                  showTime
                  value={
                    scheduleInstance?.endAt
                      ? dayjs(scheduleInstance?.endAt)
                      : null
                  }
                  format="YYYY-MM-DD HH:mm"
                  onPickerValueChange={(value) => {
                    const dateValue = value
                      ?.set("second", 0)
                      ?.set("millisecond", 0);

                    setScheduleInstance({
                      ...scheduleInstance,
                      endAt: dateValue?.utc()?.toISOString(),
                    });
                  }}
                  disabledDate={(current) => {
                    return (
                      current &&
                      (current.isBefore(dayjs().startOf("day")) ||
                        (scheduleInstance?.startAt &&
                          current.isBefore(scheduleInstance?.startAt, "day")))
                    );
                  }}
                  onChange={(value) => {
                    const dateValue = value
                      ?.set("second", 0)
                      ?.set("millisecond", 0);

                    setScheduleInstance({
                      ...scheduleInstance,
                      endAt: dateValue?.utc()?.toISOString(),
                    });
                  }}
                />
              </div>

              <Divider />
              <div
                style={{
                  fontSize: "13px",
                  color: "#555",
                  background: "#f8f8f8",
                  padding: "8px",
                  borderRadius: "5px",
                  marginTop: 10,
                }}
              >
                <strong>{t("Hint:")}</strong> {t("Cron format")}→{" "}
                <code>{t("minute hour day month weekday")}</code> <br />
                <strong>{t("Examples:")}</strong>
                <br />⏰ <code>0 0 9 ? * MON-FRI</code> →{" "}
                {t("Every weekday at 9:00 AM")}
                <br />
                🔁 <code>0 0/30 * * * ? </code> → {t("Every 30 minutes")}
                <br />
                📅 <code>0 0 0 1 * ?</code> →{" "}
                {t("First day of every month at midnight")}
              </div>
            </>
          )}
        </div>
        <div className="button-wrapper">
          <Tooltip title={editSchedule ? t("Update") : t("Add")}>
            <Button
              type="primary"
              className="save-btn"
              icon={<i className="pi pi-save" />}
              disabled={
                !scheduleInstance?.startAt ||
                (repeat && !customCronExpression) ||
                (customCronExpression
                  ? !cronValidator.isValidCronExpression(customCronExpression)
                  : false)
              }
              onClick={() => {
                const allSchedules = [...schedules];
                if (editSchedule) {
                  const editIndex = allSchedules.findIndex(
                    (item) => item.id === editSchedule?.id
                  );
                  allSchedules[editIndex] = {
                    ...scheduleInstance,
                    cronExpressionValues: repeat
                      ? [
                          {
                            cronExpressionString: customCronExpression,
                            cronExpressionType: "CUSTOM",
                          },
                        ]
                      : [],
                  };
                } else {
                  allSchedules.push({
                    ...scheduleInstance,
                    id: Math.floor(Math.random() * 1000),
                    cronExpressionValues: repeat
                      ? [
                          {
                            cronExpressionString: customCronExpression,
                            cronExpressionType: "CUSTOM",
                          },
                        ]
                      : [],
                  });
                }
                handleAdd(allSchedules);
                setScheduleInstance({
                  startAt: null,
                  cronExpressionValues: [],
                  enabled: true,
                  endAt: null,
                });
              }}
            />
          </Tooltip>
          <Tooltip title={t("Cancel")}>
            <Button
              type="primary"
              className="cancel-btn"
              icon={<i className="pi pi-ban" />}
              onClick={() => {
                handleCancel();
              }}
            />
          </Tooltip>
        </div>
      </Container>
    </ConfigProvider>
  );
};

const AddSchedule = withErrorBoundary(
  React.memo(AddScheduleBase),
  "error.generic"
);

export { AddSchedule };

const Container = styled.div<{ theme: any }>`
  display: flex;
  gap: 10px;

  & .cron-string {
    color: black;
  }

  & .ant-picker,
  input {
    width: 250px;
  }

  & .cron-expression {
    border: 1px solid #d9d9d9;
    min-height: 32px;
    padding: 4px 11px;
    border-radius: 8px;
    color: black;
    display: flex;
    justify-content: space-between;

    & span {
      color: var(--color-text);
    }
  }
  & .content-section {
    display: flex;
    gap: 8px;
    flex-direction: column;
    flex: 1;

    & > div {
      display: flex;
      gap: 8px;
      align-items: center;

      & .row {
        margin-bottom: 0px;
      }
    }
  }
  & .info {
    color: #5f6367;
  }

  & .row {
    display: flex;
    flex: 1;
    align-items: center;
    margin-bottom: 10px;
    gap: 8px;
  }

  & .save-btn {
    color: green;
    &:hover {
      color: green !important;
      opacity: 0.8;
    }
  }
  & .cancel-btn {
    color: #d3a706;
    &:hover {
      background-color: white !important;
      color: #d3a706 !important;
      opacity: 0.8;
    }
  }

  & .content {
    flex: 1;
    gap: 8px;
  }
  & .custom-cron {
    font-size: 14px;
    display: block;
    margin-top: 5px;
  }
  & .ant-checkbox-inner {
    height: 18px;
    width: 18px;

    &::after {
      inset-inline-start: 28% !important;
    }
  }
  & .cron_builder {
    max-width: unset;
    background-color: ${({ theme }) => theme.bgAttributes};

    & .disabled {
      background-color: #ededed !important;
      color: #6c757d !important;
    }
    & .tab-content {
      margin-top: 12px;
    }
    & .well {
      min-height: unset;
      padding: 0;
      background: no-repeat;
      box-shadow: none;
      border: none;
      text-align: left;
    }
    & a {
      color: ${({ theme }) => theme.colorPrimary} !important;
    }
  }
`;
