import { useQuery, useQueryClient } from "react-query";
import { getLifecycles } from "../../../services/attribute";
import { DetailsContainer, StyledDataTable } from "../../organisms";
import { useEffect, useState, useTransition } from "react";
import { getInitialTableFilters } from "../../../utils/functions/getInitialTableFilters";
import { Dropdown } from "antd";
import {
  getAttributeTitleWidth,
  getParentID,
  transformObjectPath,
} from "../../../utils";
import { IAttributes, IHyperlinks } from "../../../interfaces";
import {
  ATTACHMENT_NODE_ID,
  GET_LIFECYCLES,
  GET_NODE_ATTRIBUTES_DETAILS,
  PERMISSIONS_NODE_ID,
} from "../../../constants";
import { getNodeDetails } from "../../../services/node";
import { AttributeItem } from "../../atoms";
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { useSearchParams } from "react-router-dom";
import { NoPermissionsModal } from "../Modals";
import { usePermissions } from "../../../utils/functions/customHooks";

const EditLifecycle = ({ val, setVal, onEdit, dropdownItems }) => {
  const queryClient = useQueryClient();
  const [data, setData] = useState([]);
  const [initialLoad, setInitialLoad] = useState(true);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [contextMenuOpen, setContextMenuOpen] = useState(null);
  const [expandedRows, setExpandedRows] = useState(null);
  const [selected, setSelected] = useState(null);
  const [searchParams] = useSearchParams();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isPending, startTransition] = useTransition();
  const { getPermissions } = usePermissions();
  const [noPermissionPopup, setNoPermissionPopup] = useState(false);

  const nodeDetails = queryClient.getQueryData([
    GET_NODE_ATTRIBUTES_DETAILS,
    searchParams.get("nodeId"),
  ]) as any;

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const [filters, setFilters] = useState(
    getInitialTableFilters([
      { key: "name" },
      {
        key: "templateId",
      },
      {
        key: "path",
      },
    ])
  );

  const [sort, setSort] = useState({
    field: null,
    order: null,
  });
  const [initialLoadAPI, setInitialLoadAPI] = useState(true);

  const { isLoading, isFetching } = useQuery<any>(
    [GET_LIFECYCLES, dropdownItems.toString(), val],
    () =>
      getLifecycles(
        dropdownItems,
        searchParams.get("draft")
          ? searchParams.get("template")
          : nodeDetails?.templateId,
        val ? val[0]?.id : null,
        searchParams.get("draft") ? null : searchParams.get("nodeId"),
        nodeDetails?.parentId
      ),
    {
      onSuccess: (data: IHyperlinks[]) => {
        setData(
          data?.filter(
            (item) =>
              !item.inTrash ||
              (item.inTrash &&
                val?.some((hyperlink) => hyperlink?.id === item.id))
          )
        );
      },
      enabled: !!dropdownItems && initialLoadAPI,
    }
  );

  const baseUrl =
    import.meta.env.VITE_APP_BASE_URL === "/"
      ? ""
      : import.meta.env.VITE_APP_BASE_URL;

  const { t } = useTranslation();

  useEffect(() => {
    if (initialLoad && val) {
      setSelected(val);
      setInitialLoad(false);
    }
  }, [initialLoad, val]);

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        setContextMenuOpen(false);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
      }
    }
  };

  const COLUMNS = [
    {
      label: t("Asset Name"),
      key: "name",
      dataIndex: "name",
      render: (record) => {
        return (
          <Dropdown
            menu={{
              items: HYPERLINKS_ACTIONS,
              onClick: (e) => handleNodeClick(e.key, record.id, record.name),
            }}
            trigger={["contextMenu"]}
            onOpenChange={(open) => setContextMenuOpen(open)}
          >
            <p>{record.name}</p>
          </Dropdown>
        );
      },
    },
    {
      label: "Object Template",
      key: "templateId",
      dataIndex: "templateId",
      width: 200,
      render: (record) => {
        const selectedTemplate = templatesData[Number(record.templateId)];
        if (selectedTemplate) {
          return selectedTemplate.name;
        }
        return "-";
      },
    },
    {
      label: "Path",
      key: "path",
      dataIndex: "path",
      width: 200,
      render: (value) => transformObjectPath(value?.path, false),
    },
  ];

  const TITLE_CLASSNAME = "hyperlinktables-title";

  useEffect(() => {
    const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  }, [data]);

  const rowExpansionTemplate = (data) => {
    if (!data?.attributes) {
      return <LoadingOutlined style={{ fontSize: 24, marginLeft: 15 }} />;
    }

    if (data?.attributes?.length === 0) {
      if (data?.hasNewAttributes) {
        return (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        );
      }
      return <p>{t("No attributes11")}</p>;
    }

    return (
      <div style={{ marginLeft: 15 }}>
        {data?.attributes?.map((attribute) => (
          <AttributeItem
            readOnly
            key={attribute.id}
            {...attribute}
            title={attribute.name}
            titleClassName={TITLE_CLASSNAME}
          />
        ))}

        {data?.hasNewAttributes && (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        )}
      </div>
    );
  };

  const handleNodeExpand = async (id) => {
    const attributes = [];
    const nodeDetails = await getNodeDetails(id);
    const selectedTemplateAttributes =
      templatesData[nodeDetails.templateId]?.attributeTemplates || [];

    const nodeAttributes = nodeDetails?.body?.filter(
      (attr) =>
        attr.id !== PERMISSIONS_NODE_ID && attr.id !== ATTACHMENT_NODE_ID
    );
    const hasNewAttributes =
      selectedTemplateAttributes?.length > nodeAttributes?.length;

    selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
      const attributeValue = nodeDetails?.body?.find(
        (item) => item.id == attribute.id
      );
      if (attributeValue) {
        attributes.push({
          ...attributeValue,
          ...attribute,
          value:
            attribute.type === "multiplicity"
              ? {
                  text1: attributeValue?.value?.split("..")[0],
                  text2: attributeValue?.value?.split("..")[1],
                }
              : attribute.type === "switch"
              ? attributeValue?.value || false
              : attributeValue?.value,
        });
      }
    });

    const updatedData = [...data];
    const index = updatedData.findIndex((item) => item.id === id);
    updatedData[index].hasNewAttributes = hasNewAttributes || false;
    updatedData[index].attributes = [...attributes];
    setData([...updatedData]);
  };

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <StyledDataTable
        globalFilterFields={["name", "path"]}
        excelFileName="relations"
        filters={filters}
        setFilters={setFilters}
        sort={sort}
        setSort={setSort}
        loading={isLoading || isFetching}
        height={"50vh"}
        columns={COLUMNS}
        data={data}
        alignLeft
        expandable
        singleSelection
        onRowExpand={(e) => {
          const permissions = getPermissions(e.data?.permissionsId);
          if (permissions.includes("VIEW")) {
            const _expanded = expandedRows || {};
            _expanded[`${e.data.id}`] = true;
            setExpandedRows(_expanded);
            handleNodeExpand(e.data.id);
          } else {
            setNoPermissionPopup(true);
          }
        }}
        noDownload
        onRowCollapse={(e) => {
          const _expanded = { ...(expandedRows || {}) };
          delete _expanded[e.data.id];
          setExpandedRows(_expanded);
        }}
        expandedRows={expandedRows}
        expandCondition={(rowData) => {
          return rowData?.templateHasAttributes;
        }}
        rowExpansionTemplate={rowExpansionTemplate}
        selected={selected}
        setSelected={(item) => {
          setInitialLoadAPI(false);
          setSelected(item);

          if (item) {
            const selectedKeys = [];
            const selectedOptions = item;

            selectedKeys.push({
              id: item.id,
              name: item.name,
              inTrash: item.inTrash,
              templateHasAttributes: item.templateHasAttributes,
            });

            startTransition(() => {
              if (!contextMenuOpen) {
                if (selectedOptions) {
                  setVal(selectedKeys);
                  onEdit(selectedKeys);
                } else {
                  setVal(selectedOptions);
                  onEdit(selectedOptions);
                }
              }
            });
          } else {
            startTransition(() => {
              if (!contextMenuOpen) {
                setVal([]);
                onEdit([]);
              }
            });
          }
        }}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}

      {noPermissionPopup && (
        <NoPermissionsModal
          visible={noPermissionPopup}
          onHide={() => {
            setNoPermissionPopup(false);
          }}
        />
      )}
    </div>
  );
};

export { EditLifecycle };
