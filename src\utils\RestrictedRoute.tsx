import { Navigate, useSearchParams } from "react-router-dom";
import { RootState } from "../store";
import { useSelector } from "react-redux";
import { getReturnUrlFromParams } from "./returnUrl";

export const RestrictedRoute = ({ children }) => {
  const { authenticated } = useSelector((root: RootState) => root.auth);
  const [searchParams] = useSearchParams();

  if (authenticated) {
    // Check for return URL from URL parameters, fallback to home page
    const returnUrl = getReturnUrlFromParams(searchParams);
    return <Navigate to={returnUrl || "/"} replace />;
  }
  return children;
};
