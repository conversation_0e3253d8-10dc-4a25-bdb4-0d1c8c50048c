import fs from 'fs/promises';
import fssync from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { createRequire } from 'module';
import readline from 'readline';
// NOTE: All non-built-in and non-essential imports moved inside main function to allow dependency install before import.

// --- CONFIGURATION ---
const LOG_FILE = path.join(process.cwd(), 'deployment.log');
const REQUIRED_PKGS = ['node-ssh', 'winston', 'dotenv', 'date-fns'];
const PKG_JSON_PATH = path.join(process.cwd(), 'package.json');
const SKIP_CLEANUP = false; // Set to true to skip dependency removal

// Status message padding for alignment
const padStatus = (status) => {
  const PAD_LENGTH = 10; // Adjust based on your preference
  return status.padEnd(PAD_LENGTH);
};

// --- UTILITY FUNCTIONS ---
function log(message) {
  // Use ISO string for timestamp if format is not available yet
  let timestamp;
  try {
    // Try to use date-fns if available
    const { format } = require('date-fns');
    timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  } catch (e) {
    timestamp = new Date().toISOString();
  }
  let fullMessage = `[${timestamp}] - ${message}`;

  // If message contains status keywords, pad them for alignment
  ['STARTING', 'COMPLETED', 'FAILED', 'WARNING', 'EXECUTING', 'INFO', 'CLEANING'].forEach(status => { // Added CLEANING
    if (message.includes(status + ':')) {
      fullMessage = `[${timestamp}] - ${message.replace(status + ':', padStatus(status) + ':')}`;
    }
  });

  console.log(fullMessage);
  fssync.appendFileSync(LOG_FILE, `${fullMessage}\n`);
}

// Truncate output for logging
function truncateOutput(output, maxLength = 2000, maxLines = 5) {
  if (!output) return '';
  const lines = output.split('\n');
  let truncated = false;
  let result = output;
  if (lines.length > maxLines) {
    result = lines.slice(0, maxLines).join('\n');
    truncated = true;
  }
  if (result.length > maxLength) {
    result = result.slice(0, maxLength);
    truncated = true;
  }
  if (truncated) {
    result += '\n...output truncated...';
  }
  return result;
}

// Prompt user for input (async)
function promptUser(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }));
}

const execAsync = async (cmd, spinnerText = null, oraInstance = null) => {
  let spinner = null;
  if (spinnerText && oraInstance) {
    spinner = oraInstance(spinnerText).start();
  }
  log(`🔧 EXECUTING: ${cmd}`);
  return new Promise((resolve, reject) => {
    exec(cmd, (err, stdout, stderr) => {
      if (spinner) spinner.stop();
      const truncatedStdout = truncateOutput(stdout?.trim());
      const truncatedStderr = truncateOutput(stderr?.trim());
      if (err) {
        log(`❌ FAILED: ${cmd}`);
        if (truncatedStdout) log(`Standard output:\n${truncatedStdout}`);
        if (truncatedStderr) log(`Error output:\n${truncatedStderr}`);
        return reject({ err, stdout: truncatedStdout, stderr: truncatedStderr });
      }
      log(`✅ COMPLETED: ${cmd}`);
      if (truncatedStdout) log(`Standard output:\n${truncatedStdout}`);
      if (truncatedStderr) log(`Error output:\n${truncatedStderr}`);
      resolve({ stdout: truncatedStdout, stderr: truncatedStderr });
    });
  });
};

const require = createRequire(import.meta.url);

// --- MAIN DEPLOYMENT SCRIPT ---
(async () => {
  let originalPkg = null;
  let modified = false;
  let ssh = null;
  let addedDeps = []; // Track added dependencies for precise undo
  let undoing = false;

  // --- Ensure undo on process exit, SIGINT, SIGTERM, uncaughtException ---
  const cleanupAndExit = async (reason) => {
    if (undoing) return;
    undoing = true;
    try {
      if (modified && fssync.existsSync(PKG_JSON_PATH + '.bak')) {
        const pkgJsonRawBefore = fssync.readFileSync(PKG_JSON_PATH, 'utf8');
        const hasTrailingNewlineBefore = pkgJsonRawBefore.endsWith('\n');
        const pkgNow = JSON.parse(await fs.readFile(PKG_JSON_PATH, 'utf8'));
        if (pkgNow.devDependencies) {
          addedDeps.forEach(dep => {
            if (pkgNow.devDependencies[dep]) {
              delete pkgNow.devDependencies[dep];
            }
          });
        }
        let pkgString = JSON.stringify(pkgNow, null, 2).replace(/\r\n/g, '\n');
        if (hasTrailingNewlineBefore && !pkgString.endsWith('\n')) {
          pkgString += '\n';
        }
        await fs.writeFile(PKG_JSON_PATH, pkgString, 'utf8');
        fssync.unlinkSync(PKG_JSON_PATH + '.bak');
        log('✅ COMPLETED: Cleanup/undo performed due to: ' + reason);
      }
    } catch (e) {
      log('⚠️ WARNING: Cleanup/undo failed during ' + reason + ' - ' + e.message);
    }
    process.exit(1);
  };

  process.on('SIGINT', () => cleanupAndExit('SIGINT (Ctrl+C)'));
  process.on('SIGTERM', () => cleanupAndExit('SIGTERM'));
  process.on('exit', () => cleanupAndExit('process exit'));
  process.on('uncaughtException', (err) => {
    log('⚠️ WARNING: Uncaught exception: ' + err.message);
    cleanupAndExit('uncaughtException');
  });

  try {
    log('🚀 STARTING: Deployment process initialized');

    // --- NEW: Remove .node_modules/.vite/ cache directory ---
    const viteCachePath = path.join(process.cwd(), 'node_modules', '.vite');
    log('🧹 CLEANING: Checking for and removing Vite cache');
    try {
      if (fssync.existsSync(viteCachePath)) {
        await fs.rm(viteCachePath, { recursive: true, force: true });
        log(`✅ COMPLETED: Vite cache removed from ${viteCachePath}`);
      } else {
        log(`ℹ️ INFO: Vite cache directory (${viteCachePath}) not found, skipping removal.`);
      }
    } catch (e) {
      log(`⚠️ WARNING: Failed to remove Vite cache directory ${viteCachePath} - ${e.message}. Continuing...`);
    }

    // --- Ensure yarn is available, install if missing ---
    let yarnAvailable = false;
    try {
      await execAsync('yarn --version');
      yarnAvailable = true;
      log('ℹ️ INFO: yarn is available.');
    } catch (e) {
      log('⚠️ WARNING: yarn is not available, attempting to install globally with npm.');
      try {
        await execAsync('npm i -g yarn@latest', 'Installing yarn globally...');
        // Re-check
        await execAsync('yarn --version');
        yarnAvailable = true;
        log('✅ COMPLETED: yarn installed globally and is now available.');
      } catch (installErr) {
        log('❌ FAILED: Could not install yarn globally. Please install yarn manually and re-run the script.');
        process.exit(1);
      }
    }

    // --- ENVIRONMENT SELECTION PROMPT ---
    // Find available .env* files
    const envFiles = fssync.readdirSync(process.cwd())
      .filter(f => f === '.env' || /^\.env\..+/.test(f));
    // Map to environment names
    const envOptions = envFiles.map(f => {
      if (f === '.env') return 'dev';
      const match = f.match(/^\.env\.(.+)$/);
      return match ? match[1] : null;
    }).filter(Boolean);
    // Ensure 'dev' is always present
    if (!envOptions.includes('dev')) envOptions.unshift('dev');
    // Deduplicate while preserving order
    const uniqueEnvOptions = envOptions.filter((v, i, arr) => arr.indexOf(v) === i);
    log(`ℹ️ INFO: Available environments: ${uniqueEnvOptions.join(', ')}`);

    // Prompt user for environment selection
    let selectedEnv = null;
    while (true) {
      const ans = (await promptUser(`Which environment would you like to build? (${uniqueEnvOptions.join('/')}), default [dev]: `)).trim();
      if (!ans) {
        selectedEnv = 'dev';
        break;
      }
      if (uniqueEnvOptions.includes(ans)) {
        selectedEnv = ans;
        break;
      }
      log(`❌ Invalid environment: "${ans}". Please choose from: ${uniqueEnvOptions.join(', ')}`);
    }
    log(`ℹ️ INFO: Selected environment: ${selectedEnv}`);

    // Step 1: Backup package.json
    log('📦 STARTING: Backing up package.json');
    originalPkg = JSON.parse(await fs.readFile(PKG_JSON_PATH, 'utf8'));
    await fs.writeFile(PKG_JSON_PATH + '.bak', JSON.stringify(originalPkg, null, 2));
    modified = true;
    log('✅ COMPLETED: package.json backup created');

    // Step 2: Add devDependencies temporarily
    log('📝 STARTING: Adding required dependencies to package.json');
    const newPkg = JSON.parse(JSON.stringify(originalPkg));
    newPkg.devDependencies = newPkg.devDependencies || {};
    REQUIRED_PKGS.forEach((pkg) => {
      if (!newPkg.devDependencies[pkg]) {
        newPkg.devDependencies[pkg] = '*';
        addedDeps.push(pkg);
      }
    });
    await fs.writeFile(PKG_JSON_PATH, JSON.stringify(newPkg, null, 2));
    log('✅ COMPLETED: Dependencies added to package.json');

    // Step 3: Install dependencies
    // --- LOG: Check lock file state before install ---
    const lockFilePath = path.join(process.cwd(), 'yarn.lock');
    let lockFileExistsBefore = fssync.existsSync(lockFilePath);
    let lockFileMTimeBefore = lockFileExistsBefore ? fssync.statSync(lockFilePath).mtimeMs : null;
    log(`🔍 LOG: yarn.lock exists before install: ${lockFileExistsBefore}, mtime: ${lockFileMTimeBefore}`);

    // Check if node_modules exists and all required packages are present
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    let needInstall = false;
    // Simple install logic: if node_modules missing, install; else, check for required packages
    if (!fssync.existsSync(nodeModulesPath)) {
      log('📦 INFO: node_modules missing, will install dependencies.');
      needInstall = true;
    } else {
      for (const pkg of REQUIRED_PKGS) {
        const pkgPath = path.join(nodeModulesPath, pkg);
        if (!fssync.existsSync(pkgPath)) {
          log(`📦 INFO: Required package "${pkg}" missing in node_modules, will install dependencies.`);
          needInstall = true;
          break;
        }
      }
    }
    if (needInstall) {
      log('📦 STARTING: Installing required dependencies');
      let ora = null;
      let dotAnim = null;
      let dotCount = 0;
      let animActive = false;
      try {
        ora = (await import('ora')).default || (await import('ora'));
      } catch (e) {
        log('ℹ️ INFO: ora spinner not available, using manual ... animation.');
      }
      if (ora) {
        await execAsync('yarn install --no-immutable --no-lockfile', 'Installing dependencies (no lockfile)...', ora);
      } else {
        // Manual ... animation (print new line each time, no caret/overwrite)
        animActive = true;
        let animMsg = 'Installing dependencies (no lockfile)';
        if (process.stdout.isTTY) {
          process.stdout.write(animMsg);
          dotAnim = setInterval(() => {
            if (!animActive) return;
            dotCount = (dotCount + 1) % 4;
            process.stdout.write('\r' + animMsg + '.'.repeat(dotCount) + '   ');
          }, 300);
        } else {
          process.stdout.write(animMsg + '...\n');
        }
        try {
          await execAsync('yarn install --no-immutable --no-lockfile');
        } finally {
          animActive = false;
          if (dotAnim) clearInterval(dotAnim);
          if (process.stdout.isTTY) {
            process.stdout.write('\r' + animMsg + '... done.        \n');
          } else {
            process.stdout.write(animMsg + '... done.\n');
          }
        }
      }
      log('✅ COMPLETED: Dependencies successfully installed (no lockfile)');
    } else {
      log('📦 INFO: All required dependencies already installed, skipping yarn install.');
    }

    // --- LOG: Check lock file state after install ---
    let lockFileExistsAfter = fssync.existsSync(lockFilePath);
    let lockFileMTimeAfter = lockFileExistsAfter ? fssync.statSync(lockFilePath).mtimeMs : null;
    log(`🔍 LOG: yarn.lock exists after install: ${lockFileExistsAfter}, mtime: ${lockFileMTimeAfter}`);

    // Step 4: Load libraries dynamically
    log('🔍 STARTING: Loading required libraries');
    // Import all required packages here, after install check
    const dotEnv = (await import('dotenv')).default || (await import('dotenv'));
    const sshMod = (await import('node-ssh')).default || (await import('node-ssh'));
    const dateFns = (await import('date-fns'));
    const winston = (await import('winston')).default || (await import('winston'));
    const NodeSSH = sshMod.default || sshMod.NodeSSH || sshMod;
    log('✅ COMPLETED: Required libraries loaded successfully');

    // Re-import ora after install, so it's available for subsequent steps
    let ora = null;
    try {
      ora = (await import('ora')).default || (await import('ora'));
    } catch (e) {}

    // Step 5: Determine build mode and corresponding env file
    log('🔎 STARTING: Determining build mode and environment files');
    // Build command with selected mode
    const BUILD_COMMAND = process.env.BUILD_COMMAND || `yarn build --mode ${selectedEnv}`;

    // Use selectedEnv as buildMode
    const buildMode = selectedEnv;

    log(`ℹ️ INFO: Build mode detected: ${buildMode}`);

    // Determine env files to load, in order of precedence
    const baseEnvPath = path.join(process.cwd(), '.env');
    const modeEnvPath = path.join(process.cwd(), `.env.${buildMode}`);

    // Load environment variables properly with override support
    log(`ℹ️ INFO: Loading base env from: ${baseEnvPath}`);

    // Read base .env file
    let baseEnv = {};
    if (fssync.existsSync(baseEnvPath)) {
      // Use fs to read the file content without setting process.env
      const baseEnvContent = fssync.readFileSync(baseEnvPath, 'utf8');
      // Parse manually to avoid setting process.env
      baseEnv = dotEnv.parse(baseEnvContent);
      log(`✅ COMPLETED: Base environment loaded from ${baseEnvPath}`);
    } else {
      log(`⚠️ WARNING: Base .env file not found at ${baseEnvPath}`);
    }

    // Read mode-specific .env file
    let modeEnv = {};
    if (fssync.existsSync(modeEnvPath)) {
      log(`ℹ️ INFO: Loading mode-specific env from: ${modeEnvPath}`);
      const modeEnvContent = fssync.readFileSync(modeEnvPath, 'utf8');
      modeEnv = dotEnv.parse(modeEnvContent);
      log(`✅ COMPLETED: Mode-specific environment loaded from ${modeEnvPath}`);
    } else {
      log(`⚠️ WARNING: No mode-specific env file found at ${modeEnvPath}`);
    }

    // Merge environments with mode-specific taking precedence
    let env = { ...baseEnv, ...modeEnv };

    // Log the effective environment (only show a subset of non-sensitive keys)
    log(`ℹ️ INFO: Effective environment configuration (final values used):`);
    const safeKeys = ['SSH_HOST', 'SSH_PORT', 'SSH_USERNAME', 'REMOTE_UPLOAD_DIR', 'REMOTE_WEBAPP_DIR', 'LOCAL_BUILD_DIR'];
    const logged = new Set();
    safeKeys.forEach(key => {
      if (env[key] && !logged.has(key)) {
        log(`  - ${key}: ${env[key]}`);
        logged.add(key);
      }
    });

    // Ensure we have required variables
    if (!env.SSH_HOST || !env.SSH_USERNAME) {
      throw new Error('Required environment variables missing. Check your .env files.');
    }

    // Extract configuration from environment
    const {
      SSH_HOST,
      SSH_PORT = 22,
      SSH_USERNAME,
      SSH_PASSWORD,
      LOCAL_BUILD_DIR = 'dist', // Default to 'dist'
      REMOTE_UPLOAD_DIR = '/home/<USER>/upload',
      REMOTE_WEBAPP_DIR = '/opt/tomcat-10.1.16/webapps'
    } = env;

    const NEW_DIR_NAME = `cdo-tools-${buildMode}`;
    const LOCAL_BUILD_PATH = path.join(process.cwd(), LOCAL_BUILD_DIR);
    log('✅ COMPLETED: Environment variables loaded successfully');

    // --- NEW: Remove local build directory (e.g., dist) before build ---
    log(`🧹 CLEANING: Local build directory: ${LOCAL_BUILD_PATH}`);
    try {
      if (fssync.existsSync(LOCAL_BUILD_PATH)) {
        await fs.rm(LOCAL_BUILD_PATH, { recursive: true, force: true });
        log(`✅ COMPLETED: Local build directory ${LOCAL_BUILD_PATH} removed.`);
      } else {
        log(`ℹ️ INFO: Local build directory ${LOCAL_BUILD_PATH} not found, skipping removal.`);
      }
    } catch (e) {
      log(`❌ FAILED: Could not clean local build directory ${LOCAL_BUILD_PATH} - ${e.message}`);
      throw e; // If we can't clean the build dir, the build might fail or be incorrect
    }

    // Step 6: Run local build
    log(`🏗️ STARTING: Running build command: \`${BUILD_COMMAND}\``);
    await execAsync(BUILD_COMMAND, 'Building project...', ora); // Pass ora instance
    if (!fssync.existsSync(LOCAL_BUILD_PATH)) {
      throw new Error(`🚫 Local build failed, no output in: ${LOCAL_BUILD_PATH}`);
    }
    log(`✅ COMPLETED: Build successful, output directory: ${LOCAL_BUILD_PATH}`);

    // Step 7: Connect to SSH
    log(`🔌 STARTING: Establishing SSH connection to ${SSH_HOST}:${SSH_PORT} as ${SSH_USERNAME}`);
    ssh = new NodeSSH();
    try {
        await ssh.connect({
          host: SSH_HOST,
          username: SSH_USERNAME,
          password: SSH_PASSWORD,
          port: SSH_PORT,
        });
        log(`✅ COMPLETED: SSH connection established successfully`);
    } catch (sshErr) {
        log(`❌ FAILED: SSH connection failed - ${sshErr.message}`);
        console.error('\x1b[31m%s\x1b[0m', sshErr.stack || '');
        throw sshErr;
    }

    // Helper function for sudo commands with password
    const execSudoCommand = async (command, description) => {
      log(`🔐 STARTING: ${description} - Using sudo with password`);
      const fullCommand = `echo '${SSH_PASSWORD}' | sudo -S ${command}`;
      const result = await ssh.execCommand(fullCommand);

      // Check for common sudo errors
      if (result.stderr && !result.stderr.includes('password for')) {
        log(`⚠️ WARNING: Issue with sudo command - ${result.stderr}`);
        return { success: false, result };
      }

      log(`✅ COMPLETED: ${description}`);
      return { success: true, result };
    };

    // Step 8: Remote setup - Clean upload directory
    log(`🗑️ STARTING: Cleaning remote upload directory for ${LOCAL_BUILD_DIR}`);
    // Spinner for remote clean
    let cleanSpinner = null, cleanManual = null, cleanAnim = 0, cleanActive = false;
    if (ora) {
      cleanSpinner = ora('Cleaning remote upload directory...').start();
    } else if (process.stdout.isTTY) {
      cleanActive = true;
      const frames = ['|', '/', '-', '\\'];
      process.stdout.write('Cleaning remote upload directory... ');
      cleanManual = setInterval(() => {
        process.stdout.write('\rCleaning remote upload directory... ' + frames[cleanAnim++ % frames.length]);
      }, 200);
    }
    const cleanCommand = `rm -rf ${REMOTE_UPLOAD_DIR}/${LOCAL_BUILD_DIR}`;
    const cleanResult = await ssh.execCommand(cleanCommand);
    if (ora && cleanSpinner) cleanSpinner.succeed('Remote directory cleaned.');
    else if (cleanActive) {
      clearInterval(cleanManual);
      process.stdout.write('\rCleaning remote upload directory... done.\n');
    }
    if (cleanResult.stderr) {
      log(`⚠️ WARNING: Issue while cleaning remote directory - ${truncateOutput(cleanResult.stderr)}`);
    }
    log(`✅ COMPLETED: Remote directory cleaned`);

    // Step 9: Upload files
    log(`📤 STARTING: Uploading build to remote server - local \`${LOCAL_BUILD_PATH}\` → remote \`${REMOTE_UPLOAD_DIR}\``);
    let transferFailed = false;
    let transferErrors = [];

    // Count total files to upload
    function countFiles(dir) {
      let count = 0;
      const items = fssync.readdirSync(dir, { withFileTypes: true });
      for (const item of items) {
        const fullPath = path.join(dir, item.name);
        if (item.isDirectory()) {
          count += countFiles(fullPath);
        } else {
          count += 1;
        }
      }
      return count;
    }
    let totalFiles = 0;
    try {
      totalFiles = countFiles(LOCAL_BUILD_PATH);
    } catch (e) {
      log('⚠️ WARNING: Could not count total files for upload progress.');
    }
    let uploadedFiles = 0;

    // Progress bar function
    function renderBar(current, total, width = 30) {
      const percent = total ? Math.floor((current / total) * 100) : 0;
      const filled = Math.floor((current / total) * width);
      const bar = '█'.repeat(filled) + '-'.repeat(width - filled);
      return `[${bar}] ${current}/${total} (${percent}%)`;
    }

    let progressBarActive = false;
    let progressBarTimer = null;
    if (ora) {
      var uploadSpinner = ora('Uploading files...').start();
    } else if (process.stdout.isTTY) {
      progressBarActive = true;
      process.stdout.write('Uploading files...\n');
      progressBarTimer = setInterval(() => {
        process.stdout.write('\r' + renderBar(uploadedFiles, totalFiles));
      }, 200);
    }
    // If ora is active, suppress manual bar output in tick

    try {
      await ssh.putDirectory(LOCAL_BUILD_PATH, `${REMOTE_UPLOAD_DIR}/${LOCAL_BUILD_DIR}`, {
        recursive: true,
        concurrency: 10,
        tick: (localPath, remotePath, error) => {
          uploadedFiles++;
          if (ora && uploadSpinner) {
            uploadSpinner.text = `Uploading files... ${uploadedFiles}/${totalFiles} (${totalFiles ? Math.floor((uploadedFiles/totalFiles)*100) : 0}%)`;
            if (typeof uploadSpinner.render === 'function') uploadSpinner.render();
          } else if (process.stdout.isTTY && !ora) {
            process.stdout.write('\r' + renderBar(uploadedFiles, totalFiles));
          }
          if (error) {
            transferFailed = true;
            transferErrors.push(`${localPath}: ${error.message}`);
            log(`⚠️ WARNING: Transfer error: ${localPath} → ${remotePath} - ${error.message}`);
          }
        },
      });

      if (ora && uploadSpinner) {
        uploadSpinner.succeed(`Upload complete: ${uploadedFiles}/${totalFiles} files.`);
      } else if (progressBarActive) {
        clearInterval(progressBarTimer);
        process.stdout.write('\r' + renderBar(uploadedFiles, totalFiles) + ' Done.\n');
      }

      if (transferFailed) {
        log(`⚠️ WARNING: Some files failed to transfer (${transferErrors.length} errors)`);
        transferErrors.slice(0, 3).forEach(err => log(`  - ${err}`));
        if (transferErrors.length > 3) {
          log(`  - ... and ${transferErrors.length - 3} more errors`);
        }
      }

      log(`✅ COMPLETED: Files uploaded to remote server`);
    } catch (uploadError) {
      if (ora && uploadSpinner) uploadSpinner.fail('Upload failed.');
      if (progressBarActive) clearInterval(progressBarTimer);
      log(`❌ FAILED: File upload failed - ${uploadError.message}`);
      throw uploadError;
    }

    // Step 10: Rename remote folder
    const renameCommand = `mv ${REMOTE_UPLOAD_DIR}/${LOCAL_BUILD_DIR} ${REMOTE_UPLOAD_DIR}/${NEW_DIR_NAME}`;
    log(`🔄 STARTING: Renaming remote directory - \`${renameCommand}\``);
    const renameResult = await ssh.execCommand(renameCommand);
    if (renameResult.stderr) {
      log(`❌ FAILED: Directory rename failed - ${truncateOutput(renameResult.stderr)}`);
      throw new Error(`Directory rename failed: ${renameResult.stderr}`);
    }
    log(`✅ COMPLETED: Remote directory renamed to \`${NEW_DIR_NAME}\``);

    // Step 11: Remove old webapp version
    const OLD_WEBAPP = `${REMOTE_WEBAPP_DIR}/${NEW_DIR_NAME}`;
    const { success: removeSuccess } = await execSudoCommand(
      `rm -rf ${OLD_WEBAPP}`,
      `Removing old application version from ${OLD_WEBAPP}`
    );

    if (!removeSuccess) {
      log(`⚠️ WARNING: Could not verify removal of old webapp - continuing anyway`);
    }

    // Step 12: Move to webapps
    const { success: moveSuccess, result: moveResult } = await execSudoCommand(
      `mv ${REMOTE_UPLOAD_DIR}/${NEW_DIR_NAME} ${REMOTE_WEBAPP_DIR}`,
      `Moving application to Tomcat webapps directory`
    );

    if (!moveSuccess) {
      log(`❌ FAILED: Moving to webapps directory failed - ${moveResult.stderr}`);
      throw new Error(`Moving to webapps failed: ${moveResult.stderr}`);
    }

    // Step 13: Verify deployment
    const { success: verifySuccess, result: verifyResult } = await execSudoCommand(
      `ls -la ${REMOTE_WEBAPP_DIR}/${NEW_DIR_NAME}`,
      `Verifying deployment`
    );

    if (verifySuccess) {
      log(`✅ COMPLETED: Deployment verified. Directory listing:`);
      log(`${verifyResult.stdout}`);
    } else {
      log(`⚠️ WARNING: Could not verify final deployment - check server manually`);
    }

    log('🎉 COMPLETED: Deployment process finished successfully!');
  } catch (err) {
    log(`❌ FAILED: Deployment process - ${err.message}`);
    if (err.stdout) log(`Standard output: ${err.stdout}`);
    if (err.stderr) log(`Error output: ${err.stderr}`);
    console.error('\x1b[31m%s\x1b[0m', err.stack || '');
  } finally {
    // Disconnect SSH if connected
    if (ssh) {
      log('🔌 STARTING: Disconnecting SSH session');
      try {
        await ssh.dispose();
        log('✅ COMPLETED: SSH session closed successfully');
      } catch (disconnectError) {
        log(`⚠️ WARNING: SSH disconnect issue - ${disconnectError.message}`);
      }
    }

    // Restore package.json
    try {
      if (modified && fssync.existsSync(PKG_JSON_PATH + '.bak')) {
        // --- LOG: Check package.json line ending and trailing newline before undo ---
        const pkgJsonRawBefore = fssync.readFileSync(PKG_JSON_PATH, 'utf8');
        const hasTrailingNewlineBefore = pkgJsonRawBefore.endsWith('\n');
        log(`🔍 LOG: package.json before undo - trailing newline: ${hasTrailingNewlineBefore}`);

        // Instead of full restore, surgically remove only added devDependencies
        log('↩️ STARTING: Cleaning up added devDependencies from package.json');
        const pkgNow = JSON.parse(await fs.readFile(PKG_JSON_PATH, 'utf8'));
        if (pkgNow.devDependencies) {
          addedDeps.forEach(dep => {
            if (pkgNow.devDependencies[dep]) {
              delete pkgNow.devDependencies[dep];
            }
          });
        }
        // Write with LF endings and preserve trailing newline
        let pkgString = JSON.stringify(pkgNow, null, 2).replace(/\r\n/g, '\n');
        if (hasTrailingNewlineBefore && !pkgString.endsWith('\n')) {
          pkgString += '\n';
        }
        await fs.writeFile(PKG_JSON_PATH, pkgString, 'utf8');
        const pkgJsonRawAfter = fssync.readFileSync(PKG_JSON_PATH, 'utf8');
        const hasTrailingNewlineAfter = pkgJsonRawAfter.endsWith('\n');
        log(`🔍 LOG: package.json after undo - trailing newline: ${hasTrailingNewlineAfter}`);

        // Remove backup
        fssync.unlinkSync(PKG_JSON_PATH + '.bak');
        log('✅ COMPLETED: Added devDependencies removed and backup deleted');
      }

      // Skip the problematic dependency removal step
      if (!SKIP_CLEANUP) {
        log('🗑️ STARTING: Cleaning up (simplified approach)');
        log('ℹ️ INFO: Dependencies will be cleaned up on next yarn install');
      }
    } catch (cleanupError) {
      log(`⚠️ WARNING: Cleanup failed - ${cleanupError.message}`);
    }

    log('👋 COMPLETED: Deployment script execution completed');
  }
})();