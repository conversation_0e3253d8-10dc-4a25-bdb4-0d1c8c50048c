import { useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { useQueryClient } from "react-query";
import { RootState } from "../../../store";
import {
  GET_ALL_TEMPLATES_KEY,
  GET_APP_PROPERTIES,
  GET_LOCAL_SETTINGS_KEY,
  GET_HEADER_MENUS,
  GET_CURRENT_USER,
} from "../../../constants";

interface LoadingProgressTrackerProps {
  onComplete?: () => void;
}

export const LoadingProgressTracker: React.FC<LoadingProgressTrackerProps> = ({
  onComplete,
}) => {
  const queryClient = useQueryClient();
  const completedStagesRef = useRef(new Set<string>());
  const isInitializedRef = useRef(false);

  const { authenticated } = useSelector((root: RootState) => root.auth);

  // Get loading states from React Query
  const currentUserQuery = queryClient.getQueryState(GET_CURRENT_USER);
  const templatesQuery = queryClient.getQueryState(GET_ALL_TEMPLATES_KEY);
  const appPropertiesQuery = queryClient.getQueryState(GET_APP_PROPERTIES);
  const localSettingsQuery = queryClient.getQueryState(GET_LOCAL_SETTINGS_KEY);
  const headerMenusQuery = queryClient.getQueryState(GET_HEADER_MENUS);

  // Helper function to update progress
  const updateProgress = (stage: string, completed: boolean = false) => {
    if (typeof window !== "undefined" && window.updateLoadingProgress) {
      window.updateLoadingProgress(stage, completed);

      if (completed) {
        completedStagesRef.current.add(stage);
      }
    }
  };

  // Initialize progress tracking
  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;

      // Take control from HTML-level progress
      if (typeof window !== "undefined" && window.setReactControlled) {
        window.setReactControlled();
      }

      // Check if we have a token to determine if we should start with auth
      const hasToken = !!localStorage.getItem("token");

      if (hasToken) {
        // Start with bundle complete, then auth
        updateProgress("bundle", true);
        setTimeout(() => updateProgress("auth"), 100);
      } else {
        // No token - this is login page, let it handle its own progress
        completedStagesRef.current.clear();
        setTimeout(() => {
          onComplete?.();
        }, 100);
      }
    }
  }, [onComplete]);

  // Track authentication progress
  useEffect(() => {
    if (!localStorage.getItem("token")) {
      // No token - reset progress state (handles logout)
      if (!completedStagesRef.current.has("ready")) {
        completedStagesRef.current.clear();
        if (typeof window !== "undefined" && window.resetLoadingProgress) {
          window.resetLoadingProgress();
        }
      }
      return; // Skip auth tracking if no token
    }

    // Only track auth progress if we haven't already handled login success
    if (isInitializedRef.current && !completedStagesRef.current.has("auth")) {
      if (currentUserQuery?.status === "loading") {
        updateProgress("auth");
      } else if (currentUserQuery?.status === "success" && authenticated) {
        updateProgress("auth", true);
      } else if (currentUserQuery?.status === "error") {
        // Authentication failed, redirect will happen via existing logic
        updateProgress("ready", true);
        setTimeout(() => {
          if (typeof window !== "undefined") {
            document.body.classList.add("app-ready");
          }
          onComplete?.();
        }, 500);
      }
    }
  }, [currentUserQuery?.status, authenticated, onComplete]);

  // Track templates loading
  useEffect(() => {
    if (!authenticated || !localStorage.getItem("token")) return;

    if (
      templatesQuery?.status === "loading" &&
      !completedStagesRef.current.has("templates")
    ) {
      updateProgress("templates");
    } else if (
      templatesQuery?.status === "success" &&
      !completedStagesRef.current.has("templates")
    ) {
      updateProgress("templates", true);
    }
  }, [templatesQuery?.status, authenticated]);

  // Track app properties loading
  useEffect(() => {
    if (!authenticated || !localStorage.getItem("token")) return;

    if (
      appPropertiesQuery?.status === "loading" &&
      !completedStagesRef.current.has("properties")
    ) {
      updateProgress("properties");
    } else if (
      appPropertiesQuery?.status === "success" &&
      !completedStagesRef.current.has("properties")
    ) {
      updateProgress("properties", true);
    }
  }, [appPropertiesQuery?.status, authenticated]);

  // Track local settings loading
  useEffect(() => {
    if (!authenticated || !localStorage.getItem("token")) return;

    if (
      localSettingsQuery?.status === "loading" &&
      !completedStagesRef.current.has("settings")
    ) {
      updateProgress("settings");
    } else if (
      localSettingsQuery?.status === "success" &&
      !completedStagesRef.current.has("settings")
    ) {
      updateProgress("settings", true);
    }
  }, [localSettingsQuery?.status, authenticated]);

  // Track header menus loading
  useEffect(() => {
    if (!authenticated || !localStorage.getItem("token")) return;

    if (
      headerMenusQuery?.status === "loading" &&
      !completedStagesRef.current.has("menus")
    ) {
      updateProgress("menus");
    } else if (
      headerMenusQuery?.status === "success" &&
      !completedStagesRef.current.has("menus")
    ) {
      updateProgress("menus", true);
    }
  }, [headerMenusQuery?.status, authenticated]);

  // Check if all critical data is loaded
  useEffect(() => {
    if (!authenticated || !localStorage.getItem("token")) return;

    const criticalQueries = [
      templatesQuery,
      appPropertiesQuery,
      localSettingsQuery,
      headerMenusQuery,
    ];

    const allLoaded = criticalQueries.every(
      (query) => query?.status === "success"
    );

    if (allLoaded && !completedStagesRef.current.has("ready")) {
      // Complete the final ready stage
      updateProgress("ready", true);

      // Hide the initial loader immediately when all data is loaded
      setTimeout(() => {
        if (typeof window !== "undefined") {
          document.body.classList.add("app-ready");
        }
        onComplete?.();
      }, 50); // Minimal delay just for smooth animation
    }
  }, [
    templatesQuery?.status,
    appPropertiesQuery?.status,
    localSettingsQuery?.status,
    headerMenusQuery?.status,
    authenticated,
    onComplete,
  ]);

  // Handle login success from login page
  useEffect(() => {
    const handleLoginSuccess = () => {
      // Reset progress tracking for post-login data loading
      completedStagesRef.current.clear();
      isInitializedRef.current = false;

      // Take control from login component for remaining progress tracking
      if (typeof window !== "undefined" && window.setReactControlled) {
        window.setReactControlled();
      }

      // Mark bundle and auth as completed since login just succeeded
      // (Login component already handled the initial loader display and progress reset)
      completedStagesRef.current.add("bundle");
      completedStagesRef.current.add("auth");
    };

    // Listen for authentication state changes - only trigger once per login
    if (
      authenticated &&
      localStorage.getItem("token") &&
      !isInitializedRef.current
    ) {
      handleLoginSuccess();
    }
  }, [authenticated]);

  // Handle logout scenario - reset progress when user becomes unauthenticated
  useEffect(() => {
    if (!authenticated && !localStorage.getItem("token")) {
      // User logged out - reset all progress state
      completedStagesRef.current.clear();
      isInitializedRef.current = false;

      if (typeof window !== "undefined" && window.resetLoadingProgress) {
        window.resetLoadingProgress();
      }
    }
  }, [authenticated]);

  // Handle errors and edge cases
  useEffect(() => {
    const handleError = () => {
      // If there are errors in critical queries, still complete the loading
      const criticalQueries = [
        templatesQuery,
        appPropertiesQuery,
        localSettingsQuery,
        headerMenusQuery,
      ];

      const hasErrors = criticalQueries.some(
        (query) => query?.status === "error"
      );

      if (
        hasErrors &&
        authenticated &&
        !completedStagesRef.current.has("ready")
      ) {
        // Complete loading even with errors to prevent infinite loading
        setTimeout(() => {
          // Complete the ready stage to finish loading
          updateProgress("ready", true);

          setTimeout(() => {
            if (typeof window !== "undefined") {
              document.body.classList.add("app-ready");
            }
            onComplete?.();
          }, 50);
        }, 1000); // Reduced retry time
      }
    };

    handleError();
  }, [
    templatesQuery?.status,
    appPropertiesQuery?.status,
    localSettingsQuery?.status,
    headerMenusQuery?.status,
    authenticated,
    onComplete,
  ]);

  return null; // This component doesn't render anything
};

export default LoadingProgressTracker;
