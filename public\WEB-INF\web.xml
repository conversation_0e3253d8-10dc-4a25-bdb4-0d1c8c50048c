<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <!-- Filter to add cache-control headers for index.html -->
    <filter>
        <filter-name>NoCacheIndexHtmlFilter</filter-name>
        <filter-class>org.apache.catalina.filters.ExpiresFilter</filter-class>
        <init-param>
            <param-name>ExpiresByType text/html</param-name>
            <!-- Sets Cache-Control: no-cache (or similar) and Expires to now/past for HTML -->
            <param-value>access plus 0 seconds</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>NoCacheIndexHtmlFilter</filter-name>
        <url-pattern>/index.html</url-pattern>
        <dispatcher>REQUEST</dispatcher>
    </filter-mapping>

    <!-- Filter to add security headers -->
    <filter>
        <filter-name>httpHeaderSecurity</filter-name>
        <filter-class>org.apache.catalina.filters.HttpHeaderSecurityFilter</filter-class>
        <init-param>
            <param-name>hstsEnabled</param-name>
            <param-value>false</param-value> <!-- Set to true only if your site is fully HTTPS and you understand HSTS -->
        </init-param>
        <init-param>
            <param-name>antiClickJackingEnabled</param-name>
            <param-value>true</param-value>
        </init-param>
        <init-param>
            <param-name>antiClickJackingOption</param-name>
            <param-value>DENY</param-value> <!-- Corresponds to X-Frame-Options "DENY" -->
        </init-param>
        <init-param>
            <param-name>blockContentTypeSniffingEnabled</param-name>
            <param-value>true</param-value> <!-- Corresponds to X-Content-Type-Options "nosniff" -->
        </init-param>
        <!--
            X-Permitted-Cross-Domain-Policies is not directly supported by Tomcat's HttpHeaderSecurityFilter.
            This header is also largely deprecated in favor of CORS (Cross-Origin Resource Sharing).
            If you need specific cross-domain policies, configure CORS headers instead,
            typically using org.apache.catalina.filters.CorsFilter or a custom filter.
            For many modern applications, not setting X-Permitted-Cross-Domain-Policies is acceptable.
        -->
    </filter>

    <filter-mapping>
        <filter-name>httpHeaderSecurity</filter-name>
        <url-pattern>/*</url-pattern> <!-- Apply security headers to all requests -->
        <dispatcher>REQUEST</dispatcher>
    </filter-mapping>

    <!-- Your existing React app's routing fallback -->
    <error-page>
        <error-code>404</error-code>
        <location>/index.html</location>
    </error-page>

    <!-- Optional: Welcome file list, if you want Tomcat to serve index.html by default for the root path -->
    <!-- This might be redundant if your 404 mapping already handles it effectively for your SPA -->
    <!--
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>
    -->

</web-app>