import * as Comlink from "comlink";
import { ITreeData } from "../interfaces";

export const extractNodeKeysInOrder = (nodes: ITreeData[]): string[] => {
  return nodes.map((node) => node.key.toString());
};

export const identifyChangedPositions = (
  originalOrder: string[],
  newOrder: string[]
): Record<string, number> => {
  const result: Record<string, number> = {};
  const originalPositions: Record<string, number> = {};

  originalOrder.forEach((key, index) => {
    originalPositions[key] = index + 1;
  });

  newOrder.forEach((key, index) => {
    const newPosition = index + 1;
    const originalPosition = originalPositions[key];

    if (originalPosition !== newPosition) {
      result[key] = newPosition;
    }
  });

  return result;
};

/**
 * Creates a map of node keys to their visual order positions
 */
export const extractVisualOrderMap = (
  treeData: ITreeData[] | null | undefined
): Record<string, number> => {
  const result: Record<string, number> = {};
  if (!treeData) return result;

  const stack: Array<{ nodes: ITreeData[] }> = [{ nodes: treeData }];

  while (stack.length > 0) {
    const { nodes } = stack.pop()!;

    nodes.forEach((node, index) => {
      const nodeKey = node.key.toString();
      result[nodeKey] = index + 1;

      if (node.children?.length > 0) {
        stack.push({ nodes: node.children });
      }
    });
  }

  return result;
};

/**
 * Identifies nodes with changed positions and creates optimized payload
 */
export const generateMoveDataRecursively = (
  currentTreeData: ITreeData[] | null | undefined,
  originalOrderMap: Record<string, number>,
  movePayload: { [key: string]: any } = {}
): Record<string, number> => {
  const payload: Record<string, number> = {};
  if (!currentTreeData) return payload;

  const movedNodeKeys = new Set(Object.keys(movePayload));
  const stack: Array<{ nodes: ITreeData[] }> = [{ nodes: currentTreeData }];

  while (stack.length > 0) {
    const { nodes } = stack.pop()!;
    const changedPositions: { nodeKey: string; currentOrder: number }[] = [];

    // Identify changed positions
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      const nodeKey = node.key.toString();
      const currentOrder = i + 1;

      if (!movedNodeKeys.has(nodeKey)) {
        const originalOrder = originalOrderMap[nodeKey];
        if (originalOrder !== undefined && originalOrder !== currentOrder) {
          changedPositions.push({ nodeKey, currentOrder });
        }
      }

      if (node.children?.length > 0) {
        stack.push({ nodes: node.children });
      }
    }

    // Handle changed positions
    if (changedPositions.length === 2) {
      changedPositions.forEach(({ nodeKey, currentOrder }) => {
        payload[nodeKey] = currentOrder;
      });
    } else if (changedPositions.length > 0) {
      const originalPositions = changedPositions.map(
        ({ nodeKey }) => originalOrderMap[nodeKey]
      );
      const currentPositions = changedPositions.map(
        ({ currentOrder }) => currentOrder
      );

      const minAffected = Math.min(
        Math.min(...originalPositions),
        Math.min(...currentPositions)
      );
      const maxAffected = Math.max(
        Math.max(...originalPositions),
        Math.max(...currentPositions)
      );

      for (let i = 0; i < nodes.length; i++) {
        const currentOrder = i + 1;
        if (currentOrder >= minAffected && currentOrder <= maxAffected) {
          const nodeKey = nodes[i].key.toString();
          if (!movedNodeKeys.has(nodeKey)) {
            const originalOrder = originalOrderMap[nodeKey];
            if (originalOrder !== currentOrder) {
              payload[nodeKey] = currentOrder;
            }
          }
        }
      }
    }
  }

  return payload;
};

/**
 * Compares tree data and generates payload with only changed nodes
 */
const compareTreeDataAndGeneratePayload = (
  originalTreeData: ITreeData[] | null | undefined,
  updatedTreeData: ITreeData[] | null | undefined,
  movePayload: { [key: string]: any } = {}
): Record<string, number> => {
  if (!originalTreeData || !updatedTreeData) return {};

  const originalOrderMap = extractVisualOrderMap(originalTreeData);
  return generateMoveDataRecursively(
    updatedTreeData,
    originalOrderMap,
    movePayload
  );
};

// Worker API exposed via Comlink
const visualOrderWorker = {
  extractVisualOrderMap,
  generateMoveDataRecursively,
  extractNodeKeysInOrder,
  identifyChangedPositions,
  compareTreeDataAndGeneratePayload,
  ping: () => "pong",
};

// Export the worker API using Comlink
Comlink.expose(visualOrderWorker);

// Export the type for TypeScript
export type VisualOrderWorkerType = typeof visualOrderWorker;
