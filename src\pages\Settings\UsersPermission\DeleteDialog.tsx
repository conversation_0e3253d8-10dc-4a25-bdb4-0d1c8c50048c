import React from "react";
import { Dialog } from "primereact/dialog";
import { Button } from "antd";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

interface DeleteDialogProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  loading: boolean;
}

const DeleteDialog: React.FC<DeleteDialogProps> = ({
  visible,
  onCancel,
  onConfirm,
  loading,
}) => {
  const { t } = useTranslation();
  const message = t("Are you sure you want to delete selected?");

  return (
    <Dialog
      header={t("Delete")}
      visible={visible}
      onHide={onCancel}
      style={{ width: 400 }}
      className="export-modal draggable-modal"
      footer={null}
    >
      <Wrapper>
        <p style={{ margin: "20px auto", textAlign: "center", fontSize: 13 }}>
          {message}
        </p>
        <div className="buttons">
          <Button onClick={onCancel} disabled={loading}>
            {t("No")}
          </Button>
          <Button type="primary" onClick={onConfirm} loading={loading}>
            {t("Yes")}
          </Button>
        </div>
      </Wrapper>
    </Dialog>
  );
};

const Wrapper = styled.div`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  & .buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
  }
`;

export default withErrorBoundary(DeleteDialog, "error.generic");
