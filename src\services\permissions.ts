import { IPermissions, IRelations } from "../interfaces";
import { API } from "../utils/api";

export const getAllPermissions = (): Promise<IPermissions> => {
  return API.get(`/settings/permissions/get`);
};

export const getPermissionsActions = () => {
  return API.get(`/settings/permissions/actions-devonly`);
};

export const savePermissions = (payload) => {
  return API.post(`/permissions/set`, payload);
};

export const clearPermissions = (id) => {
  return API.post(`permissions/${id}/clear`);
};

export const getPersonsPermissions = (id: string): Promise<IRelations[]> => {
  return API.get(`/model/node/${id}/related-nodes/get?permissions=TRUE`);
};
