import { FilterMatchMode } from "primereact/api";

// initial table filters
export const getInitialTableFilters = (COLUMNS) => {
  const initialFilters = {};
  initialFilters["global"] = {
    value: null,
    matchMode: FilterMatchMode.CONTAINS,
  };

  COLUMNS.forEach((column) => {
    initialFilters[column.key] = {
      value: null,
      matchMode: column.isDate
        ? FilterMatchMode.DATE_IS
        : FilterMatchMode.CONTAINS,
    };
  });
  return initialFilters;
};
