import { <PERSON><PERSON>, <PERSON>a, <PERSON>s } from "@storybook/blocks";

import * as InputStories from "./input.stories";

<Meta of={InputStories} />

# Input Component

A input component is a area for taking input from users.

In CDO tools, input component can be of default, password or textarea type.

<Canvas of={InputStories.DefaultInput} />

<Controls />

### Error

Error is displayed if any.

<Canvas of={InputStories.ErrorInput} />

### Password Field

<Canvas of={InputStories.PasswordInput} />
