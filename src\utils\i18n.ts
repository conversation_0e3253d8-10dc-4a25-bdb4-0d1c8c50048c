import * as i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";
import translationEN from "../locales/en/translation.json";
import translationPL from "../locales/pl/translation.json";

export const LANGUAGES = [
  {
    value: "en",
    label: "English",
  },
  {
    value: "pl",
    label: "Polski",
  },
];

const resources = {
  en: {
    translation: translationEN,
  },
  pl: {
    translation: translationPL,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "en",
    interpolation: {
      escapeValue: false,
    },
    keySeparator: false,
    nsSeparator: "|",
  });

export { i18n };
