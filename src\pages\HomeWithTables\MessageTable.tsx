import { useCallback, useEffect, useState } from "react";
import { MyTable } from "../../components";
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Flex } from "antd";
import { MyTooltip } from "../../components/atoms/MyTooltip";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { setHomeSectionMask } from "../../store/features";
import { useMutation, useQueryClient } from "react-query";
import { ILocalSettings } from "../../interfaces";
import dayjs from "dayjs";
import { RootState } from "../../store";
import { getInitialTableFilters } from "../../utils/functions/getInitialTableFilters";
import { useTranslation } from "react-i18next";
import { saveLocalSettings } from "../../services";
import { useNotification } from "../../utils/functions/customHooks";
import { MESSAGES_DUMMY_DATA } from "../../constants/DUMMY_TABLE_DATAS";
import { FullscreenOutlined } from "@ant-design/icons";

const MessageTable = ({ listeners }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [resetTrigger, setResetTrigger] = useState(0);
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );

  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [columns, setColumns] = useState(COLUMNS);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const detectChange = useCallback(() => {
    dispatch(setHomeSectionMask("messages"));
  }, []);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.messageTable &&
      localSettingsData?.body[0]?.value?.messageTable?.columns
    ) {
      if (localSettingsData?.body[0]?.value?.messageTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.messageTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.messageTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.messageTable.columns?.forEach(
          (column) => {
            const index = COLUMNS.findIndex((item) => item.field === column);
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.messageTable?.columns
        );
        setPinned(localSettingsData?.body[0]?.value?.messageTable?.pinned);
        setSort(localSettingsData?.body[0]?.value?.messageTable?.sort);
        setFilters(localSettingsData?.body[0]?.value?.messageTable?.filters);
        setColumns(allColumns);
      }
    } else {
      setColumns(COLUMNS);
      setColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  useEffect(() => {
    setData([...MESSAGES_DUMMY_DATA]);
    setLoading(false);
    setFilters(getInitialTableFilters(COLUMNS));
  }, []);

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setHomeSectionMask(null));
    }, 200);
  };

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        messageTable: {
          columns: columnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setHomeSectionMask(null));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setHomeSectionMask(null));
    },
  });

  return (
    <>
      {contextHolder}
      <Flex className="header-wrapper" justify="space-between" align="center">
        <h4 {...listeners}>{t("Messages")}</h4>
        {!homeSectionMask && (
          <div className="actions">
            <Link to="/message" className="view-all">
              <MyTooltip title="View all">
                <FullscreenOutlined />
              </MyTooltip>
            </Link>
          </div>
        )}

        {homeSectionMask === "messages" && (
          <Flex gap={10}>
            <Button
              className="breadcrumb-button cancel cancel-button"
              onClick={handleCancel}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="breadcrumb-button save-button"
              onClick={handleSave}
              loading={mutation.isLoading}
            >
              {t("Save")}
            </Button>
          </Flex>
        )}
      </Flex>
      <Divider />
      <article>
        <MyTable
          loading={loading}
          height={"260px"}
          columns={columns}
          data={data}
          resetTrigger={resetTrigger}
          detectChange={detectChange}
          excelFileName="messages"
          setColumnsRequest={setColumnsRequest}
          setSort={setSort}
          setPinned={setPinned}
          setFilters={setFilters}
          initialFilters={
            localSettingsData?.body[0]?.value?.messageTable?.filters || {}
          }
        />
      </article>
    </>
  );
};

export { MessageTable };

const COLUMNS = [
  {
    headerName: "Asset Name",
    field: "title",
    width: 250,
    isHidden: false,
  },
  {
    headerName: "Description",
    field: "description",
    width: 250,
    isHidden: false,
  },

  {
    headerName: "Date",
    field: "date",
    isDate: true,
    width: 250,
    isHidden: false,
    cellRenderer: ({ data }) => {
      return dayjs(data?.date).format("YYYY/MM/DD HH:mm");
    },
  },
  {
    headerName: "Author",
    field: "user",
    width: 200,
    isHidden: false,
    cellRenderer: ({ data }) => (
      <>
        <Avatar size={32} src={data.image} /> {data.user}
      </>
    ),
  },
];
