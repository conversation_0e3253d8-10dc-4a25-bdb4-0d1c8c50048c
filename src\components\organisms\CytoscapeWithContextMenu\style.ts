/**
 * Cytoscape Graph Styling
 *
 * This file contains all styling for the Cytoscape graph component.
 * Styles are organized by element type and state for better maintainability.
 */

// Color constants
const COLORS = {
  // Node colors
  nodeBg: "#fff",
  nodeTextBg: "#fff",
  nodeBorder: "#ddd",

  // Parent node colors
  parentBg: "#f8f8f8",
  parentBgActive: "#f0f0f0",
  parentBgSelected: "#e6f7ff",

  // Selection colors
  selectedBorder: "#1888a3",
  selectedEdge: "#239df9",

  // Edge colors
  edgeTextBg: "white",
};

// Size constants
const SIZES = {
  nodeWidth: "40px",
  nodeHeight: "40px",
  nodeFontSize: "15px",
  edgeFontSize: "14px",
  textPadding: "3px",
  parentPadding: "10px",
};

// Common style utilities
const backgroundImageStyles = {
  "background-image": "data(icon)",
  "background-fit": "contain",
  "background-width": "auto",
  "background-height": "auto",
};

const nodeDimensions = {
  width: SIZES.nodeWidth,
  height: SIZES.nodeHeight,
};

const textLabelStyles = {
  label: "data(label)",
  color: "data(textColor)",
  "font-size": SIZES.nodeFontSize,
  "text-background-color": COLORS.nodeTextBg,
  "text-background-opacity": 1,
  "text-background-padding": SIZES.textPadding,
  "text-background-shape": "roundrectangle",
};

const parentNodeBorder = (width = 1, color = COLORS.nodeBorder) => ({
  "border-width": width,
  "border-color": color,
  "border-style": "dashed",
});

const edgeCommonStyles = {
  "curve-style": "bezier",
  "text-rotation": "autorotate",
  "font-size": SIZES.edgeFontSize,
  "source-endpoint": "outside-to-node",
  "target-endpoint": "outside-to-node",
};

// Export the complete style array
export const graphStyles = [
  // Base node style
  {
    selector: "node",
    style: {
      backgroundColor: COLORS.nodeBg,
      ...backgroundImageStyles,
      shape: "rectangle",
      ...nodeDimensions,
      ...textLabelStyles,
      "text-valign": "bottom",
      "text-halign": "center",
      "text-margin-y": 6,
    },
  },

  // Parent node style
  {
    selector: "node:parent",
    style: {
      "background-opacity": 0.2,
      "background-color": COLORS.parentBg,
      ...parentNodeBorder(),
      "text-valign": "top",
      "text-halign": "center",
      "text-margin-y": -5,
      shape: "roundrectangle",
      padding: SIZES.parentPadding,
    },
  },

  // Selected node style
  {
    selector: "node:selected",
    style: {
      "border-width": "3px",
      "border-color": COLORS.selectedBorder,
      "border-style": "solid",
      "background-color": COLORS.nodeBg,
      shape: "ellipse",
      ...nodeDimensions,
      ...backgroundImageStyles,
    },
  },

  // Base edge style
  {
    selector: "edge",
    style: {
      width: 2,
      "line-color": "data(color)",
      "text-halign": "center",
      "text-valign": "bottom",
      ...edgeCommonStyles,
      "target-arrow-shape": "triangle",
      "target-arrow-color": "data(color)",
    },
  },

  // Edge with label style
  {
    selector: "edge[label]",
    style: {
      "text-background-color": COLORS.edgeTextBg,
      "text-background-opacity": 1,
      "text-background-padding": SIZES.textPadding,
    },
  },

  // Selected edge style
  {
    selector: "edge:selected",
    style: {
      width: 1,
      "line-color": COLORS.selectedEdge,
    },
  },

  // Active parent node style
  {
    selector: "node:parent:active",
    style: {
      "background-opacity": 0.1,
      "background-color": COLORS.parentBgActive,
      ...parentNodeBorder(1, COLORS.selectedBorder),
    },
  },

  // Selected parent node style
  {
    selector: "node:parent:selected",
    style: {
      "background-opacity": 0.15,
      "background-color": COLORS.parentBgSelected,
      ...parentNodeBorder(2, COLORS.selectedBorder),
    },
  },
];
