import { styled } from "@linaria/react";
import { Footer, SearchPopup, SearchResult } from "../../components";
import LogoImage from "../../assets/images/logo.png";
import { useEffect, useState } from "react";
import { useTheme } from "../../utils/useTheme";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { useQuery } from "react-query";
import { getSearchResults } from "../../services/search";
import { GET_SEARCH_RESULTS } from "../../constants";

const HomePage = () => {
  const theme = useTheme();

  const [search, setSearch] = useState("");
  const [searchText, setSearchText] = useState("");
  const [searchResult, setSearchResult] = useState(null);

  const { showLogo, logo, application_name, showDescription } = useSelector(
    (root: RootState) => root.localSettings
  );

  const { data, isLoading } = useQuery(
    [GET_SEARCH_RESULTS, search],
    () => getSearchResults(search),
    {
      enabled: !!search,
    }
  );

  useEffect(() => {
    const searchText = localStorage.getItem("home-search-text");
    if (searchText) {
      setSearchText(searchText);
      setSearch(searchText);
    }
  }, []);

  useEffect(() => {
    setSearchResult(data);
  }, [data]);

  return (
    <Wrapper theme={theme}>
      <BG theme={theme} />
      <Container theme={theme}>
        <div className="logo-wrapper">
          {showLogo && <img src={logo || LogoImage} width={180} />}
          {showDescription && <p>{application_name}</p>}
        </div>
        <SearchPopup
          searchText={searchText}
          setSearchText={setSearchText}
          setSearch={setSearch}
          loading={isLoading}
        />
        {search && searchResult && (
          <div className="search-results">
            <SearchResult type="rows" searchResults={searchResult} />
          </div>
        )}
      </Container>
      <Footer />
    </Wrapper>
  );
};

export default HomePage;

// const Tags = styled.a`
//   display: flex;
//   justify-content: center;
//   margin-top: 18px;

//   & > span {
//     background: #eee;
//     cursor: pointer;
//     padding: 4px 8px;
//     color: #8d8d8d;
//   }
// `;

const Container = styled.div<{ theme: any }>`
  position: absolute;
  inset: 50px;
  bottom: 100px;
  box-shadow: 0px 0px 3px #dbdbdb;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  background: #fff;
  padding-top: 120px;

  & .search-results {
    overflow: auto;
    margin-bottom: 10px;
    margin-top: 10px;
    width: 60%;
    margin-left: auto;
    margin-right: auto;

    & > div {
      margin-top: 0px !important;
    }
  }
  & .ant-input-suffix {
    cursor: pointer;
  }
  & .logo-wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;

    & > img {
      display: flex;
      margin-left: auto;
      margin-right: auto;
      object-fit: contain;
      max-height: 40px;
    }
    & p {
      color: ${({ theme }) => theme.colorPrimary};
    }
  }
`;

const BG = styled.div<{ theme: any }>`
  height: 300px;
  background-color: ${({ theme }) => theme.background};
`;

const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  background-color: #f3f3f3;
  display: flex;
  position: relative;
  height: 100%;
  flex-direction: column;

  & input {
    box-shadow: none !important;
  }

  & .search-results > div {
    margin-top: 20px;
  }

  & .ant-input-group-wrapper {
    display: flex;

    & input {
      min-height: 36px;
      padding-left: 8px;
      font-family: "Poppins", sans-serif;
    }
    & .ant-input-group-addon {
      padding: 0px;
      border: 0px;
    }

    & button {
      height: 44px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      background: ${({ theme }) => theme.colorPrimary};
      padding: 4px 12px;

      & svg {
        width: 26px;
      }
      & path {
        fill: #fff;
      }
    }
  }
`;
