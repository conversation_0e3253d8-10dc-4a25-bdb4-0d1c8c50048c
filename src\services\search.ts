import { ISearchEngineInfo, ISearchResults } from "../interfaces";
import { API } from "../utils/api";

export const getSearchResults = (
  searchText: string
): Promise<ISearchResults[]> => {
  return API.post(`/search/main`, {
    find: searchText,
  });
};

export const getTreeSearchResults = (
  parentId: string,
  searchText: string
): Promise<ISearchResults[]> => {
  return API.post(`/search/tree`, {
    parentId: parentId,
    find: searchText,
  });
};

export const getSearchEngineInfo = (): Promise<ISearchEngineInfo> => {
  return API.get(`/search/version/get`);
};
