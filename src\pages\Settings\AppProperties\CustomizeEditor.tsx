import { styled } from "@linaria/react";
import { Editor } from "../../../components/atoms/TinyEditor/hugerte/editor";
import { Checkbox } from "primereact/checkbox";
import { Dialog } from "primereact/dialog";
import { useState } from "react";
import { RootState } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { setMask } from "../../../store/features";

const allOptions = {
  menubar: "edit insert view format tools table",
  toolbar:
    "undo redo blocks fontfamily fontsize bold italic underline strikethrough link image table align lineheight checklist numlist bullist indent outdent emoticons removeformat insertIframe",
};

const CustomizeEditor = ({ visible, onHide, options, setOptions }) => {
  const [key, setKey] = useState(0);
  const dispatch = useDispatch();
  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const handleChange = (checked, item, name) => {
    if (!mask) {
      dispatch(setMask(true));
    }

    let option = options[name] as string;

    if (checked) {
      option = option + " " + item;
    } else {
      option = option.replace(item, "");
    }

    setOptions({ ...options, [name]: option });
    setKey((key) => key + 1);
  };

  return (
    <Dialog
      header="Customize Editor"
      visible={visible}
      onHide={onHide}
      style={{ width: "70%", height: "70%" }}
    >
      <Wrapper>
        <div className="container">
          <section>
            <div className="options">
              <h4>Menubar</h4>
              <div className="checkboxes">
                {allOptions?.menubar?.split(" ")?.map((menu) => (
                  <div key={menu} className="item">
                    <Checkbox
                      value={menu}
                      checked={options?.menubar?.includes(menu)}
                      onChange={(e) => {
                        handleChange(e.checked, menu, "menubar");
                      }}
                    />
                    <label>{menu}</label>
                  </div>
                ))}
              </div>
            </div>

            <div className="options">
              <h4>Toolbar</h4>
              <div className="checkboxes">
                {allOptions?.toolbar?.split(" ")?.map((toolbar) => (
                  <div key={toolbar} className="item">
                    <Checkbox
                      value={toolbar}
                      checked={options?.toolbar?.includes(toolbar)}
                      onChange={(e) => {
                        handleChange(e.checked, toolbar, "toolbar");
                      }}
                    />
                    <label>{toolbar}</label>
                  </div>
                ))}
              </div>
            </div>
          </section>
          <Editor
            key={key}
            value={""}
            init={{
              branding: false,
              menubar: options.menubar,
              plugins:
                "preview charmap emoticons image link lists searchreplace table wordcount",
              toolbar: options.toolbar,
              elementpath: false,
              extended_valid_elements:
                "iframe[src|frameborder|style|scrolling|class|width|height|name|align]",
              content_style:
                "tbody, tr, td {border-style: inherit !important;}  p { margin: 0 }",
            }}
          />
        </div>
        {/* <div className="buttons">
          <Button type="primary" className="cancel-button">
            Cancel
          </Button>

          <Button type="primary" className="save-button">
            Save
          </Button>
        </div> */}
      </Wrapper>
    </Dialog>
  );
};

export { CustomizeEditor };

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;

  & .buttons {
    display: flex;
    justify-content: right;
    gap: 10px;
    margin-top: 10px;
  }
  & .tox {
    height: 100% !important;
  }

  & .container {
    display: flex;
    overflow: auto;

    & > section {
      overflow-y: auto;
    }

    & > * {
      flex: 1;
    }
  }

  & .options {
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
  & h4 {
    font-size: 14px;
    font-weight: 500;
    color: black;
    margin-bottom: 7px;
  }
  & .checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    & .item {
      display: flex;
      gap: 4px;
      min-width: 120px;
      max-width: 120px;
      font-size: 14px;
      color: var(--color-text);
    }
  }
`;
