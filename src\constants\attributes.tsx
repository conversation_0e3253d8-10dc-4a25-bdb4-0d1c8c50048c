import _30_attribute from "../assets/attribute_icons/_30_attribute.gif";
import _20_attribute from "../assets/attribute_icons/_20_attribute.gif";
import _30_busines_process from "../assets/attribute_icons/_30_busines_process.gif";
import _30_business_term from "../assets/attribute_icons/_30_business_term.gif";
import _30_column from "../assets/attribute_icons/_30_column.gif";
import _20_column from "../assets/attribute_icons/_20_column.gif";
import _30_computer from "../assets/attribute_icons/_30_computer.gif";
import _30_database from "../assets/attribute_icons/_30_database.gif";
import _30_document from "../assets/attribute_icons/_30_document.gif";
import _30_dot from "../assets/attribute_icons/_30_dot.gif";
import _30_entity from "../assets/attribute_icons/_30_entity.gif";
import _30_folder from "../assets/attribute_icons/_30_folder.gif";
import _20_folder from "../assets/attribute_icons/_20_folder.gif";
import _30_graph from "../assets/attribute_icons/_30_graph.gif";
import _30_green_graph from "../assets/attribute_icons/_30_green_graph.gif";
import _30_interface from "../assets/attribute_icons/_30_interface.gif";
import _30_key from "../assets/attribute_icons/_30_key.gif";
import _30_orange_graph from "../assets/attribute_icons/_30_orange_graph.gif";
import _30_pencil_and_paper from "../assets/attribute_icons/_30_pencil_and_paper.gif";
import _30_person from "../assets/attribute_icons/_30_person.gif";
import _30_red_graph from "../assets/attribute_icons/_30_red_graph.gif";
import _30_report from "../assets/attribute_icons/_30_report.gif";
import _30_role from "../assets/attribute_icons/_30_role.gif";
import _30_schema from "../assets/attribute_icons/_30_schema.gif";
import _30_server from "../assets/attribute_icons/_30_server.gif";
import _30_table from "../assets/attribute_icons/_30_table.gif";
import _30_unit from "../assets/attribute_icons/_30_unit.gif";
import _20_alias_table from "../assets/attribute_icons/_20_alias_table.gif";
import _20_article from "../assets/attribute_icons/_20_article.gif";
import _20_article_folder from "../assets/attribute_icons/_20_article_folder.gif";
import _20_bfolder from "../assets/attribute_icons/_20_bfolder.gif";
import _20_blog_icon from "../assets/attribute_icons/_20_blog_icon.gif";
import _20_blogw_icon from "../assets/attribute_icons/_20_blogw_icon.gif";
import _20_bwarea from "../assets/attribute_icons/_20_bwarea.gif";
import _20_bwbex from "../assets/attribute_icons/_20_bwbex.gif";
import _20_bwcha from "../assets/attribute_icons/_20_bwcha.gif";
import _20_bwcube from "../assets/attribute_icons/_20_bwcube.gif";
import _20_bwiset from "../assets/attribute_icons/_20_bwiset.gif";
import _20_bwkyf from "../assets/attribute_icons/_20_bwkyf.gif";
import _20_bwmpro from "../assets/attribute_icons/_20_bwmpro.gif";
import _20_bwobject from "../assets/attribute_icons/_20_bwobject.gif";
import _20_bwodso from "../assets/attribute_icons/_20_bwodso.gif";
import _20_bwtim from "../assets/attribute_icons/_20_bwtim.gif";
import _20_bwuni from "../assets/attribute_icons/_20_bwuni.gif";
import _20_class from "../assets/attribute_icons/_20_class.gif";
import _20_client from "../assets/attribute_icons/_20_client.gif";
import _20_cognos_column from "../assets/attribute_icons/_20_cognos_column.gif";
import _20_cognos_datasource from "../assets/attribute_icons/_20_cognos_datasource.gif";
import _20_cognos_dimension from "../assets/attribute_icons/_20_cognos_dimension.gif";
import _20_cognos_filter from "../assets/attribute_icons/_20_cognos_filter.gif";
import _20_cognos_folder from "../assets/attribute_icons/_20_cognos_folder.gif";
import _20_cognos_hierarchy from "../assets/attribute_icons/_20_cognos_hierarchy.gif";
import _20_cognos_measure from "../assets/attribute_icons/_20_cognos_measure.gif";
import _20_cognos_model from "../assets/attribute_icons/_20_cognos_model.gif";
import _20_cognos_package from "../assets/attribute_icons/_20_cognos_package.gif";
import _20_cognos_param_map from "../assets/attribute_icons/_20_cognos_param_map.gif";
import _20_cognos_project from "../assets/attribute_icons/_20_cognos_project.gif";
import _20_cognos_report from "../assets/attribute_icons/_20_cognos_report.gif";
import _20_cognos_table from "../assets/attribute_icons/_20_cognos_table.gif";
import _20_columnIDX from "../assets/attribute_icons/_20_columnIDX.gif";
import _20_columnPK from "../assets/attribute_icons/_20_columnPK.gif";
import _20_confluence_blog from "../assets/attribute_icons/_20_confluence_blog.gif";
import _20_confluence_folder from "../assets/attribute_icons/_20_confluence_folder.gif";
import _20_confluence_page from "../assets/attribute_icons/_20_confluence_page.gif";
import _20_confluence_space from "../assets/attribute_icons/_20_confluence_space.gif";
import _20_connection from "../assets/attribute_icons/_20_connection.gif";
import _20_connection_engine_folder from "../assets/attribute_icons/_20_connection_engine_folder.gif";
import _20_connection_folder from "../assets/attribute_icons/_20_connection_folder.gif";
import _20_connection_network_folder from "../assets/attribute_icons/_20_connection_network_folder.gif";
import _20_crystal from "../assets/attribute_icons/_20_crystal.gif";
import _20_derive_table from "../assets/attribute_icons/_20_derive_table.gif";
import _20_detail from "../assets/attribute_icons/_20_detail.gif";
import _20_dimension from "../assets/attribute_icons/_20_dimension.gif";
import _20_dqcalltests from "../assets/attribute_icons/_20_dqcalltests.gif";
import _20_dqcgroup from "../assets/attribute_icons/_20_dqcgroup.gif";
import _20_dqctestf from "../assets/attribute_icons/_20_dqctestf.gif";
import _20_dqctestn from "../assets/attribute_icons/_20_dqctestn.gif";
import _20_dqctests from "../assets/attribute_icons/_20_dqctests.gif";
import _20_dqmfoldercoordination from "../assets/attribute_icons/_20_dqmfoldercoordination.gif";
import _20_dqmgroup from "../assets/attribute_icons/_20_dqmgroup.gif";
import _20_dqmrate from "../assets/attribute_icons/_20_dqmrate.gif";
import _20_dqmrateaccepted from "../assets/attribute_icons/_20_dqmrateaccepted.gif";
import _20_dqmraterejected from "../assets/attribute_icons/_20_dqmraterejected.gif";
import _20_dqmtestfailed from "../assets/attribute_icons/_20_dqmtestfailed.gif";
import _20_dqmtesthalffailed from "../assets/attribute_icons/_20_dqmtesthalffailed.gif";
import _20_dqmtestinactive from "../assets/attribute_icons/_20_dqmtestinactive.gif";
import _20_dqmtestnotstarted from "../assets/attribute_icons/_20_dqmtestnotstarted.gif";
import _20_dqmtestsuccess from "../assets/attribute_icons/_20_dqmtestsuccess.gif";
import _20_excel from "../assets/attribute_icons/_20_excel.gif";
import _20_filtr from "../assets/attribute_icons/_20_filtr.gif";
import _20_flash from "../assets/attribute_icons/_20_flash.gif";
import _20_glosariusz from "../assets/attribute_icons/_20_glosariusz.gif";
import _20_glossary_ncorp from "../assets/attribute_icons/_20_glossary_ncorp.gif";
import _20_glossary_ver from "../assets/attribute_icons/_20_glossary_ver.gif";
import _20_keyword_folder from "../assets/attribute_icons/_20_keyword_folder.gif";
import _20_link from "../assets/attribute_icons/_20_link.gif";
import _20_measure from "../assets/attribute_icons/_20_measure.gif";
import _20_microstrategy_attribute from "../assets/attribute_icons/_20_microstrategy_attribute.gif";
import _20_microstrategy_consolidation from "../assets/attribute_icons/_20_microstrategy_consolidation.gif";
import _20_microstrategy_cube from "../assets/attribute_icons/_20_microstrategy_cube.gif";
import _20_microstrategy_custom_group from "../assets/attribute_icons/_20_microstrategy_custom_group.gif";
import _20_microstrategy_dashboard from "../assets/attribute_icons/_20_microstrategy_dashboard.gif";
import _20_microstrategy_DB_instance from "../assets/attribute_icons/_20_microstrategy_DB_instance.gif";
import _20_microstrategy_document from "../assets/attribute_icons/_20_microstrategy_document.gif";
import _20_microstrategy_drill_map from "../assets/attribute_icons/_20_microstrategy_drill_map.gif";
import _20_microstrategy_fact from "../assets/attribute_icons/_20_microstrategy_fact.gif";
import _20_microstrategy_filter from "../assets/attribute_icons/_20_microstrategy_filter.gif";
import _20_microstrategy_folder from "../assets/attribute_icons/_20_microstrategy_folder.gif";
import _20_microstrategy_hierarchy from "../assets/attribute_icons/_20_microstrategy_hierarchy.gif";
import _20_microstrategy_metric from "../assets/attribute_icons/_20_microstrategy_metric.gif";
import _20_microstrategy_project from "../assets/attribute_icons/_20_microstrategy_project.gif";
import _20_microstrategy_prompt from "../assets/attribute_icons/_20_microstrategy_prompt.gif";
import _20_microstrategy_report from "../assets/attribute_icons/_20_microstrategy_report.gif";
import _20_microstrategy_table from "../assets/attribute_icons/_20_microstrategy_table.gif";
import _20_microstrategy_template from "../assets/attribute_icons/_20_microstrategy_template.gif";
import _20_microstrategy_transformation from "../assets/attribute_icons/_20_microstrategy_transformation.gif";
import _20_mssql_clustred_index from "../assets/attribute_icons/_20_mssql_clustred_index.gif";
import _20_mssql_column from "../assets/attribute_icons/_20_mssql_column.gif";
import _20_mssql_column_FK from "../assets/attribute_icons/_20_mssql_column_FK.gif";
import _20_mssql_column_PK from "../assets/attribute_icons/_20_mssql_column_PK.gif";
import _20_mssql_DB from "../assets/attribute_icons/_20_mssql_DB.gif";
import _20_mssql_folder from "../assets/attribute_icons/_20_mssql_folder.gif";
import _20_mssql_function from "../assets/attribute_icons/_20_mssql_function.gif";
import _20_mssql_non_clustred_index from "../assets/attribute_icons/_20_mssql_non_clustred_index.gif";
import _20_mssql_owner from "../assets/attribute_icons/_20_mssql_owner.gif";
import _20_mssql_procedure from "../assets/attribute_icons/_20_mssql_procedure.gif";
import _20_mssql_server from "../assets/attribute_icons/_20_mssql_server.gif";
import _20_mssql_table from "../assets/attribute_icons/_20_mssql_table.gif";
import _20_mssql_table_function from "../assets/attribute_icons/_20_mssql_table_function.gif";
import _20_mssql_view from "../assets/attribute_icons/_20_mssql_view.gif";
import _20_oracle_BI_analysis from "../assets/attribute_icons/_20_oracle_BI_analysis.gif";
import _20_oracle_BI_analysis_and_reports from "../assets/attribute_icons/_20_oracle_BI_analysis_and_reports.gif";
import _20_oracle_BI_business_layer from "../assets/attribute_icons/_20_oracle_BI_business_layer.gif";
import _20_oracle_BI_column from "../assets/attribute_icons/_20_oracle_BI_column.gif";
import _20_oracle_BI_condition from "../assets/attribute_icons/_20_oracle_BI_condition.gif";
import _20_oracle_BI_dashboard from "../assets/attribute_icons/_20_oracle_BI_dashboard.gif";
import _20_oracle_BI_data_model from "../assets/attribute_icons/_20_oracle_BI_data_model.gif";
import _20_oracle_BI_dimension from "../assets/attribute_icons/_20_oracle_BI_dimension.gif";
import _20_oracle_BI_filter from "../assets/attribute_icons/_20_oracle_BI_filter.gif";
import _20_oracle_BI_folder from "../assets/attribute_icons/_20_oracle_BI_folder.gif";
import _20_oracle_BI_hierarchy from "../assets/attribute_icons/_20_oracle_BI_hierarchy.gif";
import _20_oracle_BI_logical_level from "../assets/attribute_icons/_20_oracle_BI_logical_level.gif";
import _20_oracle_BI_measure from "../assets/attribute_icons/_20_oracle_BI_measure.gif";
import _20_oracle_BI_monit from "../assets/attribute_icons/_20_oracle_BI_monit.gif";
import _20_oracle_BI_physical_layer from "../assets/attribute_icons/_20_oracle_BI_physical_layer.gif";
import _20_oracle_BI_presentation_layer from "../assets/attribute_icons/_20_oracle_BI_presentation_layer.gif";
import _20_oracle_BI_report from "../assets/attribute_icons/_20_oracle_BI_report.gif";
import _20_oracle_BI_reports_published from "../assets/attribute_icons/_20_oracle_BI_reports_published.gif";
import _20_oracle_BI_report_task from "../assets/attribute_icons/_20_oracle_BI_report_task.gif";
import _20_oracle_BI_table from "../assets/attribute_icons/_20_oracle_BI_table.gif";
import _20_oracle_BI_table_fact from "../assets/attribute_icons/_20_oracle_BI_table_fact.gif";
import _20_oracle_BI_template_child from "../assets/attribute_icons/_20_oracle_BI_template_child.gif";
import _20_oracle_BI_template_styles from "../assets/attribute_icons/_20_oracle_BI_template_styles.gif";
import _20_oracle_column from "../assets/attribute_icons/_20_oracle_column.gif";
import _20_oracle_column_IDX from "../assets/attribute_icons/_20_oracle_column_IDX.gif";
import _20_oracle_column_PK from "../assets/attribute_icons/_20_oracle_column_PK.gif";
import _20_oracle_folder from "../assets/attribute_icons/_20_oracle_folder.gif";
import _20_oracle_function from "../assets/attribute_icons/_20_oracle_function.gif";
import _20_oracle_index from "../assets/attribute_icons/_20_oracle_index.gif";
import _20_oracle_package from "../assets/attribute_icons/_20_oracle_package.gif";
import _20_oracle_procedure from "../assets/attribute_icons/_20_oracle_procedure.gif";
import _20_oracle_schema from "../assets/attribute_icons/_20_oracle_schema.gif";
import _20_oracle_server from "../assets/attribute_icons/_20_oracle_server.gif";
import _20_oracle_table from "../assets/attribute_icons/_20_oracle_table.gif";
import _20_oracle_view from "../assets/attribute_icons/_20_oracle_view.gif";
import _20_owner from "../assets/attribute_icons/_20_owner.gif";
import _20_paragraph from "../assets/attribute_icons/_20_paragraph.gif";
import _20_pdf from "../assets/attribute_icons/_20_pdf.gif";
import _20_postgres_column from "../assets/attribute_icons/_20_postgres_column.gif";
import _20_postgres_column_FK from "../assets/attribute_icons/_20_postgres_column_FK.gif";
import _20_postgres_column_PK from "../assets/attribute_icons/_20_postgres_column_PK.gif";
import _20_postgres_database from "../assets/attribute_icons/_20_postgres_database.gif";
import _20_postgres_folder from "../assets/attribute_icons/_20_postgres_folder.gif";
import _20_postgres_function from "../assets/attribute_icons/_20_postgres_function.gif";
import _20_postgres_index from "../assets/attribute_icons/_20_postgres_index.gif";
import _20_postgres_schema from "../assets/attribute_icons/_20_postgres_schema.gif";
import _20_postgres_server from "../assets/attribute_icons/_20_postgres_server.gif";
import _20_postgres_table from "../assets/attribute_icons/_20_postgres_table.gif";
import _20_postgres_view from "../assets/attribute_icons/_20_postgres_view.gif";
import _20_powerpoint from "../assets/attribute_icons/_20_powerpoint.gif";
import _20_procedure from "../assets/attribute_icons/_20_procedure.gif";
import _20_profile_file from "../assets/attribute_icons/_20_profile_file.gif";
import _20_profile_item from "../assets/attribute_icons/_20_profile_item.gif";
import _20_profile_library from "../assets/attribute_icons/_20_profile_library.gif";
import _20_query from "../assets/attribute_icons/_20_query.gif";
import _20_report_folder from "../assets/attribute_icons/_20_report_folder.gif";
import _20_SAS_calculated_measure from "../assets/attribute_icons/_20_SAS_calculated_measure.gif";
import _20_SAS_category from "../assets/attribute_icons/_20_SAS_category.gif";
import _20_SAS_column_char from "../assets/attribute_icons/_20_SAS_column_char.gif";
import _20_SAS_column_date from "../assets/attribute_icons/_20_SAS_column_date.gif";
import _20_SAS_column_num from "../assets/attribute_icons/_20_SAS_column_num.gif";
import _20_SAS_cube from "../assets/attribute_icons/_20_SAS_cube.gif";
import _20_SAS_cube_dimension from "../assets/attribute_icons/_20_SAS_cube_dimension.gif";
import _20_SAS_cube_measures from "../assets/attribute_icons/_20_SAS_cube_measures.gif";
import _20_SAS_dashboard from "../assets/attribute_icons/_20_SAS_dashboard.gif";
import _20_SAS_filter from "../assets/attribute_icons/_20_SAS_filter.gif";
import _20_SAS_folder from "../assets/attribute_icons/_20_SAS_folder.gif";
import _20_SAS_hierarchy from "../assets/attribute_icons/_20_SAS_hierarchy.gif";
import _20_SAS_information_map from "../assets/attribute_icons/_20_SAS_information_map.gif";
import _20_SAS_information_map_cube from "../assets/attribute_icons/_20_SAS_information_map_cube.gif";
import _20_SAS_level from "../assets/attribute_icons/_20_SAS_level.gif";
import _20_SAS_library from "../assets/attribute_icons/_20_SAS_library.gif";
import _20_SAS_measure from "../assets/attribute_icons/_20_SAS_measure.gif";
import _20_SAS_table from "../assets/attribute_icons/_20_SAS_table.gif";
import _20_SAS_web_report_studio from "../assets/attribute_icons/_20_SAS_web_report_studio.gif";
import _20_schedule from "../assets/attribute_icons/_20_schedule.gif";
import _20_scheme from "../assets/attribute_icons/_20_scheme.gif";
import _20_tablearea from "../assets/attribute_icons/_20_tablearea.gif";
import _20_tablefolder from "../assets/attribute_icons/_20_tablefolder.gif";
import _20_universeColumn from "../assets/attribute_icons/_20_universe_column.gif";
import _20_universes from "../assets/attribute_icons/_20_universes.gif";
import _20_universes_folder from "../assets/attribute_icons/_20_universes_folder.gif";
import _20_universe_table from "../assets/attribute_icons/_20_universe_table.gif";
import _20_user_folder from "../assets/attribute_icons/_20_user_folder.gif";
import _20_user_role from "../assets/attribute_icons/_20_user_role.gif";
import _20_users_icon from "../assets/attribute_icons/_20_users_icon.gif";
import _20_user_specialist from "../assets/attribute_icons/_20_user_specialist.gif";
import _20_view from "../assets/attribute_icons/_20_view.gif";
import _20_webi from "../assets/attribute_icons/_20_webi.gif";
import _20_key from "../assets/attribute_icons/_20_key.gif";
import _20_report from "../assets/attribute_icons/_20_report.gif";
import _20_table from "../assets/attribute_icons/_20_table.gif";

export const getAttributeFile = (icon) => {
  if (icon?.startsWith("data:") || icon?.startsWith("http")) {
    const trimmedUrl = icon?.replace(/\s/g, "");
    return `url(${trimmedUrl})`;
  }
  switch (icon) {
    case "_20_query":
      return _20_query;
    case "_20_report_folder":
      return _20_report_folder;
    case "_20_SAS_calculated_measure":
      return _20_SAS_calculated_measure;
    case "_20_SAS_category":
      return _20_SAS_category;
    case "_20_SAS_column_char":
      return _20_SAS_column_char;
    case "_20_SAS_column_date":
      return _20_SAS_column_date;
    case "_20_SAS_column_num":
      return _20_SAS_column_num;
    case "_20_SAS_cube":
      return _20_SAS_cube;
    case "_20_SAS_cube_dimension":
      return _20_SAS_cube_dimension;
    case "_20_SAS_cube_measures":
      return _20_SAS_cube_measures;
    case "_20_SAS_dashboard":
      return _20_SAS_dashboard;
    case "_20_SAS_filter":
      return _20_SAS_filter;
    case "_20_SAS_folder":
      return _20_SAS_folder;
    case "_20_SAS_hierarchy":
      return _20_SAS_hierarchy;
    case "_20_SAS_information_map":
      return _20_SAS_information_map;
    case "_20_SAS_information_map_cube":
      return _20_SAS_information_map_cube;
    case "_20_SAS_level":
      return _20_SAS_level;
    case "_20_SAS_library":
      return _20_SAS_library;
    case "_20_SAS_measure":
      return _20_SAS_measure;
    case "_20_SAS_table":
      return _20_SAS_table;
    case "_20_SAS_web_report_studio":
      return _20_SAS_web_report_studio;
    case "_20_schedule":
      return _20_schedule;
    case "_20_scheme":
      return _20_scheme;
    case "_20_tablearea":
      return _20_tablearea;
    case "_20_tablefolder":
      return _20_tablefolder;
    case "_20_universeColumn":
      return _20_universeColumn;
    case "_20_universes":
      return _20_universes;
    case "_20_universes_folder":
      return _20_universes_folder;
    case "_20_universe_table":
      return _20_universe_table;
    case "_20_user_folder":
      return _20_user_folder;
    case "_20_user_role":
      return _20_user_role;
    case "_20_users_icon":
      return _20_users_icon;
    case "_20_user_specialist":
      return _20_user_specialist;
    case "_20_view":
      return _20_view;
    case "_20_webi":
      return _20_webi;
    case "_20_alias_table":
      return _20_alias_table;
    case "_20_paragraph":
      return _20_paragraph;
    case "_20_pdf":
      return _20_pdf;
    case "_20_postgres_column":
      return _20_postgres_column;
    case "_20_postgres_column_FK":
      return _20_postgres_column_FK;
    case "_20_postgres_column_PK":
      return _20_postgres_column_PK;
    case "_20_postgres_database":
      return _20_postgres_database;
    case "_20_postgres_folder":
      return _20_postgres_folder;
    case "_20_postgres_function":
      return _20_postgres_function;
    case "_20_postgres_index":
      return _20_postgres_index;
    case "_20_postgres_schema":
      return _20_postgres_schema;
    case "_20_postgres_server":
      return _20_postgres_server;
    case "_20_postgres_table":
      return _20_postgres_table;
    case "_20_postgres_view":
      return _20_postgres_view;
    case "_20_powerpoint":
      return _20_powerpoint;
    case "_20_procedure":
      return _20_procedure;
    case "_20_profile_file":
      return _20_profile_file;
    case "_20_profile_item":
      return _20_profile_item;
    case "_20_profile_library":
      return _20_profile_library;

    case "_20_oracle_BI_analysis":
      return _20_oracle_BI_analysis;
    case "_20_oracle_BI_analysis_and_reports":
      return _20_oracle_BI_analysis_and_reports;
    case "_20_oracle_BI_business_layer":
      return _20_oracle_BI_business_layer;
    case "_20_oracle_BI_column":
      return _20_oracle_BI_column;
    case "_20_oracle_BI_condition":
      return _20_oracle_BI_condition;
    case "_20_oracle_BI_dashboard":
      return _20_oracle_BI_dashboard;
    case "_20_oracle_BI_data_model":
      return _20_oracle_BI_data_model;
    case "_20_oracle_BI_dimension":
      return _20_oracle_BI_dimension;
    case "_20_oracle_BI_filter":
      return _20_oracle_BI_filter;
    case "_20_oracle_BI_folder":
      return _20_oracle_BI_folder;
    case "_20_oracle_BI_hierarchy":
      return _20_oracle_BI_hierarchy;
    case "_20_oracle_BI_logical_level":
      return _20_oracle_BI_logical_level;
    case "_20_oracle_BI_measure":
      return _20_oracle_BI_measure;
    case "_20_oracle_BI_monit":
      return _20_oracle_BI_monit;
    case "_20_oracle_BI_physical_layer":
      return _20_oracle_BI_physical_layer;
    case "_20_oracle_BI_presentation_layer":
      return _20_oracle_BI_presentation_layer;
    case "_20_oracle_BI_report":
      return _20_oracle_BI_report;
    case "_20_oracle_BI_reports_published":
      return _20_oracle_BI_reports_published;
    case "_20_oracle_BI_report_task":
      return _20_oracle_BI_report_task;
    case "_20_oracle_BI_table":
      return _20_oracle_BI_table;
    case "_20_oracle_BI_table_fact":
      return _20_oracle_BI_table_fact;
    case "_20_oracle_BI_template_child":
      return _20_oracle_BI_template_child;
    case "_20_oracle_BI_template_styles":
      return _20_oracle_BI_template_styles;
    case "_20_oracle_column":
      return _20_oracle_column;
    case "_20_oracle_column_IDX":
      return _20_oracle_column_IDX;
    case "_20_oracle_column_PK":
      return _20_oracle_column_PK;
    case "_20_oracle_folder":
      return _20_oracle_folder;
    case "_20_oracle_function":
      return _20_oracle_function;
    case "_20_oracle_index":
      return _20_oracle_index;
    case "_20_oracle_package":
      return _20_oracle_package;

    case "_20_oracle_procedure":
      return _20_oracle_procedure;
    case "_20_oracle_schema":
      return _20_oracle_schema;
    case "_20_oracle_server":
      return _20_oracle_server;
    case "_20_oracle_table":
      return _20_oracle_table;
    case "_20_oracle_view":
      return _20_oracle_view;
    case "_20_owner":
      return _20_owner;

    case "_20_measure":
      return _20_measure;

    case "_20_microstrategy_attribute":
      return _20_microstrategy_attribute;
    case "_20_microstrategy_consolidation":
      return _20_microstrategy_consolidation;
    case "_20_microstrategy_cube":
      return _20_microstrategy_cube;
    case "_20_microstrategy_custom_group":
      return _20_microstrategy_custom_group;
    case "_20_microstrategy_dashboard":
      return _20_microstrategy_dashboard;
    case "_20_microstrategy_DB_instance":
      return _20_microstrategy_DB_instance;
    case "_20_microstrategy_document":
      return _20_microstrategy_document;
    case "_20_microstrategy_drill_map":
      return _20_microstrategy_drill_map;
    case "_20_microstrategy_fact":
      return _20_microstrategy_fact;
    case "_20_microstrategy_filter":
      return _20_microstrategy_filter;
    case "_20_microstrategy_folder":
      return _20_microstrategy_folder;
    case "_20_microstrategy_hierarchy":
      return _20_microstrategy_hierarchy;
    case "_20_microstrategy_metric":
      return _20_microstrategy_metric;
    case "_20_microstrategy_project":
      return _20_microstrategy_project;
    case "_20_microstrategy_prompt":
      return _20_microstrategy_prompt;
    case "_20_microstrategy_report":
      return _20_microstrategy_report;
    case "_20_microstrategy_table":
      return _20_microstrategy_table;
    case "_20_microstrategy_template":
      return _20_microstrategy_template;
    case "_20_microstrategy_transformation":
      return _20_microstrategy_transformation;
    case "_20_mssql_clustred_index":
      return _20_mssql_clustred_index;
    case "_20_mssql_column":
      return _20_mssql_column;
    case "_20_mssql_column_FK":
      return _20_mssql_column_FK;
    case "_20_mssql_column_PK":
      return _20_mssql_column_PK;
    case "_20_mssql_DB":
      return _20_mssql_DB;
    case "_20_mssql_folder":
      return _20_mssql_folder;
    case "_20_mssql_function":
      return _20_mssql_function;
    case "_20_mssql_non_clustred_index":
      return _20_mssql_non_clustred_index;
    case "_20_mssql_owner":
      return _20_mssql_owner;
    case "_20_mssql_procedure":
      return _20_mssql_procedure;
    case "_20_mssql_server":
      return _20_mssql_server;
    case "_20_mssql_table":
      return _20_mssql_table;
    case "_20_mssql_table_function":
      return _20_mssql_table_function;
    case "_20_mssql_view":
      return _20_mssql_view;

    case "_20_link":
      return _20_link;
    case "_20_keyword_folder":
      return _20_keyword_folder;

    case "_20_glosariusz":
      return _20_glosariusz;
    case "_20_glossary_ncorp":
      return _20_glossary_ncorp;
    case "_20_glossary_ver":
      return _20_glossary_ver;
    case "_20_filtr":
      return _20_filtr;
    case "_20_flash":
      return _20_flash;
    case "_20_excel":
      return _20_excel;
    case "_20_derive_table":
      return _20_derive_table;
    case "_20_detail":
      return _20_detail;
    case "_20_dimension":
      return _20_dimension;
    case "_20_dqcalltests":
      return _20_dqcalltests;
    case "_20_dqcgroup":
      return _20_dqcgroup;
    case "_20_dqctestf":
      return _20_dqctestf;
    case "_20_dqctestn":
      return _20_dqctestn;
    case "_20_dqctests":
      return _20_dqctests;
    case "_20_dqmfoldercoordination":
      return _20_dqmfoldercoordination;
    case "_20_dqmgroup":
      return _20_dqmgroup;
    case "_20_dqmrate":
      return _20_dqmrate;
    case "_20_dqmrateaccepted":
      return _20_dqmrateaccepted;
    case "_20_dqmraterejected":
      return _20_dqmraterejected;
    case "_20_dqmtestfailed":
      return _20_dqmtestfailed;
    case "_20_dqmtesthalffailed":
      return _20_dqmtesthalffailed;
    case "_20_dqmtestinactive":
      return _20_dqmtestinactive;
    case "_20_dqmtestnotstarted":
      return _20_dqmtestnotstarted;
    case "_20_dqmtestsuccess":
      return _20_dqmtestsuccess;

    case "_20_confluence_blog":
      return _20_confluence_blog;
    case "_20_confluence_folder":
      return _20_confluence_folder;
    case "_20_confluence_page":
      return _20_confluence_page;
    case "_20_confluence_space":
      return _20_confluence_space;
    case "_20_connection":
      return _20_connection;
    case "_20_connection_engine_folder":
      return _20_connection_engine_folder;
    case "_20_connection_folder":
      return _20_connection_folder;
    case "_20_cognos_report":
      return _20_cognos_report;
    case "_20_connection_network_folder":
      return _20_connection_network_folder;
    case "_20_crystal":
      return _20_crystal;

    case "_20_columnIDX":
      return _20_columnIDX;
    case "_20_cognos_table":
      return _20_cognos_table;
    case "_20_columnPK":
      return _20_columnPK;
    case "_20_class":
      return _20_class;
    case "_20_client":
      return _20_client;
    case "_20_bwcube":
      return _20_bwcube;
    case "_20_cognos_column":
      return _20_cognos_column;
    case "_20_cognos_datasource":
      return _20_cognos_datasource;
    case "_20_cognos_dimension":
      return _20_cognos_dimension;
    case "_20_cognos_filter":
      return _20_cognos_filter;
    case "_20_cognos_folder":
      return _20_cognos_folder;
    case "_20_cognos_hierarchy":
      return _20_cognos_hierarchy;
    case "_20_cognos_measure":
      return _20_cognos_measure;
    case "_20_cognos_model":
      return _20_cognos_model;
    case "_20_cognos_package":
      return _20_cognos_package;
    case "_20_cognos_param_map":
      return _20_cognos_param_map;
    case "_20_cognos_project":
      return _20_cognos_project;

    case "_20_article":
      return _20_article;
    case "_20_article_folder":
      return _20_article_folder;
    case "_20_bfolder":
      return _20_bfolder;
    case "_20_blog_icon":
      return _20_blog_icon;
    case "_20_blogw_icon":
      return _20_blogw_icon;
    case "_20_bwarea":
      return _20_bwarea;
    case "_20_bwbex":
      return _20_bwbex;
    case "_20_bwcha":
      return _20_bwcha;

    case "_20_bwiset":
      return _20_bwiset;
    case "_20_bwkyf":
      return _20_bwkyf;
    case "_20_bwmpro":
      return _20_bwmpro;
    case "_20_bwobject":
      return _20_bwobject;

    case "_20_bwodso":
      return _20_bwodso;
    case "_20_bwtim":
      return _20_bwtim;
    case "_20_bwuni":
      return _20_bwuni;
    case "_20_attribute":
      return _20_attribute;
    case "_30_attribute":
      return _30_attribute;

    case "_30_business_process":
      return _30_busines_process;
    case "_30_business_term":
      return _30_business_term;
    case "_20_column":
      return _20_column;
    case "_30_column":
      return _30_column;
    case "_30_computer":
      return _30_computer;
    case "_30_database":
      return _30_database;
    case "_30_dot":
      return _30_dot;
    case "_30_entity":
      return _30_entity;
    case "_20_folder":
      return _20_folder;
    case "_30_folder":
      return _30_folder;
    case "_30_graph":
      return _30_graph;
    case "_30_green_graph":
      return _30_green_graph;
    case "_30_interface":
      return _30_interface;
    case "_20_key":
      return _20_key;
    case "_30_key":
      return _30_key;
    case "_30_orange_graph":
      return _30_orange_graph;
    case "_30_pencil_and_paper":
      return _30_pencil_and_paper;
    case "_30_person":
      return _30_person;
    case "_30_red_graph":
      return _30_red_graph;
    case "_20_report":
      return _20_report;
    case "_30_report":
      return _30_report;
    case "_30_document":
      return _30_document;
    case "_30_role":
      return _30_role;
    case "_30_schema":
      return _30_schema;
    case "_30_server":
      return _30_server;
    case "_30_table":
      return _30_table;
    case "_20_table":
      return _20_table;
    case "_30_unit":
      return _30_unit;
    default:
      return _30_folder;
  }
};

export const getAttributeIcon = (icon) => {
  if (icon?.startsWith("http") || icon?.startsWith("data:")) {
    return <img src={icon} width={22} height={22} />;
  }
  const selectedIcon = ATTRIBUTE_ICONS.find((item) => item.value === icon);
  if (selectedIcon) {
    return selectedIcon.icon;
  }
  return <img src={_30_folder} width={22} height={22} />;
};

export const _30_ATTRIBUTE_ICONS = [
  {
    icon: <img src={_30_attribute} width={22} height={22} />,
    value: "_30_attribute",
  },
  {
    icon: <img src={_30_busines_process} width={22} height={22} />,
    value: "_30_business_process",
  },
  {
    icon: <img src={_30_business_term} width={22} height={22} />,
    value: "_30_business_term",
  },
  {
    icon: <img src={_30_column} width={22} height={22} />,
    value: "_30_column",
  },
  {
    icon: <img src={_30_computer} width={22} height={22} />,
    value: "_30_computer",
  },
  {
    icon: <img src={_30_database} width={22} height={22} />,
    value: "_30_database",
  },
  {
    icon: <img src={_30_document} width={22} height={22} />,
    value: "_30_document",
  },

  {
    icon: <img src={_30_dot} width={22} height={22} />,
    value: "_30_dot",
  },
  {
    icon: <img src={_30_entity} width={22} height={22} />,
    value: "_30_entity",
  },
  {
    icon: <img src={_30_folder} width={22} height={22} />,
    value: "_30_folder",
  },
  {
    icon: <img src={_30_graph} width={22} height={22} />,
    value: "_30_graph",
  },
  {
    icon: <img src={_30_green_graph} width={22} height={22} />,
    value: "_30_green_graph",
  },
  {
    icon: <img src={_30_interface} width={22} height={22} />,
    value: "_30_interface",
  },
  {
    icon: <img src={_30_key} width={22} height={22} />,
    value: "_30_key",
  },
  {
    icon: <img src={_30_orange_graph} width={22} height={22} />,
    value: "_30_orange_graph",
  },
  {
    icon: <img src={_30_pencil_and_paper} width={22} height={22} />,
    value: "_30_pencil_and_paper",
  },
  {
    icon: <img src={_30_person} width={22} height={22} />,
    value: "_30_person",
  },
  {
    icon: <img src={_30_red_graph} width={22} height={22} />,
    value: "_30_red_graph",
  },
  {
    icon: <img src={_30_report} width={22} height={22} />,
    value: "_30_report",
  },
  {
    icon: <img src={_30_role} width={22} height={22} />,
    value: "_30_role",
  },
  {
    icon: <img src={_30_schema} width={22} height={22} />,
    value: "_30_schema",
  },
  {
    icon: <img src={_30_server} width={22} height={22} />,
    value: "_30_server",
  },
  {
    icon: <img src={_30_table} width={22} height={22} />,
    value: "_30_table",
  },
  {
    icon: <img src={_30_unit} width={22} height={22} />,
    value: "_30_unit",
  },
];

export const _20_ATTRIBUTE_ICONS = [
  {
    icon: <img src={_20_query} width={22} height={22} />,
    value: "_20_query",
  },
  {
    icon: <img src={_20_report_folder} width={22} height={22} />,
    value: "_20_report_folder",
  },
  {
    icon: <img src={_20_SAS_calculated_measure} width={22} height={22} />,
    value: "_20_SAS_calculated_measure",
  },

  {
    icon: <img src={_20_SAS_category} width={22} height={22} />,
    value: "_20_SAS_category",
  },

  {
    icon: <img src={_20_SAS_column_char} width={22} height={22} />,
    value: "_20_SAS_column_char",
  },
  {
    icon: <img src={_20_SAS_column_date} width={22} height={22} />,
    value: "_20_SAS_column_date",
  },
  {
    icon: <img src={_20_SAS_column_num} width={22} height={22} />,
    value: "_20_SAS_column_num",
  },
  {
    icon: <img src={_20_SAS_cube} width={22} height={22} />,
    value: "_20_SAS_cube",
  },
  {
    icon: <img src={_20_SAS_cube_dimension} width={22} height={22} />,
    value: "_20_SAS_cube_dimension",
  },

  {
    icon: <img src={_20_SAS_cube_measures} width={22} height={22} />,
    value: "_20_SAS_cube_measures",
  },
  {
    icon: <img src={_20_SAS_dashboard} width={22} height={22} />,
    value: "_20_SAS_dashboard",
  },
  {
    icon: <img src={_20_SAS_filter} width={22} height={22} />,
    value: "_20_SAS_filter",
  },
  {
    icon: <img src={_20_SAS_folder} width={22} height={22} />,
    value: "_20_SAS_folder",
  },

  {
    icon: <img src={_20_SAS_hierarchy} width={22} height={22} />,
    value: "_20_SAS_hierarchy",
  },

  {
    icon: <img src={_20_SAS_information_map} width={22} height={22} />,
    value: "_20_SAS_information_map",
  },
  {
    icon: <img src={_20_SAS_information_map_cube} width={22} height={22} />,
    value: "_20_SAS_information_map_cube",
  },
  {
    icon: <img src={_20_SAS_level} width={22} height={22} />,
    value: "_20_SAS_level",
  },
  {
    icon: <img src={_20_SAS_library} width={22} height={22} />,
    value: "_20_SAS_library",
  },
  {
    icon: <img src={_20_SAS_measure} width={22} height={22} />,
    value: "_20_SAS_measure",
  },

  {
    icon: <img src={_20_SAS_table} width={22} height={22} />,
    value: "_20_SAS_table",
  },
  {
    icon: <img src={_20_SAS_web_report_studio} width={22} height={22} />,
    value: "_20_SAS_web_report_studio",
  },
  {
    icon: <img src={_20_schedule} width={22} height={22} />,
    value: "_20_schedule",
  },
  {
    icon: <img src={_20_scheme} width={22} height={22} />,
    value: "_20_scheme",
  },
  {
    icon: <img src={_20_tablearea} width={22} height={22} />,
    value: "_20_tablearea",
  },
  {
    icon: <img src={_20_tablefolder} width={22} height={22} />,
    value: "_20_tablefolder",
  },
  {
    icon: <img src={_20_universeColumn} width={22} height={22} />,
    value: "_20_universeColumn",
  },
  {
    icon: <img src={_20_universes} width={22} height={22} />,
    value: "_20_universes",
  },
  {
    icon: <img src={_20_universes_folder} width={22} height={22} />,
    value: "_20_universes_folder",
  },
  {
    icon: <img src={_20_universe_table} width={22} height={22} />,
    value: "_20_universe_table",
  },
  {
    icon: <img src={_20_user_folder} width={22} height={22} />,
    value: "_20_user_folder",
  },
  {
    icon: <img src={_20_user_role} width={22} height={22} />,
    value: "_20_user_role",
  },
  {
    icon: <img src={_20_users_icon} width={22} height={22} />,
    value: "_20_users_icon",
  },
  {
    icon: <img src={_20_user_specialist} width={22} height={22} />,
    value: "_20_user_specialist",
  },
  {
    icon: <img src={_20_view} width={22} height={22} />,
    value: "_20_view",
  },

  {
    icon: <img src={_20_webi} width={22} height={22} />,
    value: "_20_webi",
  },

  {
    icon: <img src={_20_paragraph} width={22} height={22} />,
    value: "_20_paragraph",
  },
  {
    icon: <img src={_20_pdf} width={22} height={22} />,
    value: "_20_pdf",
  },

  {
    icon: <img src={_20_postgres_column} width={22} height={22} />,
    value: "_20_postgres_column",
  },
  {
    icon: <img src={_20_postgres_column_FK} width={22} height={22} />,
    value: "_20_postgres_column_FK",
  },

  {
    icon: <img src={_20_postgres_column_PK} width={22} height={22} />,
    value: "_20_postgres_column_PK",
  },

  {
    icon: <img src={_20_postgres_database} width={22} height={22} />,
    value: "_20_postgres_database",
  },

  {
    icon: <img src={_20_postgres_folder} width={22} height={22} />,
    value: "_20_postgres_folder",
  },

  {
    icon: <img src={_20_postgres_function} width={22} height={22} />,
    value: "_20_postgres_function",
  },
  {
    icon: <img src={_20_postgres_index} width={22} height={22} />,
    value: "_20_postgres_index",
  },
  {
    icon: <img src={_20_postgres_schema} width={22} height={22} />,
    value: "_20_postgres_schema",
  },
  {
    icon: <img src={_20_postgres_server} width={22} height={22} />,
    value: "_20_postgres_server",
  },
  {
    icon: <img src={_20_postgres_table} width={22} height={22} />,
    value: "_20_postgres_table",
  },
  {
    icon: <img src={_20_postgres_view} width={22} height={22} />,
    value: "_20_postgres_view",
  },
  {
    icon: <img src={_20_powerpoint} width={22} height={22} />,
    value: "_20_powerpoint",
  },
  {
    icon: <img src={_20_procedure} width={22} height={22} />,
    value: "_20_procedure",
  },
  {
    icon: <img src={_20_profile_file} width={22} height={22} />,
    value: "_20_profile_file",
  },

  {
    icon: <img src={_20_profile_item} width={22} height={22} />,
    value: "_20_profile_item",
  },
  {
    icon: <img src={_20_profile_library} width={22} height={22} />,
    value: "_20_profile_library",
  },

  {
    icon: <img src={_20_oracle_BI_analysis} width={22} height={22} />,
    value: "_20_oracle_BI_analysis",
  },
  {
    icon: (
      <img src={_20_oracle_BI_analysis_and_reports} width={22} height={22} />
    ),
    value: "_20_oracle_BI_analysis_and_reports",
  },
  {
    icon: <img src={_20_oracle_BI_business_layer} width={22} height={22} />,
    value: "_20_oracle_BI_business_layer",
  },
  {
    icon: <img src={_20_oracle_BI_column} width={22} height={22} />,
    value: "_20_oracle_BI_column",
  },
  {
    icon: <img src={_20_oracle_BI_condition} width={22} height={22} />,
    value: "_20_oracle_BI_condition",
  },
  {
    icon: <img src={_20_oracle_BI_dashboard} width={22} height={22} />,
    value: "_20_oracle_BI_dashboard",
  },
  {
    icon: <img src={_20_oracle_BI_data_model} width={22} height={22} />,
    value: "_20_oracle_BI_data_model",
  },
  {
    icon: <img src={_20_oracle_BI_dimension} width={22} height={22} />,
    value: "_20_oracle_BI_dimension",
  },

  {
    icon: <img src={_20_oracle_BI_filter} width={22} height={22} />,
    value: "_20_oracle_BI_filter",
  },

  {
    icon: <img src={_20_oracle_BI_folder} width={22} height={22} />,
    value: "_20_oracle_BI_folder",
  },
  {
    icon: <img src={_20_oracle_BI_hierarchy} width={22} height={22} />,
    value: "_20_oracle_BI_hierarchy",
  },
  {
    icon: <img src={_20_oracle_BI_logical_level} width={22} height={22} />,
    value: "_20_oracle_BI_logical_level",
  },
  {
    icon: <img src={_20_oracle_BI_measure} width={22} height={22} />,
    value: "_20_oracle_BI_measure",
  },
  {
    icon: <img src={_20_oracle_BI_monit} width={22} height={22} />,
    value: "_20_oracle_BI_monit",
  },
  {
    icon: <img src={_20_oracle_BI_physical_layer} width={22} height={22} />,
    value: "_20_oracle_BI_physical_layer",
  },
  {
    icon: <img src={_20_oracle_BI_presentation_layer} width={22} height={22} />,
    value: "_20_oracle_BI_presentation_layer",
  },
  {
    icon: <img src={_20_oracle_BI_report} width={22} height={22} />,
    value: "_20_oracle_BI_report",
  },
  {
    icon: <img src={_20_oracle_BI_reports_published} width={22} height={22} />,
    value: "_20_oracle_BI_reports_published",
  },
  {
    icon: <img src={_20_oracle_BI_report_task} width={22} height={22} />,
    value: "_20_oracle_BI_report_task",
  },
  {
    icon: <img src={_20_oracle_BI_table} width={22} height={22} />,
    value: "_20_oracle_BI_table",
  },
  {
    icon: <img src={_20_oracle_BI_table_fact} width={22} height={22} />,
    value: "_20_oracle_BI_table_fact",
  },
  {
    icon: <img src={_20_oracle_BI_template_child} width={22} height={22} />,
    value: "_20_oracle_BI_template_child",
  },
  {
    icon: <img src={_20_oracle_BI_template_styles} width={22} height={22} />,
    value: "_20_oracle_BI_template_styles",
  },
  {
    icon: <img src={_20_oracle_column} width={22} height={22} />,
    value: "_20_oracle_column",
  },
  {
    icon: <img src={_20_oracle_column_IDX} width={22} height={22} />,
    value: "_20_oracle_column_IDX",
  },
  {
    icon: <img src={_20_oracle_column_PK} width={22} height={22} />,
    value: "_20_oracle_column_PK",
  },
  {
    icon: <img src={_20_oracle_folder} width={22} height={22} />,
    value: "_20_oracle_folder",
  },
  {
    icon: <img src={_20_oracle_function} width={22} height={22} />,
    value: "_20_oracle_function",
  },
  {
    icon: <img src={_20_oracle_index} width={22} height={22} />,
    value: "_20_oracle_index",
  },

  {
    icon: <img src={_20_oracle_package} width={22} height={22} />,
    value: "_20_oracle_package",
  },
  {
    icon: <img src={_20_oracle_procedure} width={22} height={22} />,
    value: "_20_oracle_procedure",
  },

  {
    icon: <img src={_20_oracle_schema} width={22} height={22} />,
    value: "_20_oracle_schema",
  },
  {
    icon: <img src={_20_oracle_server} width={22} height={22} />,
    value: "_20_oracle_server",
  },
  {
    icon: <img src={_20_oracle_table} width={22} height={22} />,
    value: "_20_oracle_table",
  },
  {
    icon: <img src={_20_oracle_view} width={22} height={22} />,
    value: "_20_oracle_view",
  },
  {
    icon: <img src={_20_owner} width={22} height={22} />,
    value: "_20_owner",
  },

  {
    icon: <img src={_20_measure} width={22} height={22} />,
    value: "_20_measure",
  },

  {
    icon: <img src={_20_microstrategy_attribute} width={22} height={22} />,
    value: "_20_microstrategy_attribute",
  },
  {
    icon: <img src={_20_microstrategy_consolidation} width={22} height={22} />,
    value: "_20_microstrategy_consolidation",
  },

  {
    icon: <img src={_20_microstrategy_cube} width={22} height={22} />,
    value: "_20_microstrategy_cube",
  },
  {
    icon: <img src={_20_microstrategy_custom_group} width={22} height={22} />,
    value: "_20_microstrategy_custom_group",
  },
  {
    icon: <img src={_20_microstrategy_dashboard} width={22} height={22} />,
    value: "_20_microstrategy_dashboard",
  },
  {
    icon: <img src={_20_microstrategy_DB_instance} width={22} height={22} />,
    value: "_20_microstrategy_DB_instance",
  },
  {
    icon: <img src={_20_microstrategy_document} width={22} height={22} />,
    value: "_20_microstrategy_document",
  },
  {
    icon: <img src={_20_microstrategy_drill_map} width={22} height={22} />,
    value: "_20_microstrategy_drill_map",
  },
  {
    icon: <img src={_20_microstrategy_fact} width={22} height={22} />,
    value: "_20_microstrategy_fact",
  },
  {
    icon: <img src={_20_microstrategy_filter} width={22} height={22} />,
    value: "_20_microstrategy_filter",
  },

  {
    icon: <img src={_20_microstrategy_folder} width={22} height={22} />,
    value: "_20_microstrategy_folder",
  },

  {
    icon: <img src={_20_microstrategy_hierarchy} width={22} height={22} />,
    value: "_20_microstrategy_hierarchy",
  },
  {
    icon: <img src={_20_microstrategy_metric} width={22} height={22} />,
    value: "_20_microstrategy_metric",
  },

  {
    icon: <img src={_20_microstrategy_project} width={22} height={22} />,
    value: "_20_microstrategy_project",
  },

  {
    icon: <img src={_20_microstrategy_prompt} width={22} height={22} />,
    value: "_20_microstrategy_prompt",
  },
  {
    icon: <img src={_20_microstrategy_report} width={22} height={22} />,
    value: "_20_microstrategy_report",
  },
  {
    icon: <img src={_20_microstrategy_table} width={22} height={22} />,
    value: "_20_microstrategy_table",
  },
  {
    icon: <img src={_20_microstrategy_template} width={22} height={22} />,
    value: "_20_microstrategy_template",
  },
  {
    icon: <img src={_20_microstrategy_transformation} width={22} height={22} />,
    value: "_20_microstrategy_transformation",
  },

  {
    icon: <img src={_20_mssql_clustred_index} width={22} height={22} />,
    value: "_20_mssql_clustred_index",
  },
  {
    icon: <img src={_20_mssql_column} width={22} height={22} />,
    value: "_20_mssql_column",
  },
  {
    icon: <img src={_20_mssql_column_FK} width={22} height={22} />,
    value: "_20_mssql_column_FK",
  },
  {
    icon: <img src={_20_mssql_column_PK} width={22} height={22} />,
    value: "_20_mssql_column_PK",
  },

  {
    icon: <img src={_20_mssql_DB} width={22} height={22} />,
    value: "_20_mssql_DB",
  },
  {
    icon: <img src={_20_mssql_folder} width={22} height={22} />,
    value: "_20_mssql_folder",
  },
  {
    icon: <img src={_20_mssql_function} width={22} height={22} />,
    value: "_20_mssql_function",
  },
  {
    icon: <img src={_20_mssql_non_clustred_index} width={22} height={22} />,
    value: "_20_mssql_non_clustred_index",
  },
  {
    icon: <img src={_20_mssql_owner} width={22} height={22} />,
    value: "_20_mssql_owner",
  },

  {
    icon: <img src={_20_mssql_procedure} width={22} height={22} />,
    value: "_20_mssql_procedure",
  },
  {
    icon: <img src={_20_mssql_server} width={22} height={22} />,
    value: "_20_mssql_server",
  },
  {
    icon: <img src={_20_mssql_table} width={22} height={22} />,
    value: "_20_mssql_table",
  },
  {
    icon: <img src={_20_mssql_table_function} width={22} height={22} />,
    value: "_20_mssql_table_function",
  },
  {
    icon: <img src={_20_mssql_view} width={22} height={22} />,
    value: "_20_mssql_view",
  },

  {
    icon: <img src={_20_link} width={22} height={22} />,
    value: "_20_link",
  },
  {
    icon: <img src={_20_keyword_folder} width={22} height={22} />,
    value: "_20_keyword_folder",
  },

  {
    icon: <img src={_20_glosariusz} width={22} height={22} />,
    value: "_20_glosariusz",
  },
  {
    icon: <img src={_20_glossary_ncorp} width={22} height={22} />,
    value: "_20_glossary_ncorp",
  },
  {
    icon: <img src={_20_glossary_ver} width={22} height={22} />,
    value: "_20_glossary_ver",
  },

  {
    icon: <img src={_20_filtr} width={22} height={22} />,
    value: "_20_filtr",
  },
  {
    icon: <img src={_20_flash} width={22} height={22} />,
    value: "_20_flash",
  },

  {
    icon: <img src={_20_excel} width={22} height={22} />,
    value: "_20_excel",
  },
  {
    icon: <img src={_20_derive_table} width={22} height={22} />,
    value: "_20_derive_table",
  },
  {
    icon: <img src={_20_detail} width={22} height={22} />,
    value: "_20_detail",
  },
  {
    icon: <img src={_20_dimension} width={22} height={22} />,
    value: "_20_dimension",
  },
  {
    icon: <img src={_20_dqcalltests} width={22} height={22} />,
    value: "_20_dqcalltests",
  },
  {
    icon: <img src={_20_dqcgroup} width={22} height={22} />,
    value: "_20_dqcgroup",
  },
  {
    icon: <img src={_20_dqctestf} width={22} height={22} />,
    value: "_20_dqctestf",
  },
  {
    icon: <img src={_20_dqctestn} width={22} height={22} />,
    value: "_20_dqctestn",
  },

  {
    icon: <img src={_20_dqctests} width={22} height={22} />,
    value: "_20_dqctests",
  },
  {
    icon: <img src={_20_dqmfoldercoordination} width={22} height={22} />,
    value: "_20_dqmfoldercoordination",
  },
  {
    icon: <img src={_20_dqmgroup} width={22} height={22} />,
    value: "_20_dqmgroup",
  },
  {
    icon: <img src={_20_dqmrate} width={22} height={22} />,
    value: "_20_dqmrate",
  },
  {
    icon: <img src={_20_dqmrateaccepted} width={22} height={22} />,
    value: "_20_dqmrateaccepted",
  },
  {
    icon: <img src={_20_dqmraterejected} width={22} height={22} />,
    value: "_20_dqmraterejected",
  },
  {
    icon: <img src={_20_dqmtestfailed} width={22} height={22} />,
    value: "_20_dqmtestfailed",
  },
  {
    icon: <img src={_20_dqmtesthalffailed} width={22} height={22} />,
    value: "_20_dqmtesthalffailed",
  },
  {
    icon: <img src={_20_dqmtestinactive} width={22} height={22} />,
    value: "_20_dqmtestinactive",
  },
  {
    icon: <img src={_20_dqmtestnotstarted} width={22} height={22} />,
    value: "_20_dqmtestnotstarted",
  },
  {
    icon: <img src={_20_dqmtestsuccess} width={22} height={22} />,
    value: "_20_dqmtestsuccess",
  },

  {
    icon: <img src={_20_confluence_blog} width={22} height={22} />,
    value: "_20_confluence_blog",
  },
  {
    icon: <img src={_20_confluence_folder} width={22} height={22} />,
    value: "_20_confluence_folder",
  },
  {
    icon: <img src={_20_confluence_page} width={22} height={22} />,
    value: "_20_confluence_page",
  },
  {
    icon: <img src={_20_confluence_space} width={22} height={22} />,
    value: "_20_confluence_space",
  },
  {
    icon: <img src={_20_connection} width={22} height={22} />,
    value: "_20_connection",
  },
  {
    icon: <img src={_20_connection_engine_folder} width={22} height={22} />,
    value: "_20_connection_engine_folder",
  },
  {
    icon: <img src={_20_connection_folder} width={22} height={22} />,
    value: "_20_connection_folder",
  },

  {
    icon: <img src={_20_cognos_report} width={22} height={22} />,
    value: "_20_cognos_report",
  },

  {
    icon: <img src={_20_connection_network_folder} width={22} height={22} />,
    value: "_20_connection_network_folder",
  },

  {
    icon: <img src={_20_crystal} width={22} height={22} />,
    value: "_20_crystal",
  },
  {
    icon: <img src={_20_columnIDX} width={22} height={22} />,
    value: "_20_columnIDX",
  },
  {
    icon: <img src={_20_cognos_table} width={22} height={22} />,
    value: "_20_cognos_table",
  },
  {
    icon: <img src={_20_columnPK} width={22} height={22} />,
    value: "_20_columnPK",
  },
  {
    icon: <img src={_20_class} width={22} height={22} />,
    value: "_20_class",
  },
  {
    icon: <img src={_20_client} width={22} height={22} />,
    value: "_20_client",
  },
  {
    icon: <img src={_20_bwcube} width={22} height={22} />,
    value: "_20_bwcube",
  },
  {
    icon: <img src={_20_cognos_column} width={22} height={22} />,
    value: "_20_cognos_column",
  },
  {
    icon: <img src={_20_cognos_datasource} width={22} height={22} />,
    value: "_20_cognos_datasource",
  },

  {
    icon: <img src={_20_cognos_dimension} width={22} height={22} />,
    value: "_20_cognos_dimension",
  },
  {
    icon: <img src={_20_cognos_filter} width={22} height={22} />,
    value: "_20_cognos_filter",
  },

  {
    icon: <img src={_20_cognos_folder} width={22} height={22} />,
    value: "_20_cognos_folder",
  },
  {
    icon: <img src={_20_cognos_hierarchy} width={22} height={22} />,
    value: "_20_cognos_hierarchy",
  },
  {
    icon: <img src={_20_cognos_measure} width={22} height={22} />,
    value: "_20_cognos_measure",
  },

  {
    icon: <img src={_20_cognos_model} width={22} height={22} />,
    value: "_20_cognos_model",
  },
  {
    icon: <img src={_20_cognos_package} width={22} height={22} />,
    value: "_20_cognos_package",
  },
  {
    icon: <img src={_20_cognos_param_map} width={22} height={22} />,
    value: "_20_cognos_param_map",
  },
  {
    icon: <img src={_20_cognos_project} width={22} height={22} />,
    value: "_20_cognos_project",
  },

  {
    icon: <img src={_20_article} width={22} height={22} />,
    value: "_20_article",
  },

  {
    icon: <img src={_20_article_folder} width={22} height={22} />,
    value: "_20_article_folder",
  },

  {
    icon: <img src={_20_bfolder} width={22} height={22} />,
    value: "_20_bfolder",
  },

  {
    icon: <img src={_20_blog_icon} width={22} height={22} />,
    value: "_20_blog_icon",
  },

  {
    icon: <img src={_20_blogw_icon} width={22} height={22} />,
    value: "_20_blogw_icon",
  },
  {
    icon: <img src={_20_bwarea} width={22} height={22} />,
    value: "_20_bwarea",
  },
  {
    icon: <img src={_20_bwbex} width={22} height={22} />,
    value: "_20_bwbex",
  },

  {
    icon: <img src={_20_bwcha} width={22} height={22} />,
    value: "_20_bwcha",
  },

  {
    icon: <img src={_20_bwiset} width={22} height={22} />,
    value: "_20_bwiset",
  },
  {
    icon: <img src={_20_bwkyf} width={22} height={22} />,
    value: "_20_bwkyf",
  },
  {
    icon: <img src={_20_bwmpro} width={22} height={22} />,
    value: "_20_bwmpro",
  },
  {
    icon: <img src={_20_bwobject} width={22} height={22} />,
    value: "_20_bwobject",
  },
  {
    icon: <img src={_20_bwodso} width={22} height={22} />,
    value: "_20_bwodso",
  },

  {
    icon: <img src={_20_bwtim} width={22} height={22} />,
    value: "_20_bwtim",
  },
  {
    icon: <img src={_20_bwuni} width={22} height={22} />,
    value: "_20_bwuni",
  },

  {
    icon: <img src={_20_alias_table} width={22} height={22} />,
    value: "_20_alias_table",
  },

  {
    icon: <img src={_20_attribute} width={22} height={22} />,
    value: "_20_attribute",
  },

  {
    icon: <img src={_20_column} width={22} height={22} />,
    value: "_20_column",
  },

  {
    icon: <img src={_20_folder} width={22} height={22} />,
    value: "_20_folder",
  },

  {
    icon: <img src={_20_key} width={22} height={22} />,
    value: "_20_key",
  },

  {
    icon: <img src={_20_report} width={22} height={22} />,
    value: "_20_report",
  },

  {
    icon: <img src={_20_table} width={22} height={22} />,
    value: "_20_table",
  },
];
export const ATTRIBUTE_ICONS = [
  {
    icon: <img src={_20_query} width={22} height={22} />,
    value: "_20_query",
  },
  {
    icon: <img src={_20_report_folder} width={22} height={22} />,
    value: "_20_report_folder",
  },
  {
    icon: <img src={_20_SAS_calculated_measure} width={22} height={22} />,
    value: "_20_SAS_calculated_measure",
  },

  {
    icon: <img src={_20_SAS_category} width={22} height={22} />,
    value: "_20_SAS_category",
  },

  {
    icon: <img src={_20_SAS_column_char} width={22} height={22} />,
    value: "_20_SAS_column_char",
  },
  {
    icon: <img src={_20_SAS_column_date} width={22} height={22} />,
    value: "_20_SAS_column_date",
  },
  {
    icon: <img src={_20_SAS_column_num} width={22} height={22} />,
    value: "_20_SAS_column_num",
  },
  {
    icon: <img src={_20_SAS_cube} width={22} height={22} />,
    value: "_20_SAS_cube",
  },
  {
    icon: <img src={_20_SAS_cube_dimension} width={22} height={22} />,
    value: "_20_SAS_cube_dimension",
  },

  {
    icon: <img src={_20_SAS_cube_measures} width={22} height={22} />,
    value: "_20_SAS_cube_measures",
  },
  {
    icon: <img src={_20_SAS_dashboard} width={22} height={22} />,
    value: "_20_SAS_dashboard",
  },
  {
    icon: <img src={_20_SAS_filter} width={22} height={22} />,
    value: "_20_SAS_filter",
  },
  {
    icon: <img src={_20_SAS_folder} width={22} height={22} />,
    value: "_20_SAS_folder",
  },

  {
    icon: <img src={_20_SAS_hierarchy} width={22} height={22} />,
    value: "_20_SAS_hierarchy",
  },

  {
    icon: <img src={_20_SAS_information_map} width={22} height={22} />,
    value: "_20_SAS_information_map",
  },
  {
    icon: <img src={_20_SAS_information_map_cube} width={22} height={22} />,
    value: "_20_SAS_information_map_cube",
  },
  {
    icon: <img src={_20_SAS_level} width={22} height={22} />,
    value: "_20_SAS_level",
  },
  {
    icon: <img src={_20_SAS_library} width={22} height={22} />,
    value: "_20_SAS_library",
  },
  {
    icon: <img src={_20_SAS_measure} width={22} height={22} />,
    value: "_20_SAS_measure",
  },

  {
    icon: <img src={_20_SAS_table} width={22} height={22} />,
    value: "_20_SAS_table",
  },
  {
    icon: <img src={_20_SAS_web_report_studio} width={22} height={22} />,
    value: "_20_SAS_web_report_studio",
  },
  {
    icon: <img src={_20_schedule} width={22} height={22} />,
    value: "_20_schedule",
  },
  {
    icon: <img src={_20_scheme} width={22} height={22} />,
    value: "_20_scheme",
  },
  {
    icon: <img src={_20_tablearea} width={22} height={22} />,
    value: "_20_tablearea",
  },
  {
    icon: <img src={_20_tablefolder} width={22} height={22} />,
    value: "_20_tablefolder",
  },
  {
    icon: <img src={_20_universeColumn} width={22} height={22} />,
    value: "_20_universeColumn",
  },
  {
    icon: <img src={_20_universes} width={22} height={22} />,
    value: "_20_universes",
  },
  {
    icon: <img src={_20_universes_folder} width={22} height={22} />,
    value: "_20_universes_folder",
  },
  {
    icon: <img src={_20_universe_table} width={22} height={22} />,
    value: "_20_universe_table",
  },
  {
    icon: <img src={_20_user_folder} width={22} height={22} />,
    value: "_20_user_folder",
  },
  {
    icon: <img src={_20_user_role} width={22} height={22} />,
    value: "_20_user_role",
  },
  {
    icon: <img src={_20_users_icon} width={22} height={22} />,
    value: "_20_users_icon",
  },
  {
    icon: <img src={_20_user_specialist} width={22} height={22} />,
    value: "_20_user_specialist",
  },
  {
    icon: <img src={_20_view} width={22} height={22} />,
    value: "_20_view",
  },

  {
    icon: <img src={_20_webi} width={22} height={22} />,
    value: "_20_webi",
  },

  {
    icon: <img src={_20_paragraph} width={22} height={22} />,
    value: "_20_paragraph",
  },
  {
    icon: <img src={_20_pdf} width={22} height={22} />,
    value: "_20_pdf",
  },

  {
    icon: <img src={_20_postgres_column} width={22} height={22} />,
    value: "_20_postgres_column",
  },
  {
    icon: <img src={_20_postgres_column_FK} width={22} height={22} />,
    value: "_20_postgres_column_FK",
  },

  {
    icon: <img src={_20_postgres_column_PK} width={22} height={22} />,
    value: "_20_postgres_column_PK",
  },

  {
    icon: <img src={_20_postgres_database} width={22} height={22} />,
    value: "_20_postgres_database",
  },

  {
    icon: <img src={_20_postgres_folder} width={22} height={22} />,
    value: "_20_postgres_folder",
  },

  {
    icon: <img src={_20_postgres_function} width={22} height={22} />,
    value: "_20_postgres_function",
  },
  {
    icon: <img src={_20_postgres_index} width={22} height={22} />,
    value: "_20_postgres_index",
  },
  {
    icon: <img src={_20_postgres_schema} width={22} height={22} />,
    value: "_20_postgres_schema",
  },
  {
    icon: <img src={_20_postgres_server} width={22} height={22} />,
    value: "_20_postgres_server",
  },
  {
    icon: <img src={_20_postgres_table} width={22} height={22} />,
    value: "_20_postgres_table",
  },
  {
    icon: <img src={_20_postgres_view} width={22} height={22} />,
    value: "_20_postgres_view",
  },
  {
    icon: <img src={_20_powerpoint} width={22} height={22} />,
    value: "_20_powerpoint",
  },
  {
    icon: <img src={_20_procedure} width={22} height={22} />,
    value: "_20_procedure",
  },
  {
    icon: <img src={_20_profile_file} width={22} height={22} />,
    value: "_20_profile_file",
  },

  {
    icon: <img src={_20_profile_item} width={22} height={22} />,
    value: "_20_profile_item",
  },
  {
    icon: <img src={_20_profile_library} width={22} height={22} />,
    value: "_20_profile_library",
  },

  {
    icon: <img src={_20_oracle_BI_analysis} width={22} height={22} />,
    value: "_20_oracle_BI_analysis",
  },
  {
    icon: (
      <img src={_20_oracle_BI_analysis_and_reports} width={22} height={22} />
    ),
    value: "_20_oracle_BI_analysis_and_reports",
  },
  {
    icon: <img src={_20_oracle_BI_business_layer} width={22} height={22} />,
    value: "_20_oracle_BI_business_layer",
  },
  {
    icon: <img src={_20_oracle_BI_column} width={22} height={22} />,
    value: "_20_oracle_BI_column",
  },
  {
    icon: <img src={_20_oracle_BI_condition} width={22} height={22} />,
    value: "_20_oracle_BI_condition",
  },
  {
    icon: <img src={_20_oracle_BI_dashboard} width={22} height={22} />,
    value: "_20_oracle_BI_dashboard",
  },
  {
    icon: <img src={_20_oracle_BI_data_model} width={22} height={22} />,
    value: "_20_oracle_BI_data_model",
  },
  {
    icon: <img src={_20_oracle_BI_dimension} width={22} height={22} />,
    value: "_20_oracle_BI_dimension",
  },

  {
    icon: <img src={_20_oracle_BI_filter} width={22} height={22} />,
    value: "_20_oracle_BI_filter",
  },

  {
    icon: <img src={_20_oracle_BI_folder} width={22} height={22} />,
    value: "_20_oracle_BI_folder",
  },
  {
    icon: <img src={_20_oracle_BI_hierarchy} width={22} height={22} />,
    value: "_20_oracle_BI_hierarchy",
  },
  {
    icon: <img src={_20_oracle_BI_logical_level} width={22} height={22} />,
    value: "_20_oracle_BI_logical_level",
  },
  {
    icon: <img src={_20_oracle_BI_measure} width={22} height={22} />,
    value: "_20_oracle_BI_measure",
  },
  {
    icon: <img src={_20_oracle_BI_monit} width={22} height={22} />,
    value: "_20_oracle_BI_monit",
  },
  {
    icon: <img src={_20_oracle_BI_physical_layer} width={22} height={22} />,
    value: "_20_oracle_BI_physical_layer",
  },
  {
    icon: <img src={_20_oracle_BI_presentation_layer} width={22} height={22} />,
    value: "_20_oracle_BI_presentation_layer",
  },
  {
    icon: <img src={_20_oracle_BI_report} width={22} height={22} />,
    value: "_20_oracle_BI_report",
  },
  {
    icon: <img src={_20_oracle_BI_reports_published} width={22} height={22} />,
    value: "_20_oracle_BI_reports_published",
  },
  {
    icon: <img src={_20_oracle_BI_report_task} width={22} height={22} />,
    value: "_20_oracle_BI_report_task",
  },
  {
    icon: <img src={_20_oracle_BI_table} width={22} height={22} />,
    value: "_20_oracle_BI_table",
  },
  {
    icon: <img src={_20_oracle_BI_table_fact} width={22} height={22} />,
    value: "_20_oracle_BI_table_fact",
  },
  {
    icon: <img src={_20_oracle_BI_template_child} width={22} height={22} />,
    value: "_20_oracle_BI_template_child",
  },
  {
    icon: <img src={_20_oracle_BI_template_styles} width={22} height={22} />,
    value: "_20_oracle_BI_template_styles",
  },
  {
    icon: <img src={_20_oracle_column} width={22} height={22} />,
    value: "_20_oracle_column",
  },
  {
    icon: <img src={_20_oracle_column_IDX} width={22} height={22} />,
    value: "_20_oracle_column_IDX",
  },
  {
    icon: <img src={_20_oracle_column_PK} width={22} height={22} />,
    value: "_20_oracle_column_PK",
  },
  {
    icon: <img src={_20_oracle_folder} width={22} height={22} />,
    value: "_20_oracle_folder",
  },
  {
    icon: <img src={_20_oracle_function} width={22} height={22} />,
    value: "_20_oracle_function",
  },
  {
    icon: <img src={_20_oracle_index} width={22} height={22} />,
    value: "_20_oracle_index",
  },

  {
    icon: <img src={_20_oracle_package} width={22} height={22} />,
    value: "_20_oracle_package",
  },
  {
    icon: <img src={_20_oracle_procedure} width={22} height={22} />,
    value: "_20_oracle_procedure",
  },

  {
    icon: <img src={_20_oracle_schema} width={22} height={22} />,
    value: "_20_oracle_schema",
  },
  {
    icon: <img src={_20_oracle_server} width={22} height={22} />,
    value: "_20_oracle_server",
  },
  {
    icon: <img src={_20_oracle_table} width={22} height={22} />,
    value: "_20_oracle_table",
  },
  {
    icon: <img src={_20_oracle_view} width={22} height={22} />,
    value: "_20_oracle_view",
  },
  {
    icon: <img src={_20_owner} width={22} height={22} />,
    value: "_20_owner",
  },

  {
    icon: <img src={_20_measure} width={22} height={22} />,
    value: "_20_measure",
  },

  {
    icon: <img src={_20_microstrategy_attribute} width={22} height={22} />,
    value: "_20_microstrategy_attribute",
  },
  {
    icon: <img src={_20_microstrategy_consolidation} width={22} height={22} />,
    value: "_20_microstrategy_consolidation",
  },

  {
    icon: <img src={_20_microstrategy_cube} width={22} height={22} />,
    value: "_20_microstrategy_cube",
  },
  {
    icon: <img src={_20_microstrategy_custom_group} width={22} height={22} />,
    value: "_20_microstrategy_custom_group",
  },
  {
    icon: <img src={_20_microstrategy_dashboard} width={22} height={22} />,
    value: "_20_microstrategy_dashboard",
  },
  {
    icon: <img src={_20_microstrategy_DB_instance} width={22} height={22} />,
    value: "_20_microstrategy_DB_instance",
  },
  {
    icon: <img src={_20_microstrategy_document} width={22} height={22} />,
    value: "_20_microstrategy_document",
  },
  {
    icon: <img src={_20_microstrategy_drill_map} width={22} height={22} />,
    value: "_20_microstrategy_drill_map",
  },
  {
    icon: <img src={_20_microstrategy_fact} width={22} height={22} />,
    value: "_20_microstrategy_fact",
  },
  {
    icon: <img src={_20_microstrategy_filter} width={22} height={22} />,
    value: "_20_microstrategy_filter",
  },

  {
    icon: <img src={_20_microstrategy_folder} width={22} height={22} />,
    value: "_20_microstrategy_folder",
  },

  {
    icon: <img src={_20_microstrategy_hierarchy} width={22} height={22} />,
    value: "_20_microstrategy_hierarchy",
  },
  {
    icon: <img src={_20_microstrategy_metric} width={22} height={22} />,
    value: "_20_microstrategy_metric",
  },

  {
    icon: <img src={_20_microstrategy_project} width={22} height={22} />,
    value: "_20_microstrategy_project",
  },

  {
    icon: <img src={_20_microstrategy_prompt} width={22} height={22} />,
    value: "_20_microstrategy_prompt",
  },
  {
    icon: <img src={_20_microstrategy_report} width={22} height={22} />,
    value: "_20_microstrategy_report",
  },
  {
    icon: <img src={_20_microstrategy_table} width={22} height={22} />,
    value: "_20_microstrategy_table",
  },
  {
    icon: <img src={_20_microstrategy_template} width={22} height={22} />,
    value: "_20_microstrategy_template",
  },
  {
    icon: <img src={_20_microstrategy_transformation} width={22} height={22} />,
    value: "_20_microstrategy_transformation",
  },

  {
    icon: <img src={_20_mssql_clustred_index} width={22} height={22} />,
    value: "_20_mssql_clustred_index",
  },
  {
    icon: <img src={_20_mssql_column} width={22} height={22} />,
    value: "_20_mssql_column",
  },
  {
    icon: <img src={_20_mssql_column_FK} width={22} height={22} />,
    value: "_20_mssql_column_FK",
  },
  {
    icon: <img src={_20_mssql_column_PK} width={22} height={22} />,
    value: "_20_mssql_column_PK",
  },

  {
    icon: <img src={_20_mssql_DB} width={22} height={22} />,
    value: "_20_mssql_DB",
  },
  {
    icon: <img src={_20_mssql_folder} width={22} height={22} />,
    value: "_20_mssql_folder",
  },
  {
    icon: <img src={_20_mssql_function} width={22} height={22} />,
    value: "_20_mssql_function",
  },
  {
    icon: <img src={_20_mssql_non_clustred_index} width={22} height={22} />,
    value: "_20_mssql_non_clustred_index",
  },
  {
    icon: <img src={_20_mssql_owner} width={22} height={22} />,
    value: "_20_mssql_owner",
  },

  {
    icon: <img src={_20_mssql_procedure} width={22} height={22} />,
    value: "_20_mssql_procedure",
  },
  {
    icon: <img src={_20_mssql_server} width={22} height={22} />,
    value: "_20_mssql_server",
  },
  {
    icon: <img src={_20_mssql_table} width={22} height={22} />,
    value: "_20_mssql_table",
  },
  {
    icon: <img src={_20_mssql_table_function} width={22} height={22} />,
    value: "_20_mssql_table_function",
  },
  {
    icon: <img src={_20_mssql_view} width={22} height={22} />,
    value: "_20_mssql_view",
  },

  {
    icon: <img src={_20_link} width={22} height={22} />,
    value: "_20_link",
  },
  {
    icon: <img src={_20_keyword_folder} width={22} height={22} />,
    value: "_20_keyword_folder",
  },

  {
    icon: <img src={_20_glosariusz} width={22} height={22} />,
    value: "_20_glosariusz",
  },
  {
    icon: <img src={_20_glossary_ncorp} width={22} height={22} />,
    value: "_20_glossary_ncorp",
  },
  {
    icon: <img src={_20_glossary_ver} width={22} height={22} />,
    value: "_20_glossary_ver",
  },

  {
    icon: <img src={_20_filtr} width={22} height={22} />,
    value: "_20_filtr",
  },
  {
    icon: <img src={_20_flash} width={22} height={22} />,
    value: "_20_flash",
  },

  {
    icon: <img src={_20_excel} width={22} height={22} />,
    value: "_20_excel",
  },
  {
    icon: <img src={_20_derive_table} width={22} height={22} />,
    value: "_20_derive_table",
  },
  {
    icon: <img src={_20_detail} width={22} height={22} />,
    value: "_20_detail",
  },
  {
    icon: <img src={_20_dimension} width={22} height={22} />,
    value: "_20_dimension",
  },
  {
    icon: <img src={_20_dqcalltests} width={22} height={22} />,
    value: "_20_dqcalltests",
  },
  {
    icon: <img src={_20_dqcgroup} width={22} height={22} />,
    value: "_20_dqcgroup",
  },
  {
    icon: <img src={_20_dqctestf} width={22} height={22} />,
    value: "_20_dqctestf",
  },
  {
    icon: <img src={_20_dqctestn} width={22} height={22} />,
    value: "_20_dqctestn",
  },

  {
    icon: <img src={_20_dqctests} width={22} height={22} />,
    value: "_20_dqctests",
  },
  {
    icon: <img src={_20_dqmfoldercoordination} width={22} height={22} />,
    value: "_20_dqmfoldercoordination",
  },
  {
    icon: <img src={_20_dqmgroup} width={22} height={22} />,
    value: "_20_dqmgroup",
  },
  {
    icon: <img src={_20_dqmrate} width={22} height={22} />,
    value: "_20_dqmrate",
  },
  {
    icon: <img src={_20_dqmrateaccepted} width={22} height={22} />,
    value: "_20_dqmrateaccepted",
  },
  {
    icon: <img src={_20_dqmraterejected} width={22} height={22} />,
    value: "_20_dqmraterejected",
  },
  {
    icon: <img src={_20_dqmtestfailed} width={22} height={22} />,
    value: "_20_dqmtestfailed",
  },
  {
    icon: <img src={_20_dqmtesthalffailed} width={22} height={22} />,
    value: "_20_dqmtesthalffailed",
  },
  {
    icon: <img src={_20_dqmtestinactive} width={22} height={22} />,
    value: "_20_dqmtestinactive",
  },
  {
    icon: <img src={_20_dqmtestnotstarted} width={22} height={22} />,
    value: "_20_dqmtestnotstarted",
  },
  {
    icon: <img src={_20_dqmtestsuccess} width={22} height={22} />,
    value: "_20_dqmtestsuccess",
  },

  {
    icon: <img src={_20_confluence_blog} width={22} height={22} />,
    value: "_20_confluence_blog",
  },
  {
    icon: <img src={_20_confluence_folder} width={22} height={22} />,
    value: "_20_confluence_folder",
  },
  {
    icon: <img src={_20_confluence_page} width={22} height={22} />,
    value: "_20_confluence_page",
  },
  {
    icon: <img src={_20_confluence_space} width={22} height={22} />,
    value: "_20_confluence_space",
  },
  {
    icon: <img src={_20_connection} width={22} height={22} />,
    value: "_20_connection",
  },
  {
    icon: <img src={_20_connection_engine_folder} width={22} height={22} />,
    value: "_20_connection_engine_folder",
  },
  {
    icon: <img src={_20_connection_folder} width={22} height={22} />,
    value: "_20_connection_folder",
  },

  {
    icon: <img src={_20_cognos_report} width={22} height={22} />,
    value: "_20_cognos_report",
  },

  {
    icon: <img src={_20_connection_network_folder} width={22} height={22} />,
    value: "_20_connection_network_folder",
  },

  {
    icon: <img src={_20_crystal} width={22} height={22} />,
    value: "_20_crystal",
  },
  {
    icon: <img src={_20_columnIDX} width={22} height={22} />,
    value: "_20_columnIDX",
  },
  {
    icon: <img src={_20_cognos_table} width={22} height={22} />,
    value: "_20_cognos_table",
  },
  {
    icon: <img src={_20_columnPK} width={22} height={22} />,
    value: "_20_columnPK",
  },
  {
    icon: <img src={_20_class} width={22} height={22} />,
    value: "_20_class",
  },
  {
    icon: <img src={_20_client} width={22} height={22} />,
    value: "_20_client",
  },
  {
    icon: <img src={_20_bwcube} width={22} height={22} />,
    value: "_20_bwcube",
  },
  {
    icon: <img src={_20_cognos_column} width={22} height={22} />,
    value: "_20_cognos_column",
  },
  {
    icon: <img src={_20_cognos_datasource} width={22} height={22} />,
    value: "_20_cognos_datasource",
  },

  {
    icon: <img src={_20_cognos_dimension} width={22} height={22} />,
    value: "_20_cognos_dimension",
  },
  {
    icon: <img src={_20_cognos_filter} width={22} height={22} />,
    value: "_20_cognos_filter",
  },

  {
    icon: <img src={_20_cognos_folder} width={22} height={22} />,
    value: "_20_cognos_folder",
  },
  {
    icon: <img src={_20_cognos_hierarchy} width={22} height={22} />,
    value: "_20_cognos_hierarchy",
  },
  {
    icon: <img src={_20_cognos_measure} width={22} height={22} />,
    value: "_20_cognos_measure",
  },

  {
    icon: <img src={_20_cognos_model} width={22} height={22} />,
    value: "_20_cognos_model",
  },
  {
    icon: <img src={_20_cognos_package} width={22} height={22} />,
    value: "_20_cognos_package",
  },
  {
    icon: <img src={_20_cognos_param_map} width={22} height={22} />,
    value: "_20_cognos_param_map",
  },
  {
    icon: <img src={_20_cognos_project} width={22} height={22} />,
    value: "_20_cognos_project",
  },

  {
    icon: <img src={_20_article} width={22} height={22} />,
    value: "_20_article",
  },

  {
    icon: <img src={_20_article_folder} width={22} height={22} />,
    value: "_20_article_folder",
  },

  {
    icon: <img src={_20_bfolder} width={22} height={22} />,
    value: "_20_bfolder",
  },

  {
    icon: <img src={_20_blog_icon} width={22} height={22} />,
    value: "_20_blog_icon",
  },

  {
    icon: <img src={_20_blogw_icon} width={22} height={22} />,
    value: "_20_blogw_icon",
  },
  {
    icon: <img src={_20_bwarea} width={22} height={22} />,
    value: "_20_bwarea",
  },
  {
    icon: <img src={_20_bwbex} width={22} height={22} />,
    value: "_20_bwbex",
  },

  {
    icon: <img src={_20_bwcha} width={22} height={22} />,
    value: "_20_bwcha",
  },

  {
    icon: <img src={_20_bwiset} width={22} height={22} />,
    value: "_20_bwiset",
  },
  {
    icon: <img src={_20_bwkyf} width={22} height={22} />,
    value: "_20_bwkyf",
  },
  {
    icon: <img src={_20_bwmpro} width={22} height={22} />,
    value: "_20_bwmpro",
  },
  {
    icon: <img src={_20_bwobject} width={22} height={22} />,
    value: "_20_bwobject",
  },
  {
    icon: <img src={_20_bwodso} width={22} height={22} />,
    value: "_20_bwodso",
  },

  {
    icon: <img src={_20_bwtim} width={22} height={22} />,
    value: "_20_bwtim",
  },
  {
    icon: <img src={_20_bwuni} width={22} height={22} />,
    value: "_20_bwuni",
  },

  {
    icon: <img src={_20_alias_table} width={22} height={22} />,
    value: "_20_alias_table",
  },
  {
    icon: <img src={_30_attribute} width={22} height={22} />,
    value: "_30_attribute",
  },
  {
    icon: <img src={_20_attribute} width={22} height={22} />,
    value: "_20_attribute",
  },
  {
    icon: <img src={_30_busines_process} width={22} height={22} />,
    value: "_30_business_process",
  },
  {
    icon: <img src={_30_business_term} width={22} height={22} />,
    value: "_30_business_term",
  },
  {
    icon: <img src={_30_column} width={22} height={22} />,
    value: "_30_column",
  },
  {
    icon: <img src={_20_column} width={22} height={22} />,
    value: "_20_column",
  },
  {
    icon: <img src={_30_computer} width={22} height={22} />,
    value: "_30_computer",
  },
  {
    icon: <img src={_30_database} width={22} height={22} />,
    value: "_30_database",
  },
  {
    icon: <img src={_30_document} width={22} height={22} />,
    value: "_30_document",
  },
  {
    icon: <img src={_30_dot} width={22} height={22} />,
    value: "_30_dot",
  },
  {
    icon: <img src={_30_entity} width={22} height={22} />,
    value: "_30_entity",
  },
  {
    icon: <img src={_20_folder} width={22} height={22} />,
    value: "_20_folder",
  },
  {
    icon: <img src={_30_folder} width={22} height={22} />,
    value: "_30_folder",
  },
  {
    icon: <img src={_30_graph} width={22} height={22} />,
    value: "_30_graph",
  },
  {
    icon: <img src={_30_green_graph} width={22} height={22} />,
    value: "_30_green_graph",
  },
  {
    icon: <img src={_30_interface} width={22} height={22} />,
    value: "_30_interface",
  },
  {
    icon: <img src={_20_key} width={22} height={22} />,
    value: "_20_key",
  },
  {
    icon: <img src={_30_key} width={22} height={22} />,
    value: "_30_key",
  },
  {
    icon: <img src={_30_orange_graph} width={22} height={22} />,
    value: "_30_orange_graph",
  },
  {
    icon: <img src={_30_pencil_and_paper} width={22} height={22} />,
    value: "_30_pencil_and_paper",
  },
  {
    icon: <img src={_30_person} width={22} height={22} />,
    value: "_30_person",
  },
  {
    icon: <img src={_30_red_graph} width={22} height={22} />,
    value: "_30_red_graph",
  },
  {
    icon: <img src={_20_report} width={22} height={22} />,
    value: "_20_report",
  },
  {
    icon: <img src={_30_report} width={22} height={22} />,
    value: "_30_report",
  },
  {
    icon: <img src={_30_role} width={22} height={22} />,
    value: "_30_role",
  },
  {
    icon: <img src={_30_schema} width={22} height={22} />,
    value: "_30_schema",
  },
  {
    icon: <img src={_30_server} width={22} height={22} />,
    value: "_30_server",
  },
  {
    icon: <img src={_20_table} width={22} height={22} />,
    value: "_20_table",
  },
  {
    icon: <img src={_30_table} width={22} height={22} />,
    value: "_30_table",
  },
  {
    icon: <img src={_30_unit} width={22} height={22} />,
    value: "_30_unit",
  },
];
