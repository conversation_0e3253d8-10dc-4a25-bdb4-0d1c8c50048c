import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { getAttributeIcon } from "../../../constants";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";

const TemplateNameRendererBase = (params) => {
  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const record = params.data;
  const selectedTemplate = templatesData[Number(record?.templateId)];
  if (!selectedTemplate) {
    return "-";
  }
  const templateIcon = selectedTemplate?.icon || "_30_folder";

  return (
    <p className="title-container">
      {getAttributeIcon(templateIcon)}
      {selectedTemplate.name}
    </p>
  );
};

export const TemplateNameRenderer = withErrorBoundary(
  React.memo(TemplateNameRendererBase),
  "error.generic"
);
