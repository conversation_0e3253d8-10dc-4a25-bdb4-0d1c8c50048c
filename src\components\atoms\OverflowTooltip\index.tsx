import { useRef, useEffect, useState, ReactNode } from "react";
import { Tooltip } from "antd";

interface Props {
  title: string;
  children: ReactNode;
  style?: any;
  id?: string;
  className?: string;
}

// Used in home sidebar to show overflowed text

const OverflowTooltip = ({ title, children, style, id, className }: Props) => {
  const containerRef = useRef(null);
  const [isOverflowed, setIsOverflowed] = useState(false);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { scrollWidth, clientWidth } = entry.target;
          setIsOverflowed(scrollWidth > clientWidth);
        }
      });

      resizeObserver.observe(container);

      return () => {
        resizeObserver.unobserve(container);
      };
    }
  }, [title]);

  return (
    <div
      ref={containerRef}
      id={id}
      className={className ? className : ""}
      style={{ overflow: "hidden", textOverflow: "ellipsis", ...style }}
    >
      {isOverflowed ? (
        <Tooltip placement="topLeft" title={title} mouseEnterDelay={0.8}>
          {children}
        </Tooltip>
      ) : (
        children
      )}
    </div>
  );
};

export { OverflowTooltip };
