import { Dialog } from "primereact/dialog";
import React, { useEffect, useState } from "react";
import { styled } from "@linaria/react";
import PartyPNG from "../../../assets/images/party.png";
import CheckPNG from "../../../assets/images/check.png";
import { Button } from "antd";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../withErrorBoundary";

const versionChanges = [
  {
    version: "3.0.26",
    changes: [
      "Added version update info popup!",
      "Favorites Page Improvements",
      "Fixed No data message visible while data loading",
      "Fixed time interval - bug if you don't select an interval",
    ],
  },
  {
    version: "********",
    changes: [
      "Implemented block attribute change type",
      "Fixed breadcrumbs display in order in details container",
      "Attribute type - numeric allows to enter non integer values",
      "Removed time interval default value as 0",
    ],
  },
  {
    version: "********",
    changes: ["Number fields can now take negative values"],
  },
  {
    version: "********",
    changes: ["Added tooltip for overflown sidebar items"],
  },
  {
    version: "********",
    changes: [
      "Metamodel - old components to be replaced with new for consistency ",
      "Added mockup information",
    ],
  },
  {
    version: "********",
    changes: ["Fixed help text covered by UI"],
  },
  {
    version: "********",
    changes: ["Added more favorites section"],
  },
  {
    version: "********",
    changes: ["Fixed columns selector - no name column"],
  },
  {
    version: "3.0.27",
    changes: ["Updated mockup info"],
  },
  {
    version: "********",
    changes: ["Saving bottom navigation tables in database"],
  },
  {
    version: "********",
    changes: [
      "Hyperlink mono - no option to delete value",
      "Automatically save current state of resizable divs",
      "Add Object Folder, Add obejct template translations",
      "Table component - saved sorting, filtering in database",
    ],
  },
  {
    version: "********",
    changes: [
      "Added remove from favorites in sidebar collapsed state",
      "Added expandable hyperlink tables",
    ],
  },
  {
    version: "3.0.27.4",
    changes: ["Updated expand/collapse display type in hyperlinks"],
  },
  {
    version: "********",
    changes: [
      "Pinned Improvements",
      "Remove Pin icon in sidebar node and implement favorite in sidebar context menu",
      "Added automatic data refresh in children table view",
    ],
  },
  {
    version: "********",
    changes: ["Displayed show parent option in graph"],
  },
  {
    version: "********",
    changes: [
      "Added hover on breadcrumb last item",
      "Prevented dragging if multiple nodes selected",
      "Removed group name of graphs",
    ],
  },

  {
    version: "********",
    changes: [
      "Added tooltip in graph node",
      "Remove from pinned if the pinned node is deleted from tree",
    ],
  },
  {
    version: "********0",
    changes: ["Added minimal editor for footer text"],
  },
  {
    version: "********",
    changes: ["Made D&D to top easier in sidebar"],
  },
  {
    version: "********",
    changes: [
      "Hided delete options for negative ids",
      "Fixed d&d issue in sidebar",
      "Fixed issue with large numbers in number field",
      "Updated editor",
    ],
  },
  {
    version: "********",
    changes: [
      "Updated parent path API implementation",
      "Added input field for hidden attributes in general settings",
      "Linked hidden attributes with dev mode",
      "Hided Download button in table component - Hyperlinks",
      "Remove blue rectangle around original node while Drag&Drop",
    ],
  },
  {
    version: "********",
    changes: ["Scroll to focused node on parent change/favorites click"],
  },
  {
    version: "********",
    changes: [
      "Reflected metamodel updates in model area",
      "Updated trashcan implementation",
      "Pinned Improvements",
      "Hyperlink Improvements",
    ],
  },
  {
    version: "3.0.30.2",
    changes: ["Calculated attribute names width in frontend"],
  },
  {
    version: "3.0.31.1",
    changes: [
      "Not considered New Attributes width",
      "Fixed empty rows in translations issues",
      "Pinned Object Improvements",
    ],
  },
  {
    version: "3.0.31.2",
    changes: ["Added scrollbar in trashcan tree"],
  },
  {
    version: "3.0.31.3",
    changes: ["Added edit with red frame and cancel button in settings page"],
  },
  {
    version: "3.0.31.4",
    changes: ["Pinned Improvements"],
  },
  {
    version: "3.0.32.1",
    changes: ["Implemented working version mockup"],
  },
  {
    version: "3.0.32.2",
    changes: ["Updated alignment in hyperlink table"],
  },
  {
    version: "3.0.32.3",
    changes: ["Added strike-through styles in trashcan"],
  },
  {
    version: "3.0.33.1",
    changes: [
      "Removed 3 dots in a page header",
      "Application properties tile - hide",
    ],
  },
  {
    version: "********",
    changes: ["Implemented working version"],
  },
  {
    version: "********",
    changes: ["Implemented disabling templates"],
  },
  {
    version: "********",
    changes: ["Restore and Jump implmentation"],
  },
  {
    version: "********",
    changes: ["Refreshed graph values if attribute values is changed"],
  },
  {
    version: "********",
    changes: [
      "Restricted move multiple nodes of different level to move to trashcan",
    ],
  },
  {
    version: "********",
    changes: ["Table improvements"],
  },
  {
    version: "********",
    changes: ["Update logo fix"],
  },
  {
    version: "********",
    changes: ["Added tour component"],
  },
  {
    version: "********",
    changes: ["Trashcan - restore and refreshing tree view"],
  },
  {
    version: "********",
    changes: ["Home page - Shortcuts theme"],
  },
  {
    version: "********",
    changes: ["Data sources tile mockup"],
  },
  {
    version: "********",
    changes: ["Home page - Quick links theme"],
  },
  {
    version: "********",
    changes: ["Hided -ve ids from template selections"],
  },
  {
    version: "********",
    changes: ["Updated allowed children format"],
  },
  {
    version: "********",
    changes: ["Validation for multiplicity"],
  },
  {
    version: "********",
    changes: ["Implemented node count"],
  },
  {
    version: "********",
    changes: [
      "Added count in menu creator and working version tree",
      "Implemented compound list dependency",
      "Implemented SQL attribute",
    ],
  },
  {
    version: "********",
    changes: ["Children table view - different order than in tree"],
  },
  {
    version: "********",
    changes: ["Hyperlink performance"],
  },
  {
    version: "********",
    changes: ["Compound attribute updated"],
  },
  {
    version: "********",
    changes: ["Mockup-Compound attribute"],
  },
  {
    version: "********",
    changes: ["Implemented Scheduler Attribute", "Implemented default values"],
  },
  {
    version: "********",
    changes: ["Implemented Dropzone"],
  },
  {
    version: "********",
    changes: ["Implemented search in home page"],
  },
  {
    version: "********",
    changes: [
      "Added visualization in relations",
      "Implemented search",
      "Added multiplicity in dropdown",
      "My comments implementation",
    ],
  },

  {
    version: "********",
    changes: ["Relation homogenity added!"],
  },
  {
    version: "*********",
    changes: ["Permissions implemented!", "Performance of tree improved!"],
  },
  {
    version: "*********",
    changes: [
      "Visual indicator for tree reordering",
      "Table perfromance optimization",
    ],
  },
];

const VersionChangeDialogBase = () => {
  const [open, setOpen] = useState(false);
  const [currentVersionIndex, setCurrentVersionIndex] = useState(-1);
  const { t } = useTranslation();
  const authenticated = useSelector(
    (rootState: RootState) => rootState.auth.authenticated
  );

  const headerElement = (
    <Header>
      <img src={PartyPNG} alt="Party Popper Image" height={30} width={30} />{" "}
      {t("New version updated!")}
    </Header>
  );

  useEffect(() => {
    const currentVersion = import.meta.env.VITE_FE_NEW_VERSION;

    const storedVersion = localStorage.getItem("appVersion");
    if (storedVersion) {
      const index = versionChanges.findIndex(
        (item) => item.version === storedVersion
      );
      setCurrentVersionIndex(index);
    }

    if (storedVersion !== currentVersion && authenticated) {
      localStorage.setItem("appVersion", currentVersion);
      setOpen(true);
    }
  }, [authenticated]);

  return (
    <Dialog
      visible={open}
      onHide={() => setOpen(false)}
      style={{ width: "700px" }}
      header={headerElement}
    >
      <Content>
        <p>
          {t(
            "We're excited to share the latest updates with you! We've introduced some exciting new features that we hope you'll enjoy. Please take a moment to familiarize yourself with them."
          )}
        </p>
        <h6>{t("Top features and improvements in this version")}</h6>
        <div className="improvements">
          {versionChanges.slice(currentVersionIndex + 1).map((version) =>
            version.changes.map((change, index) => (
              <div key={`${version.version}-${index}`}>
                <img src={CheckPNG} height={20} width={20} />
                {change}
              </div>
            ))
          )}
        </div>

        <Button type="primary" onClick={() => setOpen(false)}>
          {t("OK")}
        </Button>
      </Content>
    </Dialog>
  );
};

export const VersionChangeDialog = withErrorBoundary(
  React.memo(VersionChangeDialogBase),
  "error.generic"
);

const Content = styled.div`
  & > p {
    text-align: center;
  }

  & h6 {
    font-size: 13px;
    margin-top: 12px;
    text-align: center;
  }

  & .improvements {
    background: #eee;
    padding: 10px;
    margin-top: 11px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    & div {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  & button {
    margin-top: 18px;
    font-size: 13px;
    box-shadow: none;
    display: flex;
    margin-left: auto;
    margin-right: auto;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  color: var(--color-text);
`;
