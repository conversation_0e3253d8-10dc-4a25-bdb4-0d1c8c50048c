import { <PERSON><PERSON>, Flex } from "antd";
import { useEffect, useState } from "react";
import { BreadCrumb, MyTable } from "../../../components";
import { useTheme } from "../../../utils/useTheme";
import LogoImage from "../../../assets/images/logo.png";
import { Helmet } from "react-helmet";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { saveGlobalSettings, saveLocalSettings } from "../../../services";
import {
  IGeneralSettings,
  IGlobalSettings,
  ILocalSettings,
} from "../../../interfaces";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../../store/features";
import { Container, Wrapper } from "./styles";
import {
  GET_GLOBAL_SETTINGS_KEY,
  GET_LOCAL_SETTINGS_KEY,
} from "../../../constants";
import { setHiddenAttributes } from "../../../store/features/globalSettings";
import { RootState } from "../../../store";
import { setLogo, setShowLogo } from "../../../store/features/localSettings";
import { useNotification } from "../../../utils/functions/customHooks";
import { CustomIcons } from "./CustomIcons";

const Settings = () => {
  const queryClient = useQueryClient();
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [isPropertiesEdited, setPropertiesEdited] = useState(false);
  const [isTableEdited, setIsTableEdited] = useState(false);
  const [generalSettings, setGeneralSettings] = useState(
    DEFAULT_GENERAL_SETTINGS as IGeneralSettings
  );
  const [applicationTitle, setApplicationTitle] = useState("");

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const settingsData = queryClient.getQueryData(
    GET_GLOBAL_SETTINGS_KEY
  ) as IGlobalSettings;

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(breadcrumb));
  }, []);

  // table settings
  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.generalSettingsTable &&
      localSettingsData?.body[0]?.value?.generalSettingsTable?.columns.length >
        0
    ) {
      if (localSettingsData?.body[0]?.value?.generalSettingsTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.generalSettingsTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.generalSettingsTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.generalSettingsTable.columns?.forEach(
          (column) => {
            const index = COLUMNS.findIndex((item) => item.field === column);
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.generalSettingsTable?.columns
        );
        setPinned(
          localSettingsData?.body[0]?.value?.generalSettingsTable?.pinned
        );
        setSort(localSettingsData?.body[0]?.value?.generalSettingsTable?.sort);
        setFilters(
          localSettingsData?.body[0]?.value?.generalSettingsTable?.filters
        );
        setColumns(allColumns);
      }
    } else {
      setColumns(COLUMNS);
      setColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  useEffect(() => {
    const rows = [];
    rows.push({
      id: "logo",
      key: t("Logo"),
      editType: "image",
      value: settingsData.body[0].value.general?.logo || LogoImage,
    });
    rows.push({
      id: "title",
      key: t("Application Title"),

      value:
        settingsData.body[0].value?.general?.application_name ||
        DEFAULT_GENERAL_SETTINGS?.application_name,
    });
    rows.push({
      id: "footer",
      key: t("Footer Text"),
      editType: "textEditor",
      value:
        settingsData.body[0].value?.general?.footerText ||
        DEFAULT_GENERAL_SETTINGS?.footerText,
    });

    setRows(rows);
  }, [settingsData]);

  const tableMutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const mutation = useMutation(saveGlobalSettings, {
    onSuccess: () => {
      showSuccessNotification("General Settings Published Successfully!");
      dispatch(setMask(false));
      queryClient.invalidateQueries(GET_GLOBAL_SETTINGS_KEY);
      dispatch(setLogo(generalSettings?.logo));
      setApplicationTitle(generalSettings?.application_name);
      dispatch(setShowLogo(generalSettings?.showLogo));
      dispatch(setHiddenAttributes(generalSettings?.hiddenAttributes));
    },
  });

  const handleSave = () => {
    if (isTableEdited) {
      const request = {
        value: {
          ...(localSettingsData?.body
            ? localSettingsData?.body[0]?.value || {}
            : {}),
          generalSettingsTable: {
            columns: columnsRequest,
            filters: filters,
            sort: sort,
            pinned: pinned,
          },
        },
      };
      tableMutation.mutate(request);
    }
    if (isPropertiesEdited) {
      if (settingsData && rows.length > 0) {
        mutation.mutate({
          value: {
            ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
            general: {
              application_name: rows?.[1]?.value || "CDO.tools",
              logo: rows?.[0]?.value || "",
              homeLayoutType: "kanban",
              sections: ["messages", "history"],
              hasShortcut: false,
              showSearchHistory: false,
              footerText: rows?.[2]?.value || "",
            },
          },
        });
      }
    }
  };

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const handleCancel = () => {
    setIsTableEdited(false);
    setPropertiesEdited(false);
    setResetTrigger((trigger) => trigger + 1);

    if (settingsData && settingsData?.body[0]?.value?.general) {
      setGeneralSettings({ ...settingsData.body[0].value.general });
      setApplicationTitle(
        settingsData.body[0].value?.general?.application_name
      );
    } else {
      setGeneralSettings(DEFAULT_GENERAL_SETTINGS);
      setApplicationTitle(DEFAULT_GENERAL_SETTINGS?.application_name);
    }
    dispatch(setMask(false));
  };

  return (
    <Wrapper>
      <Helmet>
        <title>{applicationTitle}</title>
      </Helmet>
      {contextHolder}
      <BreadCrumb
        extra={
          mask && (
            <Flex gap={10}>
              <Button
                className="breadcrumb-button cancel-button"
                type="primary"
                onClick={handleCancel}
              >
                {t("Cancel")}
              </Button>
              <Button
                className="breadcrumb-button save-button"
                type="primary"
                onClick={handleSave}
                loading={mutation.isLoading || tableMutation.isLoading}
              >
                {t("Save")}
              </Button>
            </Flex>
          )
        }
      />
      <div
        style={{
          flex: 1,
          overflow: "auto",
        }}
      >
        <Container
          theme={theme}
          style={{ border: mask ? "1px solid red" : "none" }}
        >
          <MyTable
            columns={columns}
            data={rows}
            detectChange={() => {
              detectChange();
              setIsTableEdited(true);
            }}
            resetTrigger={resetTrigger}
            editable
            loading={mutation.isLoading}
            excelFileName="settings"
            onRowsEdit={(values) => {
              const editIndex = rows?.findIndex(
                (data) => data?.key === values?.key
              );
              const newRows = [...rows];
              newRows[editIndex] = values;

              setRows([...newRows]);
              detectChange();
              setPropertiesEdited(true);
            }}
            setPinned={setPinned}
            setColumnsRequest={setColumnsRequest}
            setFilters={setFilters}
            setSort={setSort}
            initialFilters={
              localSettingsData?.body[0]?.value?.generalSettingsTable
                ?.filters || {}
            }
          />
        </Container>
        <div>
          <Container theme={theme}>
            {mask && <div className="mask" />}
            <CustomIcons />
          </Container>
        </div>
      </div>
    </Wrapper>
  );
};

const DEFAULT_GENERAL_SETTINGS = {
  application_name: "CDO.tools",
  logo: "",
  homeLayoutType: "kanban",
  sections: ["messages", "history"],
  hasShortcut: false,
  showSearchHistory: false,
  showLogo: false,
  showDescription: false,
  footerText: "",
  footerTextAlignment: "center",
  displayFooterText: false,
} as IGeneralSettings;

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "General Settings",
    to: "/settings/general",
  },
];

export default Settings;

const COLUMNS = [
  {
    headerName: "Settings",
    field: "key",
    minWidth: 250,
    flex: 1,
  },
  {
    headerName: "Value",
    field: "value",
    minWidth: 250,
    flex: 1,
    autoHeight: true,
    editable: true,
    cellRenderer: ({ data }) => {
      if (data?.editType === "image") {
        return <img src={data?.value} width={100} />;
      }

      return data?.value;
    },
  },

  {
    headerName: "",
    field: "actions",
    isEditActions: true,
  },
];
