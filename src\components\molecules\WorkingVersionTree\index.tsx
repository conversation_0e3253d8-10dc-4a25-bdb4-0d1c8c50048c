import { Tree } from "antd";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import type { EventDataNode } from "antd/es/tree";
import { ITreeData } from "../../../interfaces";
import {
  useNotification,
  useParentHeight,
  useWorkingVersionDragActions,
} from "../../../utils/functions/customHooks";
import { WorkingVersionTreeLabel } from "./WorkingVersionTreeLabel";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";

const WorkingVersionTreeBase = ({
  treeData,
  setAction,
  expandedKeys,
  setExpandedKeys,
  setSelected,
  selected,
  dropdownOpen,
  setDropdownOpen,
  setTreeData,
}) => {
  const { contextHolder } = useNotification();
  const { handleWorkingVersionDrop } = useWorkingVersionDragActions();
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  const getRecursiveSelectedKeys = (
    selectedNodes,
    selectedKeys,
    selectedKeysInfo
  ) => {
    selectedNodes.forEach((item) => {
      if (!selectedKeys.includes(item.key)) {
        selectedKeys.push(item.key);
        selectedKeysInfo.push({
          id: item.key,
          parentId: item.parentId,
          name: item.name,
          isAsset: item.templateId === 2,
          templateId: item?.templateId,
          body: item?.body,
          isLeaf: item?.isLeaf,
        });
      }

      if (item.children && item.children.length > 0) {
        getRecursiveSelectedKeys(item.children, selectedKeys, selectedKeysInfo);
      }
    });
  };

  const expandNode = (isLeaf, childrens, id) => {
    if (!isLeaf && childrens < 1) {
      setExpandedKeys([...expandedKeys, id]);
    }
  };

  const handleSelect = (keys: number[], event) => {
    if (!dropdownOpen) {
      if (keys.length < 2) {
        const selectedKeys = [];
        event?.selectedNodes.forEach((item) => {
          selectedKeys.push({
            id: item.key,
            parentId: item.parentId,
            name: item.name,
            isAsset: item.templateId === 2,
            templateId: item?.templateId,
            body: item?.body,
            isLeaf: item?.isLeaf,
          });
        });
        setSelected({ keys: [...keys], info: [...selectedKeys] });
      } else {
        const selectedKeysInfo = [];
        const selectedKeys = [];

        getRecursiveSelectedKeys(
          event?.selectedNodes,
          selectedKeys,
          selectedKeysInfo
        );

        setSelected({
          keys: [...selectedKeys],
          info: [...selectedKeysInfo],
        });
      }
    }
  };

  const handleExpand = (
    _,
    event: {
      node: EventDataNode<ITreeData>;
      expanded: boolean;
      nativeEvent: any;
    }
  ) => {
    const isExpandIconClicked = event.nativeEvent.target.classList.contains(
      "ant-tree-switcher-icon"
    );

    if (!isExpandIconClicked) {
      return;
    }

    let newExpandedKeys = null;
    if (expandedKeys.includes(event.node.key)) {
      newExpandedKeys = expandedKeys.filter((key) => key != event.node.key);
    } else {
      newExpandedKeys = [...expandedKeys, event.node.key];
    }

    setExpandedKeys([...newExpandedKeys]);
  };

  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );

  const movingMask = useSelector((state: RootState) => state.mask.movingMask);

  return (
    <div className="tree tree-container" ref={containerRef}>
      {(workingVersionActive || movingMask) && <div className="mask" />}
      {contextHolder}
      <Tree.DirectoryTree
        showLine
        showIcon={false}
        multiple
        blockNode
        draggable
        virtual
        height={containerHeight}
        onClick={(e) => e.stopPropagation()}
        className="hide-draggable-icon"
        autoExpandParent
        onSelect={handleSelect}
        onDrop={async (e) => {
          if (selected.keys.length <= 1) {
            handleWorkingVersionDrop(e, treeData, setTreeData);
          }
        }}
        switcherIcon={(val) => {
          return (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          );
        }}
        titleRender={(node) => (
          <WorkingVersionTreeLabel
            setAction={setAction}
            id={node.key}
            label={node.name}
            permissionsId={node?.permissionsId}
            parentId={node.parentId}
            setDropdownOpen={setDropdownOpen}
            templateId={node.templateId}
            allowedChildrens={node.allowedChildrens}
            icon={node.icon}
            breadcrumbs={node.breadcrumb}
            isLeaf={node.isLeaf}
            childrens={node?.children?.length || 0}
            expandNode={expandNode}
            setSelected={setSelected}
            selected={selected}
            count={node?.countChildren}
          />
        )}
        expandedKeys={expandedKeys}
        treeData={treeData}
        selectedKeys={(selected?.keys as any) || []}
        // loadData={async (treeNode) => {
        //   if (expandedTrashKeys.includes(treeNode.key)) {
        //     return onLoadData(treeNode);
        //   }
        //   return Promise.reject();
        // }}
        onExpand={dropdownOpen ? () => null : handleExpand}
      />
    </div>
  );
};

export const WorkingVersionTree = withErrorBoundary(
  React.memo(WorkingVersionTreeBase),
  "error.generic"
);
