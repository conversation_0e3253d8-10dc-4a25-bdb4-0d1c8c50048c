import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";

const DropdownItemsBase = ({ attributeValue }) => {
  const { t } = useTranslation();
  // const items = [];
  // Object.keys(attributeValue || {})?.forEach((item) => {
  //   items.push(attributeValue[item]);
  // });

  return (
    <Items>
      {attributeValue?.map((item, index) => {
        return (
          <div key={index}>
            {item.name} <span>{item?.default ? `(${t("Default")})` : ""}</span>
          </div>
        );
      })}
    </Items>
  );
};

export const DropdownItems = withErrorBoundary(
  React.memo(DropdownItemsBase),
  "error.generic"
);

const Items = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;

  & > div {
    border: 1px solid #eaeaea;
    padding: 4px 12px;
    border-radius: 4px;
  }
`;
