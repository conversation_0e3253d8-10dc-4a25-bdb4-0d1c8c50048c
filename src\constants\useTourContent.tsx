import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../store";
import { NO_USER_ASSOCIATED } from ".";

const useTourContent = () => {
  const { t } = useTranslation();

  const { profileId, role } = useSelector((root: RootState) => root.auth);

  const HOME_TOUR_CONTENTS = [
    {
      title: t("Help"),
      description: t("More info about CDO.tools here."),
      allowedRoles: ["admin", "user", "no-person"],
      target: () => document.getElementById("help-tour-item"),
    },
    {
      title: t("Logout"),
      description: t("Logout here."),
      allowedRoles: ["admin", "user", "no-person"],
      target: () => document.getElementById("logout-tour-item"),
    },
    {
      title: t("Expand"),
      description: t("Expand / collapse vertical menu."),
      allowedRoles: ["admin", "user", "no-person"],
      target: () => document.getElementById("expand-tour-item"),
    },
    {
      title: t("Settings"),
      description: t("You can find more settings here."),
      allowedRoles: ["admin", "user", "no-person"],
      target: () => document.getElementById("settings-item"),
    },
    {
      title: t("Trashcan"),
      description: t("You can find deleted items here."),
      allowedRoles: ["admin"],
      target: () => document.getElementById("trashcan-item"),
    },
  ];

  const SETTINGS_TOUR_CONTENT = [
    {
      title: t("Metamodel"),
      description: t("Metamodel management."),
      allowedRoles: ["admin"],
      target: () => document.getElementById("item-Metamodel"),
    },
    {
      title: t("Menu Creator"),
      description: t("Menu management."),
      allowedRoles: ["admin"],
      target: () => document.getElementById("item-MenuCreator"),
    },

    {
      title: t("General Settings"),
      description: t(
        "Application settings -  logo, footer, application name (…)"
      ),
      allowedRoles: ["admin"],
      target: () => document.getElementById("item-GeneralSettings"),
    },
    {
      title: t("Translations"),
      description: t("Add new language or update translations from here."),
      allowedRoles: ["admin"],
      target: () => document.getElementById("item-Translations"),
    },
    {
      title: t("Theme & Layout"),
      description: t("Homepage theme, color palette"),
      allowedRoles: ["admin", "user"],
      target: () => document.getElementById("item-Theme&Layout"),
    },
  ];

  const getHomeTourContents = () => {
    const roles = profileId === NO_USER_ASSOCIATED ? "no-person" : role;
    return HOME_TOUR_CONTENTS.filter((item) =>
      item.allowedRoles.includes(roles)
    );
  };

  const getSettingsTourContents = () => {
    const roles = profileId === NO_USER_ASSOCIATED ? "no-person" : role;
    return SETTINGS_TOUR_CONTENT.filter((item) =>
      item.allowedRoles.includes(roles)
    );
  };

  return { getHomeTourContents, getSettingsTourContents };
};

export { useTourContent };
