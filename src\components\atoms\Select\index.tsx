import { Select as AntdSelect } from "antd";
import { Error, FormItem } from "../Input";
import { useTranslation } from "react-i18next";
import { Controller } from "react-hook-form";

// Simple select component to use with react-hook-form
// It uses antd select component
interface Props {
  control: any;
  options: any[];
  label?: string;
  error: string;
  name: string;
  onChange?: any;
  multiple?: boolean;
  value?: any;
}

const Select = ({
  control,
  options,
  label,
  name,
  error,
  onChange,
  value,
  multiple,
}: Props) => {
  const { t } = useTranslation();

  return (
    <FormItem>
      {label && <label>{t(label)}</label>}

      <Controller
        render={({ field }) =>
          multiple ? (
            <AntdSelect
              {...field}
              options={options}
              onChange={onChange}
              mode="multiple"
              popupClassName="multi-select"
              value={value || field?.value}
              filterOption={(input, option) => {
                return (option?.label?.toLowerCase() ?? "").includes(
                  input.toLowerCase()
                );
              }}
            />
          ) : onChange ? (
            <AntdSelect
              {...field}
              options={options}
              onChange={onChange}
              mode="multiple"
              popupClassName="multi-select"
              value={value || field?.value}
            />
          ) : (
            <AntdSelect {...field} options={options} />
          )
        }
        name={name}
        control={control}
      />

      {error && <Error>{error}</Error>}
    </FormItem>
  );
};

export { Select };
