import { styled } from "@linaria/react";
import { useTheme } from "../../../../utils/useTheme";
import { MyTable } from "../../../organisms";
import { GET_LOCAL_SETTINGS_KEY, GET_LOGS_DATA } from "../../../../constants";
import { memo, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import durationPlugin from "dayjs/plugin/duration";
dayjs.extend(durationPlugin);
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getNodeLogs } from "../../../../services/history";
import { useDispatch } from "react-redux";
import { useNotification } from "../../../../utils/functions/customHooks";
import { setBottomDrawerMask } from "../../../../store/features";
import { saveLocalSettings } from "../../../../services";
import { ILocalSettings } from "../../../../interfaces";
import { useSearchParams } from "react-router-dom";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";
import { debounce } from "lodash";

// display logs in bottom drawer
const LogsContainerBase = ({ id, displaySaveCancel, fromTrashcan }) => {
  const theme = useTheme();
  const parentRef = useRef(null);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();

  const [height, setHeight] = useState(0);
  const [logsData, setLogsData] = useState([]);
  const [triggerChange, setTriggerChange] = useState(null);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const COLUMNS = [
    {
      headerName: "Date",
      field: "timestamp",
      width: 150,
      cellRenderer: ({ data }) =>
        dayjs(data.timestamp).format("YYYY-MM-DD HH:mm"),
    },
    {
      headerName: "User",
      field: "user",
      width: 150,
      cellRenderer: ({ data }) => data?.user || "-",
    },
    {
      headerName: "Activation",
      field: "activation",
      width: 150,
      cellRenderer: ({ data }) =>
        data?.user === "Scheduler user" ? t("Scheduler") : t("Manual"),
    },
    {
      headerName: "Duration",
      field: "duration",
      width: 150,
      cellRenderer: ({ data }) => data?.duration || "-",
    },
    {
      headerName: "Status",
      field: "operation",
      minWidth: 150,
      flex: 1,
      cellRenderer: ({ data }) => t(data?.operation, data?.params || {}),
    },
  ];

  const [columns, setColumns] = useState(COLUMNS);

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  // fetch logs data
  const { data, isLoading, isFetching, refetch, isError } = useQuery<any>(
    [GET_LOGS_DATA, id],
    () => getNodeLogs(id),
    {
      enabled: !!id && !searchParams.get("draft"),
    }
  );

  // Define the properties that indicate the start and end of an operation
  // These properties are used to determine the duration of operations in the logs
  const startProp = ["OP_CONNECTOR_START", "OP_DQM_TEST_START"];
  const endProp = ["OP_CONNECTOR_END", "OP_DQM_TEST_END"];

  // calculate duration of logs
  const calculateDurations = () => {
    // Create a shallow copy and reverse for processing, as in the original code
    const newLogs = [...(data || [])]?.reverse();
    let startTimestamp = null;

    newLogs?.forEach((log) => {
      if (
        !log ||
        typeof log.operation === "undefined" ||
        typeof log.timestamp === "undefined"
      ) {
        // Skip invalid log entries
        return;
      }

      if (startProp.includes(log.operation)) {
        startTimestamp = new Date(log.timestamp);
      }

      if (endProp.includes(log.operation) && startTimestamp) {
        const endTimestamp = new Date(log.timestamp);
        const durationInMilliseconds =
          endTimestamp.getTime() - startTimestamp.getTime();

        if (Number.isNaN(durationInMilliseconds)) {
          // Handle cases where timestamps might be invalid
          log.duration = "Invalid date";
        } else if (durationInMilliseconds < 0) {
          // Handle potential timestamp ordering issues
          log.duration = "Error: Negative duration";
        } else if (durationInMilliseconds < 1000) {
          log.duration = "< 1 sec";
        } else {
          // For durations >= 1000ms, round to the nearest second
          const roundedMilliseconds =
            Math.round(durationInMilliseconds / 1000) * 1000;

          // Create a dayjs duration object from the rounded milliseconds
          const dur = dayjs.duration(roundedMilliseconds);

          const days = dur.days();
          const hours = dur.hours();
          const minutes = dur.minutes();
          const seconds = dur.seconds();

          const parts = [];
          if (days > 0) {
            parts.push(`${days}d`);
          }
          if (hours > 0) {
            parts.push(`${hours}h`);
          }
          if (minutes > 0) {
            parts.push(`${minutes}m`);
          }

          // Add seconds if:
          // 1. It's non-zero.
          // 2. Or, if all larger units (days, hours, minutes) are zero,
          //    meaning the total rounded duration is less than 1 minute (e.g., 30s).
          if (seconds > 0 || (days === 0 && hours === 0 && minutes === 0)) {
            parts.push(`${seconds}s`);
          }

          // Join the parts. If parts is empty (e.g. roundedMilliseconds was 0, though unlikely here due to >=1000ms condition),
          // default to "0s". However, parts should always have at least one item if roundedMilliseconds >= 1000.
          log.duration = parts.length > 0 ? parts.join(" ") : "0s";
        }
      }
    });

    // Reverse again to restore original order before setting state
    setLogsData(newLogs?.reverse());
  };

  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (data) calculateDurations();
  }, [data]);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.logsDrawer &&
      localSettingsData?.body[0]?.value?.logsDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.logsDrawer?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.logsDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.logsDrawer.columns?.forEach((column) => {
        const index = COLUMNS.findIndex((item) => item.field === column);
        allColumns.push({
          ...COLUMNS[index],
          pinned: pinned?.includes(column) ? "left" : null,
          sort: sort?.find((val) => val.colId === column)?.sort || null,
        });
      });

      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const handleCancel = () => {
    setTimeout(() => {
      setTriggerChange((prev) => (!prev ? 1 : prev + 1));
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  // save table details to local settings
  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        logsDrawer: {
          columns: newColumns,
          pinned: pinned,
          filters: filters,
          sort: sort,
        },
      },
    };
    mutation.mutate(request);
  };

  return (
    <Wrapper
      ref={parentRef}
      theme={theme}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}
      <MyTable
        isError={isError}
        excelFileName="logs"
        data={[...(logsData || [])].map((d, index) => ({ ...d, id: index }))}
        loading={isLoading || isFetching}
        columns={columns}
        withRefresh
        height={`${height - 50}px`}
        emptyMessage="No logs"
        resetTrigger={triggerChange}
        detectChange={detectChange}
        displaySaveCancel={displaySaveCancel}
        onCancelClick={handleCancel}
        saveLoading={mutation.isLoading}
        onSaveClick={handleSave}
        onRefresh={() => refetch()}
        initialFilters={
          localSettingsData?.body[0]?.value?.logsDrawer?.filters || {}
        }
      />
    </Wrapper>
  );
};

const LogsContainer = withErrorBoundary(
  memo(LogsContainerBase),
  "error.generic"
);

export { LogsContainer };

const Wrapper = styled.div<{ theme: any }>`
  height: 100%;
  overflow: hidden;
  display: flex;
  width: 100%;
  padding: 10px;

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & td.attributes-data {
    overflow: auto;
  }
  & .item {
    display: flex;
    align-items: stretch;
    gap: 6px;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;
    margin-bottom: 6px;
    min-width: fit-content;
    & h6 {
      font-size: 13px;
      word-break: break-all;
      font-weight: 400;
      min-width: 60px;
      background-color: ${({ theme }) => theme.bgAttributes};
      border-right: 1px solid #eaeaea;
      width: 28%;
      text-align: left;
      max-width: 28%;
      color: ${({ theme }) => theme.colorPrimary};
      padding: 6px 10px;
      display: flex;
      align-items: center;
    }
    & p {
      padding: 6px;
      font-size: 13px;
      word-break: break-all;
      flex: 1;
      text-align: left;
      min-width: 60px;
      overflow: auto;
    }
  }

  & .ant-table-header > table,
  .ant-table-body > table {
    width: fit-content !important;
  }

  & td {
    cursor: auto !important;
  }

  & .table-wrapper {
    width: 100%;
  }

  & table {
    height: auto !important;
  }
  position: relative;
  & .anticon-export {
    font-size: 17px;
    margin-left: 10px;
  }
  & .ant-avatar {
    border-radius: 10px;
  }
`;
