import { styled } from "@linaria/react";
import { Checkbox, Divider, Input } from "antd";
import React, { useState } from "react";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  FileOutlined,
  PaperClipOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

const HomeFilterBase = ({ checkedList, setCheckedList }) => {
  const { t } = useTranslation();

  const OPTIONS = {
    filter1: ["Projects", "Repositories", "Tech Area", "Filter 1"],
    filter2: [
      { label: "Attachment", icon: <PaperClipOutlined /> },
      { label: "Person", icon: <UserOutlined /> },
      { label: "Documents", icon: <FileOutlined /> },
    ],
    filter3: ["Object name", "Repositories", "Developers"],
  };

  const [checkAll, setCheckAll] = useState({
    filter1: false,
    filter2: false,
    filter3: false,
  });

  const [indeterminate, setIndeterminate] = useState({
    filter1: false,
    filter2: false,
    filter3: false,
  });

  const [filters, setFilters] = useState({
    filter1: [...OPTIONS.filter1],
    filter2: [...OPTIONS.filter2],
    filter3: [...OPTIONS.filter3],
  });

  const onCheckAllChange = (e: CheckboxChangeEvent, name: string) => {
    setCheckedList({
      ...checkedList,
      [name]: e.target.checked ? filters[name] : [],
    });
    setIndeterminate({ ...indeterminate, [name]: false });
    setCheckAll({ ...checkAll, [name]: e.target.checked });
  };

  const onChange = (list: any[], name: string) => {
    setCheckedList({ ...checkedList, [name]: list });
    setIndeterminate({
      ...indeterminate,
      [name]: !!list.length && list.length < filters[name].length,
    });
    setCheckAll({ ...checkAll, [name]: list.length === filters[name].length });
  };

  const handleFilter = (event) => {
    if (!event.target.value) {
      setFilters({
        ...filters,
        [event.target.name]: OPTIONS[event.target.name],
      });
    } else {
      const filterItems = filters[event.target.name];
      let filtered;
      if (event.target.name === "filter2") {
        filtered = filterItems.filter((item) =>
          item.label.toLowerCase().includes(event.target.value.toLowerCase())
        );
      } else {
        filtered = filterItems.filter((item) =>
          item.toLowerCase().includes(event.target.value.toLowerCase())
        );
      }
      setFilters({ ...filters, [event.target.name]: filtered });
    }
  };

  return (
    <FilterWrapper>
      <h5>{t("Filters")}</h5>
      <Divider />
      <div className="grid">
        <Box>
          <div className="title">
            <Checkbox
              indeterminate={indeterminate.filter1}
              onChange={(e) => onCheckAllChange(e, "filter1")}
              checked={checkAll.filter1}
            >
              {t("Trees")}
            </Checkbox>
          </div>
          <Input
            placeholder={t("Search...")}
            name="filter1"
            onChange={handleFilter}
          />
          <Checkbox.Group
            options={filters.filter1}
            value={checkedList.filter1}
            onChange={(e) => onChange(e, "filter1")}
          />
        </Box>
        <Box>
          <div className="title">
            <Checkbox
              indeterminate={indeterminate.filter2}
              onChange={(e) => onCheckAllChange(e, "filter2")}
              checked={checkAll.filter2}
            >
              {t("Objects")}
            </Checkbox>
          </div>
          <Input
            placeholder={t("Search...")}
            name="filter2"
            onChange={handleFilter}
          />
          <Checkbox.Group
            value={checkedList.filter2}
            onChange={(e) => onChange(e, "filter2")}
          >
            {filters.filter2.map((item, index) => (
              <Checkbox checked value={item.label} key={index}>
                {item.icon}
                {item.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </Box>
        <Box>
          <div className="title">
            <Checkbox
              indeterminate={indeterminate.filter3}
              onChange={(e) => onCheckAllChange(e, "filter3")}
              checked={checkAll.filter3}
            >
              {t("Attributes")}
            </Checkbox>
          </div>
          <Input
            placeholder={t("Search...")}
            name="filter3"
            onChange={handleFilter}
          />
          <Checkbox.Group
            options={filters.filter3}
            value={checkedList.filter3}
            onChange={(e) => onChange(e, "filter3")}
          />
        </Box>
      </div>
    </FilterWrapper>
  );
};

const HomeFilter = withErrorBoundary(
  React.memo(HomeFilterBase),
  "error.generic"
);

export { HomeFilter };
const FilterWrapper = styled.div`
  & h5 {
    font-weight: 500;
  }

  & .ant-divider {
    margin: 5px 0px;
  }

  & .grid {
    display: flex;
    gap: 5px;
  }
`;

const Box = styled.div`
  flex: 1;
  background-color: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 5px;

  & input {
    border: none;
    box-shadow: 0px 2px 2px rgb(34 78 134 / 7%);
    border-radius: 0px;
  }

  & .title {
    background: var(--color-light);
    padding: 7px;

    & label {
      color: var(--color-text);
    }
  }

  & .ant-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 10px;
  }

  & .ant-checkbox-group-item,
  .ant-checkbox-wrapper {
    margin-left: 0px !important;
    color: var(--color-text);

    & .anticon {
      margin-right: 4px;
    }
  }
`;
