import { useDispatch, useSelector } from "react-redux";
import {
  Link,
  useLocation,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { setBreadcrumb, setParentBreadcrumbs } from "../../../store/features";
import { setSelected } from "../../../store/features/sidebar";
import { RootState } from "../../../store";
import { useQueryClient } from "react-query";
import { GET_CHILDRENS } from "../../../constants";
import { getAllNodes } from "../../../services/node";

const useHeaderGenerator = () => {
  const dispatch = useDispatch();
  const params = useParams();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const parentsBreadcrumb = useSelector(
    (state: RootState) => state.breadcrumbs.parentBreadcrumbs
  );
  const refreshBreadcrumbs = useSelector(
    (state: RootState) => state.breadcrumbs.refreshBreadcrumbs
  );

  const generateHeaderData = (treeData) => {
    return recursiveHeaderGenerator(treeData, null, []);
  };

  const recursiveHeaderGenerator = (tree, allowedChildrens, breadcrumbs) => {
    const allowedChildrenKeys = Object.keys(allowedChildrens || {});
    if (allowedChildrenKeys.length > 0) {
      return null;
    }

    return tree.map((node) => {
      const parentBreadcrumb = [
        ...breadcrumbs,
        {
          id: node?.id,
          title: node.name,
          disabled: Object.keys(node?.allowedChildren || {}).length === 0,
          isParent: true,
          parentId: node?.id,
          allowedChildrens:
            Object.keys(node?.allowedChildren || node?.allowedChildrens || {})
              .length > 0
              ? node?.allowedChildren || node?.allowedChildrens
              : null,
          isLeaf: Object.keys(node?.allowedChildren || {}).length === 0,
        },
      ];

      if (
        params?.nodeId == node.id &&
        (parentsBreadcrumb.length === 0 ||
          refreshBreadcrumbs ||
          searchParams.get("redirect"))
      ) {
        dispatch(setParentBreadcrumbs([...parentBreadcrumb]));
        dispatch(setBreadcrumb([]));
        dispatch(setSelected({ keys: [], info: [] }));
      }

      const newNode = {
        label: (
          <div className="header-menus">
            {Object.keys(node?.allowedChildren || {}).length > 0 ? (
              <Link
                to={
                  params?.nodeId == node.id && searchParams.get("nodeId")
                    ? `${location.pathname}?nodeId=${searchParams.get(
                        "nodeId"
                      )}`
                    : // : node?.name.startsWith("DQM")
                      // ? `/details/dqm/${node.id}`
                      `/details/${node.id}`
                }
                onClick={() => {
                  queryClient.prefetchQuery(
                    [GET_CHILDRENS, node.id?.toString()],
                    () => getAllNodes(node.id)
                  );
                  // if (params?.nodeId != node.id) {
                  //   dispatch(setBreadcrumb([]));
                  //   dispatch(setParentBreadcrumbs(parentBreadcrumb));
                  // }
                }}
              >
                {node.name || ""}
              </Link>
            ) : (
              <span className="submenu-title"> {node.name}</span>
            )}
          </div>
        ),
        key: node.id,
        breadcrumb: parentBreadcrumb,
        children:
          node?.children?.length > 0
            ? recursiveHeaderGenerator(
                node?.children,
                node?.allowedChildren,
                parentBreadcrumb
              )
            : null,
      };

      return newNode;
    });
  };
  return { generateHeaderData };
};

export { useHeaderGenerator };
