import {
  Flex,
  Image,
  Popconfirm,
  Tooltip,
  Upload,
  Button,
  Progress,
  Typography,
  Tag,
} from "antd";
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  FileOutlined,
  FileImageOutlined,
  DeleteOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { MyTable } from "../../../components";
import { useTranslation } from "react-i18next";
import {
  deleteAttachment,
  getFileList,
  uploadAttachment,
} from "../../../services";
import { UploadChangeParam } from "antd/es/upload";
import { UploadFile, RcFile } from "antd/lib/upload";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  useNotification,
  usePermissions,
} from "../../../utils/functions/customHooks";
import { GET_APP_PROPERTIES, GET_ICONS } from "../../../constants";
import { useEffect, useMemo, useState, useCallback, memo, useRef } from "react";
import { IAppProperties } from "../../../interfaces";
import { css } from "@linaria/core";
import { theme } from "../../../utils/theme";

const { Text } = Typography;

// Common variables for consistent spacing and colors
const SPACING = {
  xs: "4px",
  sm: "8px",
  md: "12px",
  lg: "16px",
  xl: "20px",
  xxl: "24px",
};

// Common colors from theme
const COLORS = {
  primary: theme.primary,
  secondary: theme.secondary,
  danger: "#ff4d4f",
  dangerHover: "#ff7875",
  success: "#52c41a",
  disabled: "#bfbfbf",
  border: "#f0f0f0",
  background: "#fafafa",
  hoverBg: "#f5f5f5",
};

// Reusable styles for file components
const fileItemStyles = `
  .file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: ${SPACING.sm} ${SPACING.md};
    border-radius: 4px;
    transition: background-color 0.3s;
    gap: ${SPACING.lg};

    &:hover {
      background-color: ${COLORS.hoverBg};
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: ${SPACING.lg};
    flex: 1;
    min-width: 0;
    justify-content: flex-start;
  }

  .file-icon {
    font-size: 16px;
    color: ${COLORS.primary};
    flex-shrink: 0;
  }

  .file-name-container {
    flex: 1;
    min-width: 0;
  }

  .file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }

  .file-error {
    flex-shrink: 0;
    color: ${COLORS.danger};
    max-width: 200px;
  }

  .error-message {
    color: ${COLORS.danger};
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    width: 100%;
  }

  .file-status {
    width: 100px;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
  }

  .file-size {
    font-size: 12px;
    width: 80px;
    text-align: right;
    flex-shrink: 0;
  }

  .file-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: 40px;
    justify-content: flex-end;
  }
`;

// Reusable styles for buttons and actions
const actionStyles = `
  .button-group {
    display: flex;
    gap: ${SPACING.sm};
  }

  .ag-danger {
    color: ${COLORS.danger};
    cursor: pointer;
    z-index: 2;
    position: relative;
  }

  .ag-danger:hover {
    color: ${COLORS.dangerHover};
  }

  .ag-danger.disabled {
    color: ${COLORS.disabled};
    cursor: not-allowed;
  }

  .ag-danger.disabled:hover {
    color: ${COLORS.disabled};
  }
`;

interface UploadButtonProps {
  allowedFileType: string;
}

const UploadButton = memo(({ allowedFileType }: UploadButtonProps) => {
  const { t } = useTranslation();

  return (
    <div className="upload-button">
      <PlusOutlined />
      <div className="upload-text">
        {t("Upload")}{" "}
        <span className="file-types">
          ({t("allowed file types")}:{" "}
          {allowedFileType?.replace(/\.(?=\w)/g, "")?.replace(/,(?=\S)/g, ", ")}
          )
        </span>
      </div>
    </div>
  );
});

interface FileListActionsProps {
  uploading: boolean;
  fileList: UploadFile[];
  hasFailedUploads: boolean;
  onCancel: () => void;
  onClearAll: () => void;
  onClearFailed: () => void;
  onUpload: () => void;
}

interface FileListProps {
  fileList: UploadFile[];
  onRemove: (file: UploadFile) => void;
  formatFileSize: (bytes: number) => string;
  uploading: boolean;
  currentFileIndex: number;
  uploadedFiles: string[];
  uploadErrors: { [key: string]: string };
}

interface UploadProgressProps {
  currentFileIndex: number;
  totalFiles: number;
}

const UploadProgress = memo(
  ({ currentFileIndex, totalFiles }: UploadProgressProps) => {
    const { t } = useTranslation();

    return (
      <div className="upload-progress">
        <Progress
          percent={Math.round((currentFileIndex / totalFiles) * 100)}
          status="active"
          format={() => `${t("Uploading")}: ${currentFileIndex}/${totalFiles}`}
          strokeColor={{ from: theme.primary, to: theme.secondary }}
        />
      </div>
    );
  }
);

const FileList = memo(
  ({
    fileList,
    onRemove,
    formatFileSize,
    uploading,
    currentFileIndex,
    uploadedFiles,
    uploadErrors,
  }: FileListProps) => {
    return (
      <div className="file-list-container">
        <div className="file-list">
          {fileList.map((file, index) => (
            <FileItem
              key={file.uid}
              file={file}
              onRemove={onRemove}
              formatFileSize={formatFileSize}
              uploading={uploading}
              currentFileIndex={currentFileIndex}
              fileIndex={index}
              uploadedFiles={uploadedFiles}
              uploadError={uploadErrors}
            />
          ))}
        </div>
      </div>
    );
  }
);

const FileListActions = memo(
  ({
    uploading,
    fileList,
    hasFailedUploads,
    onCancel,
    onClearAll,
    onClearFailed,
    onUpload,
  }: FileListActionsProps) => {
    const { t } = useTranslation();

    if (uploading) {
      return (
        <Popconfirm
          title={t("Cancel?")}
          description={t("Cancel remaining uploads?")}
          onConfirm={onCancel}
          okText={t("Yes")}
          cancelText={t("No")}
        >
          <Button danger>{t("Cancel Upload")}</Button>
        </Popconfirm>
      );
    }

    return (
      <div className="button-group">
        {fileList.length > 0 && (
          <Popconfirm
            title={t("Clear?")}
            description={t("Remove all attachments?")}
            onConfirm={onClearAll}
            okText={t("Yes")}
            cancelText={t("No")}
          >
            <Button>{t("Clear")}</Button>
          </Popconfirm>
        )}
        {hasFailedUploads && (
          <Button onClick={onClearFailed} danger>
            {t("Clear Failed")}
          </Button>
        )}
        <Button type="primary" onClick={onUpload}>
          {t("Upload")}
        </Button>
      </div>
    );
  }
);

interface FileItemProps {
  file: UploadFile;
  onRemove: (file: UploadFile) => void;
  formatFileSize: (bytes: number) => string;
  uploading: boolean;
  currentFileIndex: number;
  fileIndex: number;
  uploadedFiles: string[];
  uploadError?: { [key: string]: string };
}

// Reusable FileItem component with optimized rendering
const FileItem = memo(
  ({
    file,
    onRemove,
    formatFileSize,
    uploading,
    currentFileIndex,
    fileIndex,
    uploadedFiles,
    uploadError,
  }: FileItemProps) => {
    const { t } = useTranslation();

    const isUploaded = uploadedFiles.includes(file.uid);
    const hasError = uploadError && uploadError[file.uid];
    // Disable remove button if uploading and either this file is being uploaded or has been uploaded
    const isRemoveDisabled =
      uploading && (currentFileIndex > fileIndex || isUploaded);

    const FileIcon = file.type?.startsWith("image/") ? (
      <FileImageOutlined className="file-icon" />
    ) : (
      <FileOutlined className="file-icon" />
    );

    const StatusTag = () => {
      if (isUploaded) {
        return (
          <Tag color="success" bordered={false} icon={<CheckCircleOutlined />}>
            {t("Uploaded")}
          </Tag>
        );
      }
      if (hasError) {
        return (
          <Tag
            color="error"
            bordered={false}
            icon={<ExclamationCircleOutlined />}
          >
            {t("Failed")}
          </Tag>
        );
      }
      return null;
    };

    const ErrorMessage = hasError ? (
      <Tooltip title={uploadError[file.uid]}>
        <div className="file-error">
          <span className="error-message">{t(uploadError[file.uid])}</span>
        </div>
      </Tooltip>
    ) : null;

    const RemoveButton = isRemoveDisabled ? (
      <Tooltip title={t("Cannot remove during upload")}>
        <div className="ag-danger disabled">
          <DeleteOutlined />
        </div>
      </Tooltip>
    ) : (
      <Tooltip title={t("Remove")}>
        <div className="ag-danger" onClick={() => onRemove(file)}>
          <DeleteOutlined />
        </div>
      </Tooltip>
    );

    return (
      <div key={file.uid} className="file-item">
        <div className="file-info">
          {FileIcon}
          <div className="file-name-container">
            <Text
              className="file-name"
              ellipsis={{
                tooltip: { title: file.name, placement: "topLeft" },
              }}
            >
              {file.name}
            </Text>
          </div>
          {ErrorMessage}
          <div className="file-status">
            <StatusTag />
          </div>
          <Text type="secondary" className="file-size">
            {formatFileSize(file.size || 0)}
          </Text>
        </div>
        <div className="file-actions">{RemoveButton}</div>
      </div>
    );
  }
);

const CustomIcons = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const {
    contextHolder,
    showSuccessNotification,
    showErrorNotification,
    showErrorStyledNotification,
  } = useNotification();
  const { getPermissions } = usePermissions();

  const [, setLoading] = useState(false);
  const [allowedFileType, setAllowedFileType] = useState(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [uploadErrors, setUploadErrors] = useState<{ [key: string]: string }>(
    {}
  );
  const [cancelUpload, setCancelUpload] = useState(false);
  const [hasFailedUploads, setHasFailedUploads] = useState(false);
  const [hasRecentUpload, setHasRecentUpload] = useState(false);

  // Reference to track if cancel was triggered during an upload
  const isCancellingRef = useRef(false);

  useEffect(() => {
    const appProperties = queryClient.getQueryData(
      GET_APP_PROPERTIES
    ) as IAppProperties[];

    const allowedFileExtensions = appProperties?.find(
      (prop) => prop.key === "allowed.file.extensions.graphics"
    );
    if (allowedFileExtensions) {
      if (allowedFileExtensions?.value) {
        setAllowedFileType(
          allowedFileExtensions?.value
            ?.split(",")
            ?.map((ext) => `.${ext}`)
            ?.join()
        );
      } else {
        setAllowedFileType(null);
      }
    } else {
      setAllowedFileType("image/png, image/jpg, image/jpeg");
    }
  }, []);

  const {
    data: uploadedIcons,
    refetch: refetchIcons,
    isLoading,
    isFetching,
    isError,
  } = useQuery<any, any>([GET_ICONS, "ICON"], () => getFileList("ICON"));

  const handleChange = useCallback((info: UploadChangeParam<UploadFile>) => {
    if (info.file.status !== "uploading") {
      if (info.file.status !== "error") {
        setFileList((prevList) => {
          if (!prevList.some((f) => f.uid === info.file.uid)) {
            setTimeout(() => {
              try {
                const fileListElement = document.querySelector(".file-list");
                if (fileListElement) {
                  const fileItems =
                    fileListElement.querySelectorAll(".file-item");
                  if (fileItems.length > 0) {
                    const lastItem = fileItems[fileItems.length - 1];
                    lastItem.scrollIntoView({
                      behavior: "smooth",
                      block: "nearest",
                    });
                  } else {
                    fileListElement.scrollTop = fileListElement.scrollHeight;
                  }
                }

                const uploadFooter = document.querySelector(".upload-footer");
                if (uploadFooter) {
                  uploadFooter.scrollIntoView({
                    behavior: "smooth",
                    block: "end",
                  });
                }
              } catch {
                // Ignore any errors
              }
            }, 300);
            return [...prevList, info.file];
          }
          return prevList;
        });
      }
    }
  }, []);

  const handleCustomRequest = useCallback(({ onSuccess }: any) => {
    setTimeout(() => {
      onSuccess("ok");
    }, 0);
  }, []);

  const removeFile = useCallback(
    (file: UploadFile) => {
      // If file is already uploaded, also remove it from uploadedFiles
      if (uploadedFiles.includes(file.uid)) {
        setUploadedFiles((prev) => prev.filter((uid) => uid !== file.uid));
      }

      setFileList((prevList) =>
        prevList.filter((item) => item.uid !== file.uid)
      );
    },
    [uploadedFiles]
  );

  // Reset the entire upload state and clear all files
  const resetEntireUploadState = useCallback(() => {
    setFileList([]);
    setUploadedFiles([]);
    setUploadErrors({});
    setHasFailedUploads(false);
    setUploading(false);
    setCurrentFileIndex(0);
    setTotalFiles(0);
    setCancelUpload(false);
    isCancellingRef.current = false;
    setHasRecentUpload(false);
  }, []);

  const uploadAttachmentMutation = useMutation(uploadAttachment, {
    onSuccess: () => {
      setHasRecentUpload(true);
    },
    onError: (error: any) => {
      setLoading(false);

      if (!isCancellingRef.current) {
        if (error?.data?.code === "EX_FILE_EXTENSIONS") {
          showErrorStyledNotification(
            <span>
              {t("Error in uploading!")}{" "}
              <span style={{ color: "red" }}>{t("Illegal file type")}</span>
            </span>
          );
        }
      }
    },
  });

  const resetUploadState = useCallback(() => {
    setUploading(false);
    setCurrentFileIndex(0);
    setTotalFiles(0);
    setCancelUpload(false);
    isCancellingRef.current = false;
    setHasRecentUpload(false);
  }, []);

  const handleCancelUpload = useCallback(() => {
    isCancellingRef.current = true;

    setCancelUpload(true);

    setUploading(false);

    const uploadedCount = uploadedFiles.length;

    if (hasRecentUpload) {
      // Add a short delay to ensure all backend processing is complete
      setTimeout(() => {
        refetchIcons();
      }, 300);
    }

    // Show appropriate toast message based on how many files were uploaded
    if (uploadedCount > 0) {
      showSuccessNotification(
        t("Upload cancelled with {{count}} files uploaded", {
          count: uploadedCount,
        })
      );
    } else {
      showSuccessNotification(t("Upload cancelled"));
    }
  }, [
    showSuccessNotification,
    t,
    uploadedFiles.length,
    hasRecentUpload,
    refetchIcons,
  ]);

  const clearFailedUploads = useCallback(() => {
    const failedFileUids = Object.keys(uploadErrors);

    setFileList((prevList) =>
      prevList.filter((file) => !failedFileUids.includes(file.uid))
    );

    setUploadErrors({});
    setHasFailedUploads(false);
  }, [uploadErrors]);

  const clearAllFiles = useCallback(() => {
    // Clear all files from the list and reset the entire upload state
    resetEntireUploadState();
  }, [resetEntireUploadState]);

  const uploadFiles = useCallback(async () => {
    if (fileList.length === 0) return;

    // Reset cancellation flag at the start of a new upload
    isCancellingRef.current = false;
    setHasRecentUpload(false);

    setUploading(true);

    // Only count files that haven't been uploaded yet
    const filesToUpload = fileList.filter(
      (file) => !uploadedFiles.includes(file.uid)
    );
    setTotalFiles(filesToUpload.length + uploadedFiles.length);

    // Start with the count of already uploaded files
    setCurrentFileIndex(uploadedFiles.length);

    setUploadErrors({});
    setHasFailedUploads(false);
    setCancelUpload(false);

    let successCount = 0;
    let errorCount = 0;
    const successfullyUploadedFiles: string[] = [];
    const failedFiles: string[] = [];

    // Upload files sequentially, skipping already uploaded ones
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];

      if (uploadedFiles.includes(file.uid)) {
        continue;
      }

      if (isCancellingRef.current || cancelUpload) {
        break;
      }

      setCurrentFileIndex((prev) => prev + 1);

      if (file.status === "removed") continue;

      try {
        // Check again if cancel was triggered
        if (isCancellingRef.current || cancelUpload) break;

        const formData = new FormData();
        formData.append("fileType", "ICON");
        if (file.originFileObj) {
          formData.append("multipartFile", file.originFileObj);

          await uploadAttachmentMutation.mutateAsync(formData);

          // Check again if cancel was triggered during this upload
          if (isCancellingRef.current || cancelUpload) break;

          successCount++;
          successfullyUploadedFiles.push(file.uid);

          setUploadedFiles((prev) => [...prev, file.uid]);
          setHasRecentUpload(true);

          // Small delay to prevent UI lag
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      } catch (error: any) {
        // Check if cancel was triggered
        if (isCancellingRef.current || cancelUpload) break;
        errorCount++;
        failedFiles.push(file.uid);

        // If the file already exists, track it as an error with the specific message
        // Server code has been returned.
        if (error?.data?.code === "EX_FILE_EXISTS_ICON") {
          const errorMessage = t("A file with this name already exists");
          setUploadErrors((prev) => ({
            ...prev,
            [file.uid]: errorMessage,
          }));
        } else {
          const errorMessage =
            error?.data?.details || t("Error in uploading attachment");
          setUploadErrors((prev) => ({
            ...prev,
            [file.uid]: errorMessage,
          }));
        }
      }
    }

    // Check if upload was cancelled
    if (!isCancellingRef.current && !cancelUpload) {
      // Refresh the icons list if any files were uploaded
      if (successCount > 0 || hasRecentUpload) {
        setTimeout(() => {
          refetchIcons();
        }, 100);
      }

      // Check if there are any failed uploads
      const hasErrors = Object.keys(uploadErrors).length > 0;
      setHasFailedUploads(hasErrors);

      // Show appropriate notification
      if (successCount > 0) {
        if (successCount === filesToUpload.length) {
          showSuccessNotification(t("All attachments uploaded successfully"));
        } else {
          showSuccessNotification(
            `${successCount} ${t("attachments uploaded successfully")}`
          );
        }
      }

      // IMPORTANT: If all files have been processed (successfully or with errors),
      // and no cancellation occurred, clear the entire upload state
      if (successCount + errorCount === filesToUpload.length) {
        // Wait a bit for user to see the results before clearing
        setTimeout(() => {
          resetEntireUploadState();
        }, 1500);
      }
    }

    // Always reset upload state at the end, regardless of cancellation
    resetUploadState();
  }, [
    fileList,
    uploadAttachmentMutation,
    refetchIcons,
    t,
    showSuccessNotification,
    cancelUpload,
    resetUploadState,
    uploadErrors,
    uploadedFiles,
    hasRecentUpload,
    resetEntireUploadState,
  ]);

  const deleteMutation = useMutation(deleteAttachment, {
    onSuccess: () => {
      refetchIcons();
      showSuccessNotification("Attachment deleted successfully!");
    },
    onError: () => {
      showErrorNotification("Error in deleting attachment!");
    },
  });

  const COLUMNS = useMemo(() => {
    return [
      {
        field: "name",
        flex: 1,
        headerName: "Name",
        minWidth: 200,
      },
      {
        width: 200,
        field: "content",
        headerName: "Preview",
        cellRenderer: ({ value }) => {
          return (
            <Image
              src={`data:image/png;base64, ${value}`}
              width={40}
              height={30}
            />
          );
        },
      },
      {
        headerName: "Uploaded By",
        field: "user",
        width: 200,
        cellRenderer: () => "-",
      },

      {
        headerName: "Date",
        width: 150,
        field: "date",
        cellRenderer: () => {
          return "-";
        },
      },

      {
        field: "actions",
        headerName: "Actions",
        isAction: true,
        width: 100,
        cellRenderer: ({ data }) => {
          return (
            <div className="ag-actions">
              <Tooltip title={t("Download")}>
                <div
                  onClick={() => {
                    handleDownload(data?.content, data.name);
                    showSuccessNotification(
                      "Attachment downloaded successfully!"
                    );
                  }}
                >
                  <DownloadOutlined />
                </div>
              </Tooltip>

              <Popconfirm
                title={`${t("Delete")}?`}
                description={t("Are you sure to delete this attachment?")}
                onConfirm={() => deleteMutation.mutate(data?.id)}
                okButtonProps={{ loading: deleteMutation.isLoading }}
                okText={t("Yes")}
                cancelText={t("No")}
              >
                <Tooltip title={t("Delete")}>
                  <div className="ag-danger">
                    <DeleteOutlined />
                  </div>
                </Tooltip>
              </Popconfirm>
            </div>
          );
        },
      },
    ];
  }, []);

  const beforeUpload = useCallback(
    (file: RcFile) => {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        showErrorNotification(t("File must be smaller than 1MB!"));
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    [showErrorNotification, t]
  );

  // Format file size to human-readable format
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes < 1024) return bytes + " B";
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
    return (bytes / (1024 * 1024)).toFixed(1) + " MB";
  }, []);

  const handleDownload = useCallback((content: string, name: string) => {
    const link = document.createElement("a");
    link.href = `data:image/png;base64, ${content}`;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  const handleMultipleDownload = useCallback(
    (selected: any[]) => {
      selected?.forEach((attachment) => {
        handleDownload(attachment?.content, attachment?.name);
      });
    },
    [handleDownload]
  );

  const PERMISSIONS = useMemo(() => {
    return getPermissions(0);
  }, []);

  return (
    <Flex vertical>
      {contextHolder}
      <h4>{t("Icons")}</h4>

      <MyTable
        isError={isError}
        loading={isLoading || isFetching}
        columns={COLUMNS}
        onDownload={(selected) => {
          handleMultipleDownload(selected);
        }}
        data={uploadedIcons || []}
      />

      {PERMISSIONS.includes("ATTACH") && (
        <UploadArea
          allowedFileType={allowedFileType}
          beforeUpload={beforeUpload}
          handleChange={handleChange}
          handleCustomRequest={handleCustomRequest}
          fileList={fileList}
          uploading={uploading}
          currentFileIndex={currentFileIndex}
          totalFiles={totalFiles}
          uploadedFiles={uploadedFiles}
          uploadErrors={uploadErrors}
          hasFailedUploads={hasFailedUploads}
          onRemove={removeFile}
          onCancel={handleCancelUpload}
          onClearAll={clearAllFiles}
          onClearFailed={clearFailedUploads}
          onUpload={uploadFiles}
          formatFileSize={formatFileSize}
        />
      )}
    </Flex>
  );
};

// Reusable upload area component
interface UploadAreaProps {
  allowedFileType: string;
  beforeUpload: (file: RcFile) => boolean | string;
  handleChange: (info: UploadChangeParam<UploadFile>) => void;
  handleCustomRequest: ({ onSuccess }: any) => void;
  fileList: UploadFile[];
  uploading: boolean;
  currentFileIndex: number;
  totalFiles: number;
  uploadedFiles: string[];
  uploadErrors: { [key: string]: string };
  hasFailedUploads: boolean;
  onRemove: (file: UploadFile) => void;
  onCancel: () => void;
  onClearAll: () => void;
  onClearFailed: () => void;
  onUpload: () => void;
  formatFileSize: (bytes: number) => string;
}

const UploadArea = memo(
  ({
    allowedFileType,
    beforeUpload,
    handleChange,
    handleCustomRequest,
    fileList,
    uploading,
    currentFileIndex,
    totalFiles,
    uploadedFiles,
    uploadErrors,
    hasFailedUploads,
    onRemove,
    onCancel,
    onClearAll,
    onClearFailed,
    onUpload,
    formatFileSize,
  }: UploadAreaProps) => {
    return (
      <div className={uploadSectionCss}>
        <div className="upload-area">
          <div className="upload-center">
            <Upload
              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={handleChange}
              customRequest={handleCustomRequest}
              accept={allowedFileType}
              multiple={true}
              disabled={uploading}
            >
              <UploadButton allowedFileType={allowedFileType} />
            </Upload>
          </div>

          {fileList.length > 0 && (
            <>
              <FileList
                fileList={fileList}
                onRemove={onRemove}
                formatFileSize={formatFileSize}
                uploading={uploading}
                currentFileIndex={currentFileIndex}
                uploadedFiles={uploadedFiles}
                uploadErrors={uploadErrors}
              />

              {uploading && (
                <UploadProgress
                  currentFileIndex={currentFileIndex}
                  totalFiles={totalFiles}
                />
              )}

              <div className="upload-footer">
                <FileListActions
                  uploading={uploading}
                  fileList={fileList}
                  hasFailedUploads={hasFailedUploads}
                  onCancel={onCancel}
                  onClearAll={onClearAll}
                  onClearFailed={onClearFailed}
                  onUpload={onUpload}
                />
              </div>
            </>
          )}
        </div>
      </div>
    );
  }
);

export { CustomIcons };

const uploadSectionCss = css`
  margin-top: ${SPACING.xl};

  .upload-area {
    background-color: ${COLORS.background};
    border: 1px solid ${COLORS.border};
    border-radius: 4px;
    padding: ${SPACING.lg};
    transition: all 0.3s;

    &:hover {
      border: 1px dashed ${COLORS.primary};
    }
  }

  .upload-center {
    display: flex;
    justify-content: center;
  }

  .upload-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
  }

  .file-types {
    font-size: 12px;
    margin-left: ${SPACING.xs};
    color: black;
    font-weight: 400;
  }

  .file-list-container {
    margin-top: ${SPACING.lg};
  }

  .file-list {
    max-height: 320px;
    overflow-y: auto;
    padding-right: ${SPACING.xs};
  }

  ${fileItemStyles}

  .upload-progress {
    margin: ${SPACING.md} 0;
  }

  .upload-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: ${SPACING.md};
  }

  ${actionStyles}

  /* Responsive styles */
  @media (max-width: 768px) {
    .file-item {
      flex-wrap: wrap;
    }

    .file-info {
      width: 100%;
      flex-wrap: wrap;
    }

    .file-error {
      margin-left: 32px;
      max-width: 100%;
    }
  }
`;
