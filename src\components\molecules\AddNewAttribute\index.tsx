import { PlusOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Button, Select } from "antd";
import { memo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useFlags } from "../../../utils/functions/customHooks";
import { setDisplaySaveButton } from "../../../store/features/sidebar";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

interface Props {
  open: boolean;
  onClose: () => void;
  data: any[];
  onAddAttributes: any;
}

const AddNewAttributeBase = ({ onClose, data, onAddAttributes }: Props) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { getFlags } = useFlags();

  const filteredData = data?.filter((item) => {
    if (!item.bitFlag) return true;
    return !getFlags(item.bitFlag).includes("EDIT_VALUE_OFF");
  });

  const [checkedList, setCheckedList] = useState([]);

  return (
    <Wrapper>
      <Select
        mode="multiple"
        options={filteredData?.map((item) => {
          return { ...item, title: null };
        })}
        popupClassName="multi-select"
        onChange={(values) => {
          setCheckedList(values);
        }}
        filterOption={(input, option) => {
          return (option?.label?.toLowerCase() ?? "").includes(
            input.toLowerCase()
          );
        }}
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? "")
            .toLowerCase()
            .localeCompare((optionB?.label ?? "").toLowerCase())
        }
      />

      <Button
        type="primary"
        onClick={() => {
          onAddAttributes(checkedList);
          onClose();
          dispatch(setDisplaySaveButton(true));
        }}
      >
        <PlusOutlined />
        {t("Add")}
      </Button>
    </Wrapper>
  );
};

export const AddNewAttribute = withErrorBoundary(
  memo(AddNewAttributeBase),
  "error.generic"
);

const Wrapper = styled.div`
  display: flex;
  flex: 1;
  overflow: auto;
  font-size: 13px;
  margin-top: 14px;
  padding: 0px 10px 14px 10px;
  gap: 10px;
  & button {
    box-shadow: none;
  }

  & .ant-select {
    flex: 1;
  }
`;
