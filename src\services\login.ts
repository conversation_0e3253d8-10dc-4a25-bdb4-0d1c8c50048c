import { API } from "../utils/api";

export const loginService = (values) => {
  return API.post(`/auth/login`, {
    login: values.username,
    pass: values.password,
  });
};

export const getProfile = () => {
  return API.get(`/settings/profile/local/get`);
};

export const getCurrentUser = () => {
  return API.get(`/auth/current`);
};

export const logoutService = () => {
  return API.post(`/auth/logout`);
};

export const changePasswordService = (values) => {
  return API.post(`/auth/update`, values);
};
