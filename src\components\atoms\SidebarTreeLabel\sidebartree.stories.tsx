import type { <PERSON>a, StoryObj } from "@storybook/react";
import { SidebarTreeLabel } from "./index";

const meta: Meta<typeof SidebarTreeLabel> = {
  title: "Components/Atoms/SidebarTreeLabel",
  component: SidebarTreeLabel,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof SidebarTreeLabel>;

export const Basic: Story = {
  args: {
    label: "Label",
    id: 1,
  },
};
