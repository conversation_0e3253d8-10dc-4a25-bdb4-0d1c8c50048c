import { Input } from "antd";
import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { FormItem } from "./styles";

const EditIfFrame = ({ errors, onFieldValueChange, values }) => {
  const inputRef = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    inputRef?.current?.focus();
  }, []);

  return (
    <section>
      <FormItem>
        <label>URL</label>
        <Input
          ref={inputRef}
          onChange={(e) => {
            onFieldValueChange("url", e.target.value);
          }}
          value={values?.url || ""}
        />

        {errors?.url?.message && (
          <p className="error">{errors?.url.message as any}</p>
        )}
      </FormItem>

      <FormItem>
        <label>{t("Width")}</label>
        <Input
          onChange={(e) => {
            onFieldValueChange("width", e.target.value);
          }}
          value={values?.width || ""}
        />

        {errors?.width?.message && (
          <p className="error">{errors?.width.message as any}</p>
        )}
      </FormItem>

      <FormItem>
        <label>{t("Height")}</label>
        <Input
          onChange={(e) => {
            onFieldValueChange("height", e.target.value);
          }}
          value={values?.height || ""}
        />

        {errors?.height?.message && (
          <p className="error">{errors?.height.message as any}</p>
        )}
      </FormItem>
    </section>
  );
};

export { EditIfFrame };
