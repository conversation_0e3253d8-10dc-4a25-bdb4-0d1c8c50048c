import { getAttributeIcon } from "../../constants";
import { ITemplates } from "../../interfaces";

export const getTemplateIcon = (
  allTemplates: { number: ITemplates },
  templateID: number
) => {
  if (!allTemplates) {
    return "-";
  }
  let templateIcon = "_30_folder";
  const selectedTemplate = allTemplates[Number(templateID)];
  if (selectedTemplate) {
    templateIcon = selectedTemplate.icon;
  }
  return getAttributeIcon(templateIcon);
};
