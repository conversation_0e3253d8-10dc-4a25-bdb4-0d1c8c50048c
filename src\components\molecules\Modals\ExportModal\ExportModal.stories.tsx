import type { Meta, StoryObj } from "@storybook/react";
import { ExportModal } from ".";
import { Button } from "antd";
import { useState } from "react";

const ModalComponent = () => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button type="primary" onClick={() => setOpen(true)}>
        Click to open export modal
      </Button>
      <ExportModal isOpen={open} onClose={() => setOpen(false)} />
    </>
  );
};

const meta: Meta<typeof ExportModal> = {
  title: "Components/Molecules/Modals/ExportModal",
  component: ModalComponent,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof ExportModal>;

export const Basic: Story = {};
