// for showing preview of all header data in MenuCreater Modal

export const generateHeaderPreview = (allDatas) => {
  const headerItems = [];
  allDatas?.forEach((menu) => {
    headerItems.push({
      label: <a>{menu?.name || ""}</a>,
      key: menu?.key,
      children:
        menu?.children?.length > 0
          ? generateHeaderPreview(menu?.children)
          : null,
    });
  });
  return headerItems;
};
