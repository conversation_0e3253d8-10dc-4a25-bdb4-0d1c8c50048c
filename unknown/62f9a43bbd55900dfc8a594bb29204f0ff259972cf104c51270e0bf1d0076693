import { styled } from "@linaria/react";
import { useEffect, useState } from "react";
import { AttributeContent, BreadCrumb, Item, MyTable } from "../../components";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { useTheme } from "../../utils";
import { useNotification } from "../../utils/functions/customHooks";
import { Button, Flex, Select } from "antd";
import { saveUserProfile } from "../../services";
import { useMutation, useQuery } from "react-query";
import { GET_PROFILE } from "../../constants";
import { getProfile } from "../../services/login";
import { RootState } from "../../store";

//  Not used currently
//  used in future

const Newsletter = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [value, setValue] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const FREQUENCY_OPTIONS = [
    {
      label: t("Once a day"),
      value: "DAILY",
    },
    {
      label: t("Once a week"),
      value: "WEEKLY",
    },
  ];

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const { data, refetch } = useQuery<any>(GET_PROFILE, () => getProfile());
  // const { data: newsletters, refetch: refetchNewsletters } = useQuery<any>(
  //   [GET_NEWSLETTER, value],
  //   () => getNewsletters(value),
  //   {
  //     enabled: !!value,
  //   }
  // );

  useEffect(() => {
    if (data) {
      setValue(data?.body?.[0]?.value || null);
    }
  }, [data]);

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));
  }, []);

  const getValue = () => {
    return (
      FREQUENCY_OPTIONS?.find((freq) => freq.value === value)?.label || "-"
    );
  };

  const mutation = useMutation(saveUserProfile, {
    onSuccess: () => {
      setIsEditing(false);
      showSuccessNotification("Frequency updated successfully!");
      refetch();

      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleCancel = () => {
    dispatch(setMask(false));
    setValue(data?.body?.[0]?.value || null);
    setIsEditing(false);
  };

  const handleSave = () => {
    mutation.mutate(value);
  };

  return (
    <Wrapper theme={theme}>
      <BreadCrumb
        extra={
          <Flex gap={10}>
            {mask && (
              <>
                <Button
                  className="breadcrumb-button cancel-button"
                  type="primary"
                  onClick={handleCancel}
                >
                  {t("Cancel")}
                </Button>
                <Button
                  className="breadcrumb-button save-button"
                  type="primary"
                  onClick={handleSave}
                  loading={mutation.isLoading}
                >
                  {t("Save")}
                </Button>
              </>
            )}
          </Flex>
        }
      />
      {contextHolder}

      <div className="content">
        <div
          className="container"
          style={{ border: mask ? "1px solid red" : "none" }}
        >
          <Item theme={theme}>
            <h6 onClick={() => setIsEditing((val) => !val)}>
              {t("Frequency")}
            </h6>

            <AttributeContent theme={theme} id="select-wrapper">
              {isEditing ? (
                <Select
                  getPopupContainer={() =>
                    document.getElementById("select-wrapper")
                  }
                  defaultOpen
                  options={FREQUENCY_OPTIONS}
                  value={value}
                  onChange={(values) => {
                    setValue(values);
                    dispatch(setMask(true));
                  }}
                />
              ) : (
                getValue()
              )}
            </AttributeContent>
          </Item>
        </div>

        <div className="bottom">
          {mask && <div className="mask" />}
          <div className="container">
            <p>{t("You are subscribe to objects of type:")}</p>
            <MyTable
              data={[]}
              columns={[{ label: t("Asset Name"), key: "name" }]}
            />
          </div>

          <div className="container">
            <p>{t("Your subscribed objects:")}</p>
            <MyTable
              data={[]}
              columns={[{ label: t("Asset Name"), key: "name" }]}
            />
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

export default Newsletter;

const breadcrumb = [
  {
    title: "Newsletter",
    to: "/newsletter",
  },
];

const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  display: flex;
  flex-direction: column;

  & .bottom {
    flex: 1;
    position: relative;
  }
  & .mask {
    height: calc(100% + 16px);
    left: -10px;
    right: -10px;
    bottom: -10px;
  }

  & .content {
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  & .container {
    background: white;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;

    & > p {
      font-size: 14px;
      color: var(--color-text);
    }
  }
`;
