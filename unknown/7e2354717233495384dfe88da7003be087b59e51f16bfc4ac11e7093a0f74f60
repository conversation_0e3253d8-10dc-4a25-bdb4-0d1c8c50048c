import { Float<PERSON><PERSON>on, Popover } from "antd";
import { useRef, useState } from "react";
import { HomeLayoutOptions } from "../../components";
import Draggable from "react-draggable";
import { SettingFilled } from "@ant-design/icons";

const CustomizeIcon = ({ detectChange, iconPosition, setIconPosition }) => {
  const dragStartPosition = useRef(iconPosition);
  const clickStartTime = useRef(0);
  const clickThreshold = 300;
  const moveThreshold = 5;

  const [settingsPopupOpen, setSettingsPopupOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // Helper to check if movement exceeds threshold
  const hasSignificantMovement = (clientX, clientY) => {
    const dx = Math.abs(clientX - dragStartPosition.current.x);
    const dy = Math.abs(clientY - dragStartPosition.current.y);
    return dx > moveThreshold || dy > moveThreshold;
  };

  return (
    <Popover
      placement="topRight"
      trigger={["click"]}
      onOpenChange={(open) => setSettingsPopupOpen(open)}
      open={settingsPopupOpen}
      content={<HomeLayoutOptions detectChange={detectChange} />}
    >
      <Draggable
        position={iconPosition}
        onDrag={(e: unknown) => {
          if (hasSignificantMovement(e["clientX"], e["clientY"])) {
            setSettingsPopupOpen(false);
            setIsDragging(true);
          }
        }}
        onStart={(e: any) => {
          dragStartPosition.current = { x: e.clientX, y: e.clientY };
          clickStartTime.current = Date.now();
        }}
        onStop={(e: unknown, data) => {
          if (hasSignificantMovement(e["clientX"], e["clientY"])) {
            detectChange();
          }

          setIconPosition({ x: data.x, y: data.y });
          setTimeout(() => setIsDragging(false), 0);
        }}
      >
        <FloatButton
          onClick={(e) => {
            if (isDragging) {
              e.preventDefault();
              e.stopPropagation();
              return;
            }

            const clickDuration = Date.now() - clickStartTime.current;

            if (
              clickDuration < clickThreshold &&
              !hasSignificantMovement(e.clientX, e.clientY)
            ) {
              setSettingsPopupOpen(!settingsPopupOpen);
            }
          }}
          type="default"
          icon={<SettingFilled />}
        />
      </Draggable>
    </Popover>
  );
};

export { CustomizeIcon };
