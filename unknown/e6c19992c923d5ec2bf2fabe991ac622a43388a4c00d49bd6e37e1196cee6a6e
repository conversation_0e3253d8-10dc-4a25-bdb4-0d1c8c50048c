import { IHistory } from "../interfaces";
import { API } from "../utils/api";

export const getMetamodelHistory = (id: string): Promise<IHistory[]> => {
  return API.post(`/metamodel/history/get`, {
    ids: [id],
    allByCurrentPerson: false,
  });
};

export const getAllNodesHistory = (): Promise<IHistory[]> => {
  return API.post(`/model/history/get`, {
    ids: null,
    allByCurrentPerson: true,
  });
};

export const getNodeHistory = (id: string): Promise<IHistory[]> => {
  return API.post(`/model/history/get`, {
    ids: [id],
    allByCurrentPerson: false,
  });
};

export const getNodeLogs = (id: string) => {
  return API.post(`/model/log/get`, {
    ids: [id],
    allByCurrentPerson: false,
  });
};
