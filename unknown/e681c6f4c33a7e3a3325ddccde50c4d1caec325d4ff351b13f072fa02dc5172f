import { SearchOutlined } from "@ant-design/icons";
import { styled } from "@linaria/react";
import { Input } from "antd";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useQuery } from "react-query";
import { GET_TREE_SEARCH_RESULTS } from "../../../constants";
import { getTreeSearchResults } from "../../../services/search";
import { useParams, useSearchParams } from "react-router-dom";

// Search used in tree in Sidebar and Trashcan

const SearchInputWithFilter = ({ onSearch, parentId }) => {
  const { t } = useTranslation();
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();

  const [searchText, setSearchText] = useState("");
  const [inputValue, setInputValue] = useState("");

  const setDebouncedSearchText = useCallback(
    debounce((value) => setSearchText(value), 500),
    []
  );

  useEffect(() => {
    setInputValue("");
    setSearchText("");
  }, [params?.nodeId]);

  // fetching search results
  const { data } = useQuery(
    [GET_TREE_SEARCH_RESULTS, parentId, searchText],
    () => getTreeSearchResults(parentId, searchText),
    {
      enabled: !!searchText,
    }
  );

  useEffect(() => {
    if (!data) {
      return;
    }
    const newParams = new URLSearchParams(searchParams);
    newParams.delete("nodeId");
    setSearchParams(newParams);
    onSearch(data, searchText);
  }, [data]);

  return (
    <SearchWrapper
      className="search-wrapper"
      onClick={(e) => e.stopPropagation()}
    >
      <Input
        name="search"
        allowClear
        value={inputValue}
        placeholder={`${t("Filter")}`}
        prefix={<SearchOutlined />}
        onChange={(e) => {
          setInputValue(e.target.value);
          setDebouncedSearchText(e.target.value);
          if (!e.target.value) {
            onSearch([], e.target.value);
          }
        }}
      />
    </SearchWrapper>
  );
};

export { SearchInputWithFilter };

const SearchWrapper = styled.div`
  width: 100%;

  & .ant-input-affix-wrapper {
    background: #ffffff;
    border-radius: 4px;
  }

  & input {
    background: #ffffff;
  }

  & .ant-input-suffix {
    cursor: pointer;

    & path {
      transition: all 0.5s;
    }

    &:hover path {
      fill: #8c8c8c;
    }
  }

  & .ant-input-prefix > span {
    color: #bfc3d9;
    fill: #bfc3d9;
  }
`;
