import { styled } from "@linaria/react";

export const SearchWrapper = styled.div`
  padding: 20px 12px;
  border: 1px solid #eee;
  border-radius: 8px;

  & input {
    display: block;
    margin-bottom: 14px;
    border-radius: 4px;
  }

  & .button-wrapper {
    display: flex;
    align-items: center;

    & button {
      min-width: 38px;
      font-size: 12px;
      border-radius: 4px;
      box-shadow: none;
      padding: 4px 10px;
    }

    & a {
      margin-left: auto;
      color: #134d81;
      font-size: 13px;
    }
  }
`;

export const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  & .title-container {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  /* background-color: #fff !important; */

  & .has-mask {
    border: 1px solid red;
  }

  & img {
    object-fit: contain;
  }
  & .content {
    flex: 1;
  }
  & .table-wrapper {
    padding-bottom: 0px !important;
    height: 100%;
  }

  & .ant-table-body {
    background-color: #f3f3f3 !important;
  }
  & .ant-tag {
    background: ${({ theme }) => theme.bgAttributes};
    margin-right: 0px;
    border: 1px solid ${({ theme }) => theme.colorSecondary};
    color: ${({ theme }) => theme.colorSecondary};
  }

  & .ant-dropdown-trigger {
    font-size: 13px;
  }

  & .content {
    & > div {
      padding: 10px;
    }
  }

  & .item {
    display: flex;
    align-items: stretch;
    gap: 6px;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;
    margin-bottom: 6px;
    min-width: fit-content;

    & h6 {
      font-size: 13px;
      font-weight: 400;
      background-color: ${({ theme }) => theme.bgAttributes};
      border-right: 1px solid #eaeaea;
      text-align: left;
      color: ${({ theme }) => theme.colorPrimary};
      padding: 6px;
      display: flex;
      align-items: baseline;
    }
    & p {
      font-size: 13px;
      padding: 6px 6px 6px 0px;
      word-break: break-all;
      flex: 1;
      text-align: left;
      min-width: 60px;
      overflow: auto;
    }
  }
`;

export const LoadMore = styled.div`
  font-size: 20px;
  display: flex;
  padding: 5px !important;
  justify-content: center;
`;
