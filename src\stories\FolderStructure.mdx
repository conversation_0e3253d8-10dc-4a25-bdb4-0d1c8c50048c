import { Meta } from "@storybook/blocks";
import AtomicImage from "./images/atomic.webp";

<Meta title="Documentation/FolderStructure" />;

# Introduction

This is the basic folder structure that you can see from outside. Let me discuss with you in detail.

```
- src
  - assets
  - components
    - atoms
       - MyComponent1
          - index.tsx
          - index.stories.tsx
       -  MyComponent2
          - index.tsx
          - index.stories.tsx
       - index.ts
    - molecules
       - MyComponent1
          - index.tsx
          - index.stories.tsx
       -  MyComponent2
          - index.tsx
          - index.stories.tsx
       - index.ts
    - organisms
       - MyComponent1
          - index.tsx
          - index.stories.tsx
       -  MyComponent2
          - index.tsx
          - index.stories.tsx
       - index.ts
    - index.ts

  - constants
    - header.ts
    - common.ts
    - index.ts

  - interfaces
     - common.ts
     - header.ts
     - index.ts

  - locales
     - en
     - pl

  - pages
    - login
       - index.tsx

  - services
     - header.ts
     - index.ts

  - utils
    - theme.ts
    - index.ts

```

## assets

This contains static files such as animations, svgs, and other image files.

## components

Now, comes the most important folder structure, components
I follow atomic design approach in which smaller components are combined to create larger components.

<img src={AtomicImage} />

In atoms, the smallest components like `Buttons`, `Checkboxes`, `Toasters`, etc. are stored.

Molecules folder contains those components that are larger than atoms and make use of atomic components, such as `Modals`, `Cards`, etc.

In organisms, there are more larger components that are directly used inside pages, such as `Header`, `Footer`, etc.

## constants

All the constants, such as dropdown items, context menu items, are placed here.

## interfaces

This is also similar to constants folder. All reusable interfaces are placed inside interfaces folder.

## locales

All translations needed for react-i18next and localization are stored here.

## pages

This folder all the pages that you will have inside your project.

## services

This contains all the API calls that will be used in this application.

## utils

This contains utility function such as theme configuration, and other reusable functions that is used throughout the project.
