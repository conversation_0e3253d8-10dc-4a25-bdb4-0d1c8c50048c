import {
  AccountBookOutlined,
  ApartmentOutlined,
  A<PERSON>toreOutlined,
  AuditOutlined,
  BgColorsOutlined,
  // ClockCircleOutlined,
  ClusterOutlined,
  ControlOutlined,
  EyeOutlined,
  SettingOutlined,
  Slide<PERSON>Outlined,
  TeamOutlined,
  TranslationOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { USER_GROUP_ID, USER_ID } from "./attributeIds";

export const SETTINGS_ITEMS = [
  {
    url: "/settings/profile",
    to: "/settings/profile",
    icon: <UserOutlined />,
    label: "Personal profile",
    allowedRoles: ["admin", "user", "no-person"],
  },

  // {
  //   url: "/pinned",
  //   icon: <PushpinOutlined />,
  //   label: "Pinned",
  // },
  // {
  //   url: "/message",
  //   icon: <MessageOutlined />,
  //   label: "Messages",
  // },

  // {
  //   url: "/history",
  //   icon: <HistoryOutlined />,
  //   label: "History",
  // },
  {
    url: "/business-log",
    to: "/business-log",
    icon: <AccountBookOutlined />,
    label: "Business Log",
    allowedRoles: ["admin"],
  },
  {
    url: "/technical-log",
    to: "/technical-log",
    icon: <AuditOutlined />,
    label: "Technical Log",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/data-sources/-2",
    to: "/settings/data-sources/-2",
    icon: <ClusterOutlined />,
    label: "Data Sources",
    allowedRoles: ["admin"],
  },
  // {
  //   url: "/schedules",
  //   icon: <ClockCircleOutlined />,
  //   label: "Schedules",
  // },
  {
    url: `/settings/users/-4`,
    to: `/settings/users/${USER_ID}`,
    icon: <TeamOutlined />,
    label: "Users",
    allowedRoles: ["admin"],
  },
  {
    url: `/settings/user-groups/${USER_GROUP_ID}`,
    to: `/settings/user-groups/${USER_GROUP_ID}`,
    icon: <TeamOutlined />,
    label: "User Groups",
    isNew: true,
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/actions/-6",
    to: "/settings/actions/-6",
    icon: <EyeOutlined />,
    label: "Actions",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/roles/-5",
    to: "/settings/roles/-5",
    icon: <ApartmentOutlined />,
    label: "Roles",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/general",
    to: "/settings/general",
    icon: <SettingOutlined />,
    label: "General Settings",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/translations",
    to: "/settings/translations",
    icon: <TranslationOutlined />,
    label: "Translations",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/theme",
    to: "/settings/theme",
    icon: <BgColorsOutlined />,
    label: "Theme & Layout",
    allowedRoles: ["admin", "user"],
  },
  {
    url: "/settings/menu-creator",
    to: "/settings/menu-creator",
    icon: <AppstoreOutlined />,
    label: "Menu Creator",
    allowedRoles: ["admin"],
  },
  // {
  //   url: "/details/dqm/179",
  //   icon: <BarChartOutlined />,
  //   label: "DQM",
  // },
  {
    url: "/settings/metamodel/-1",
    to: "/settings/metamodel/-1",
    icon: <ControlOutlined />,
    label: "Metamodel",
    allowedRoles: ["admin"],
  },
  {
    url: "/settings/app-properties",
    to: "/settings/app-properties",
    icon: <SlidersOutlined />,
    label: "Application Properties",
    allowedRoles: ["admin"],
  },
];
