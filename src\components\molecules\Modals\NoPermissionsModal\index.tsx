import { styled } from "@linaria/react";
import { Dialog } from "primereact/dialog";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";
import React from "react";

const NoPermissionsModalBase = ({ visible, onHide }) => {
  const { t } = useTranslation();

  return (
    <Dialog visible={visible} onHide={onHide} style={{ width: "50vw" }}>
      <Wrapper>
        <i className="pi pi-exclamation-triangle" />
        {t("You do not have permission to view the characteristics")}
      </Wrapper>
    </Dialog>
  );
};

const NoPermissionsModal = withErrorBoundary(
  React.memo(NoPermissionsModalBase),
  "error.generic"
);

export { NoPermissionsModal };

const Wrapper = styled.div`
  font-size: 13px;
  display: flex;
  color: black;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 10px;
  text-align: center;

  & i {
    font-size: 26px;
    color: orange;
  }
`;
