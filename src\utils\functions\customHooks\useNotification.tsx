import { Button, Space, notification } from "antd";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

const useNotification = () => {
  const [api, contextHolder] = notification.useNotification();
  const { t } = useTranslation();

  const clearAllNotification = () => {
    api.destroy();
  };

  const showInfoNotification = (
    description: string,
    message?: string,
    buttons?: ReactNode,
    icon?: ReactNode
  ) => {
    api.open({
      message: message ? t(message) : t("Success!"),
      description: t(description),
      type: "info",
      actions: buttons,
      icon: icon || null,
    });
  };

  const showTourNotification = (
    setTourOpen,
    onHideClick,
    title?: string,
    description?: string
  ) => {
    api.open({
      key: "tour",
      message: t(title) || t("Welcome"),
      description: description || t("Feel free to begin this short tour!"),
      placement: "bottomRight",
      actions: (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => {
              api.destroy();
              onHideClick();
            }}
          >
            {t("Do not show again")}
          </Button>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setTourOpen(true);
              api.destroy("tour");
            }}
          >
            {t("Begin Tour")}
          </Button>
        </Space>
      ),
    });
  };

  const showSuccessNotification = (description: string, message?: string) => {
    api.open({
      message: message ? t(message) : t("Success!"),
      description: t(description),
      type: "success",
    });
  };

  const showErrorStyledNotification = (
    description: ReactNode,
    message?: string
  ) => {
    api.open({
      message: message ? t(message) : t("Error Occurred!"),
      description: description,
      type: "error",
    });
  };

  const showErrorNotification = (
    description: string,
    message = "",
    locale = true
  ) => {
    api.open({
      message: message ? (locale ? t(message) : message) : t("Error Occurred!"),
      description: locale ? t(description) : description,
      type: "error",
    });
  };

  const showWarningNotification = (description: string, message?: string) => {
    api.open({
      message: message ? t(message) : t("Error Occurred!"),
      description: t(description),
      type: "warning",
    });
  };

  return {
    showSuccessNotification,
    showErrorNotification,
    contextHolder,
    showInfoNotification,
    showWarningNotification,
    api,
    showTourNotification,
    clearAllNotification,
    showErrorStyledNotification,
  };
};

export { useNotification };
