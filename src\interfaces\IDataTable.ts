import { ReactNode } from "react";

export type IDataTableColumns = {
  key: string;
  label: string;
  render?: (value: any, data: any) => ReactNode;
  width?: string | number;
  isDate?: boolean;
  frozen?: boolean;
  isHidden?: boolean;
  index?: number;
  originalIndex?: number;
  editable?: boolean;
  isBoolean?: boolean;
};

export type IDataTableProps = {
  columns: IDataTableColumns[];
  loading?: boolean;
  customRowClassName?: any;
  dataKey?: string;
  detectChange?: any;
  expandCondition?: any;
  disableSelection?: any;
  onColumnsDelete?: any;
  noHeader?: boolean;
  rowReorder?: boolean;
  noSelect?: boolean;
  withDelete?: boolean;
  height?: any;
  data: any[];
  emptyMessage?: string;
  globalFilterFields: string[];
  alignLeft?: boolean;
  excelFileName: string;
  setAllColumnsRequest?: any;
  editable?: boolean;
  disableSave?: boolean;
  onRowsEdit?: any;
  extraAction?: any;
  selected?: any[];
  virtualScroll?: boolean;
  setSelected?: any;
  selectedActionsTemplate?: any;
  triggerChange?: any;
  singleSelection?: boolean;
  displaySaveCancel?: boolean;
  onCancelClick?: any;
  onSaveClick?: any;
  saveLoading?: boolean;
  filters: any;
  setFilters: any;
  sort: {
    field: any;
    order: any;
  };
  setSort: any;
  readOnlySelect?: boolean;
  defaultPanelExpand?: boolean;
  expandable?: boolean;
  expandedRows?: any;
  rowExpansionTemplate?: any;
  onRowExpand?: any;
  onRowCollapse?: any;
  noDownload?: boolean;
  extra?: ReactNode;
  hideSelectAll?: boolean;
  lazyLoad?: boolean;
  withMultiplicity?: boolean;
  multiplicity?: string;
  onDeleteClick?: any;
};
