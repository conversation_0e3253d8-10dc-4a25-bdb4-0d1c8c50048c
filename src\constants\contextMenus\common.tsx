import { Translation } from "react-i18next";

export const NODES_MENU_ITEMS = [
  {
    label: <Translation>{(t) => <p>{t("View Details")}</p>}</Translation>,
    key: "details",
  },
  {
    label: <Translation>{(t) => <p>{t("Open in new tab")}</p>}</Translation>,
    key: "open-in-new-tab",
  },
];

export const TRASH_NODES_MENU_ITEMS = [
  {
    label: <Translation>{(t) => <p>{t("View Details")}</p>}</Translation>,
    key: "details",
  },
  {
    label: <Translation>{(t) => <p>{t("View in trashcan")}</p>}</Translation>,
    key: "view-in-trashcan",
  },
];
export const DETAILS_OPTIONS = [
  {
    label: <Translation>{(t) => <p>{t("View Details")}</p>}</Translation>,
    key: "details",
  },
];
