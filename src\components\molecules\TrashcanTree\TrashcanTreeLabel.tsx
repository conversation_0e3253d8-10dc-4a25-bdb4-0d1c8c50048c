import { ReactNode, memo, useMemo, useState, useCallback } from "react";
import { useTheme } from "../../../utils";
import { styled } from "@linaria/react";
import { Dropdown } from "antd";
import {
  GET_HAS_PARENT,
  GET_NODE_ATTRIBUTES_DETAILS,
  getTrashcanMenus,
} from "../../../constants";
import { useTranslation } from "react-i18next";
import { IBreadcrumbs, INodeDetails } from "../../../interfaces";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { setSelectedTrash } from "../../../store/features/trashcan";
import { useLocation } from "react-router-dom";
import { useQuery, useQueryClient } from "react-query";
import { getParentExistence } from "../../../services/node";
import {
  useNotification,
  usePermissions,
} from "../../../utils/functions/customHooks";

const TrashcanTreeLabel = memo(
  ({
    label,
    id,
    setAction,
    parentId,
    isLeaf,
    setDropdownOpen,
    templateId,
    allowedChildrens,
    icon,
    isMeta,
  }: Props) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const dispatch = useDispatch();
    const location = useLocation();
    const queryClient = useQueryClient();

    const { contextHolder, showErrorNotification } = useNotification();
    const { getPermissions } = usePermissions();

    const { selectedTrash } = useSelector((state: RootState) => state.trash);
    const { selected } = useSelector((state: RootState) => state.sidebar);

    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const { data: parentExists } = useQuery(
      [GET_HAS_PARENT, selectedTrash.keys[0]],
      () => getParentExistence(selectedTrash.keys[0]),
      {
        enabled: isMenuOpen && !!selectedTrash.keys[0],
      }
    );

    const checkDuplicates = useCallback((objects) => {
      const nameMap = {};

      for (const obj of objects) {
        if (nameMap[obj.name]) {
          return true;
        }
        nameMap[obj.name] = true;
      }

      return false;
    }, []);

    const bodyData = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      id.toString(),
    ]) as INodeDetails;

    const PERMISSIONS = useMemo(() => {
      return getPermissions(bodyData?.permissionsId);
    }, [bodyData?.permissionsId]);

    const contextMenuItems = getTrashcanMenus(
      location?.pathname?.startsWith("/details") &&
        !isMeta &&
        selected.keys.length < 2,
      parentExists,
      selectedTrash.keys.length > 1,
      PERMISSIONS
    ) as any;

    const handleSidebarMenuChange = ({ key }, id, value) => {
      const selectedContextMenu = contextMenuItems.find(
        (item) => item?.key == key
      );

      setDropdownOpen(true);

      if (key === "move-to-selected") {
        const hasSameName = checkDuplicates(selectedTrash?.info);
        if (hasSameName) {
          showErrorNotification(
            "Objects with same name cannot exist in same level"
          );
          return;
        }
      }
      setAction({
        id: id,
        label: value,
        key: key,
        parentId: parentId,
        title: key.startsWith("add") ? (
          <>
            {selectedContextMenu?.label} {t("to")} {value}
          </>
        ) : (
          selectedContextMenu?.label
        ),
        templateId: selectedContextMenu?.templateid || templateId,
        allowedChildrens: allowedChildrens,
        isLeaf: isLeaf,
      });
    };

    const handleOpenChange = useCallback(
      (open) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        setDropdownOpen && setDropdownOpen(open);
        setIsMenuOpen(open);

        if (open) {
          if (!selectedTrash.keys.includes(id)) {
            const selectedNodeInfo = {
              id: id,
              parentId: parentId,
              name: label,
              isLeaf: isLeaf,
              templateId: templateId,
            };
            dispatch(
              setSelectedTrash({
                keys: [Number(id)],
                info: [selectedNodeInfo],
              })
            );
          }
        }
      },
      [setDropdownOpen, setIsMenuOpen, id, dispatch]
    );

    return (
      <Item theme={theme} key={id.toString()}>
        {contextHolder}
        <Dropdown
          onOpenChange={handleOpenChange}
          menu={{
            items: contextMenuItems,
            onClick: (e) => handleSidebarMenuChange(e, id, label),
          }}
          trigger={["contextMenu"]}
        >
          <div className="sidebar-title">
            {icon}
            <p>{label}</p>
          </div>
        </Dropdown>
      </Item>
    );
  }
);

export { TrashcanTreeLabel };

interface Props {
  // label
  label: string;
  id: number;
  setAction: any;
  parentId: number;
  isMultiple?: boolean;
  setDropdownOpen: any;
  templateId?: number;
  icon: ReactNode;
  allowedChildrens: any[];
  breadcrumbs: IBreadcrumbs[];
  isLeaf?: boolean;
  childrens?: number;
  expandNode: any;
  isMeta: boolean;
}

const Item = styled.div<{ theme: any }>`
  position: relative;
  display: flex;
  font-size: 13px;
  width: 100%;

  & .sidebar-title {
    display: flex;
    gap: 5px;
    padding-left: 3px;
    align-items: center;
    /* text-decoration: line-through; */
    color: ${({ theme }) => theme.trashBreadcrumbsColor} !important;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }

  & .anticon-pushpin {
    right: 22px;
    font-size: 14px;
    top: 3px;
  }
  & span {
    color: ${({ theme }) => theme.colorPrimary};
    position: absolute;
    opacity: 0;
    top: 2px;
    font-size: 18px;
    transition: all 0.3s ease-in;
    right: 0px;
    &:hover {
      opacity: 0.8;
    }
  }
  &:hover span {
    opacity: 1;
    transition: all 0.2s ease-in;
  }

  & .ant-dropdown-trigger {
    width: 100%;
  }
`;
