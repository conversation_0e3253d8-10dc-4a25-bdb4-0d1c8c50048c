import { Tabs } from "antd";
import React, { ReactNode, cloneElement } from "react";
import type { DragEndEvent } from "@dnd-kit/core";
import { DndContext, PointerSensor, useSensor } from "@dnd-kit/core";
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useDispatch, useSelector } from "react-redux";
import { setNavigationItems } from "../../../store/features/navigation";
import { useMutation, useQueryClient } from "react-query";
import { saveLocalSettings } from "../../../services";
import { GET_LOCAL_SETTINGS_KEY } from "../../../constants";
import { ILocalSettings } from "../../../interfaces";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../../components/withErrorBoundary";
import { createSafeNavigationItemsOrder } from "../../../utils/navigationItemsUtils";

interface Props {
  items: any;
  onTabClick: (value: any) => void;
  tabBarExtraContent?: ReactNode;
  setItems: any;
  localActiveKey?: string;
  fromModal?: boolean;
}

interface DraggableTabPaneProps extends React.HTMLAttributes<HTMLDivElement> {
  "data-node-key": string;
}

const DraggableTabNode = ({ ...props }: DraggableTabPaneProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: props["data-node-key"],
    });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleX: 1 }),
    transition,
    cursor: "move",
  };

  return cloneElement(props.children as React.ReactElement, {
    ref: setNodeRef,
    style,
    ...attributes,
    ...listeners,
  });
};

const DraggableTabsBase = ({
  items,
  onTabClick,
  tabBarExtraContent,
  setItems,
  localActiveKey,
  fromModal,
}: Props) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const activeKey = useSelector(
    (root: RootState) => root.navigation.selectedBottomNavbar
  );

  const sensor = useSensor(PointerSensor, {
    activationConstraint: { distance: 10 },
  });

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  // Get the complete navigationItems array from Redux store
  const { navigationItems } = useSelector((state: RootState) => state.navigation);

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = items.findIndex((i) => i?.key === active.id);
      const overIndex = items.findIndex((i) => i?.key === over?.id);
      const newItems = arrayMove(items, activeIndex, overIndex);
      setItems([...newItems]);

      // Get the current complete navigationItems from Redux store
      const currentNavigationItems = navigationItems || [];

      // Extract only the visible item keys in their new order
      const reorderedVisibleKeys = [];
      newItems.forEach((item: any) => reorderedVisibleKeys.push(item?.key));

      // Create a safe merge that preserves ALL items while respecting the new order
      const safelyMergedNavigationItems = createSafeNavigationItemsOrder(
        currentNavigationItems,
        reorderedVisibleKeys
      );

      dispatch(setNavigationItems(safelyMergedNavigationItems));

      mutation.mutate({
        value: {
          ...(localSettingsData?.body
            ? localSettingsData?.body[0]?.value || {}
            : {}),
          navigationItems: safelyMergedNavigationItems,
        },
      });
    }
  };

  const mutation = useMutation(saveLocalSettings);

  const bottomDrawerMask = useSelector(
    (state: RootState) => state.mask.bottomDrawer
  );
  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );
  const movingMask = useSelector((state: RootState) => state.mask.movingMask);

  return (
    <Tabs
      items={items}
      activeKey={fromModal ? localActiveKey : activeKey}
      onTabClick={onTabClick}
      tabBarExtraContent={tabBarExtraContent}
      renderTabBar={(tabBarProps, DefaultTabBar) => (
        <DndContext sensors={[sensor]} onDragEnd={onDragEnd}>
          {(bottomDrawerMask || workingVersionActive || movingMask) && (
            <div className="mask mask-tabs" />
          )}
          <SortableContext
            items={items.map((i) => i?.key)}
            strategy={horizontalListSortingStrategy}
          >
            <DefaultTabBar {...tabBarProps}>
              {(node) => (
                <DraggableTabNode {...node.props} key={node.key}>
                  {node}
                </DraggableTabNode>
              )}
            </DefaultTabBar>
          </SortableContext>
        </DndContext>
      )}
    />
  );
};

const DraggableTabs = withErrorBoundary(
  React.memo(DraggableTabsBase),
  "error.generic"
);

export { DraggableTabs };
