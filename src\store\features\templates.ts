import { createSlice } from "@reduxjs/toolkit";
import { ITemplates } from "../../interfaces";

export interface AuthState {
  templates: {
    number: ITemplates;
  };
}

const initialState: AuthState = {
  templates: null,
};

export const templateSlice = createSlice({
  name: "template",
  initialState,
  reducers: {
    setTemplates: (state, action) => {
      state.templates = action.payload;
    },
  },
});

export const { setTemplates } = templateSlice.actions;

export default templateSlice.reducer;
