import React, { useEffect, useRef } from "react";
import { usePermissions } from "../../../utils/functions/customHooks";
import { DetailCellRenderer } from "./DetailCellRenderer";
import { withErrorBoundary } from "../../withErrorBoundary";

const CheckDetailsPermissionsBase = (props) => {
  const { data, node, context, api } = props;
  const { getPermissions } = usePermissions();
  const permissions = getPermissions(data?.permissionsId || 0);
  const hasTriggeredPopup = useRef(false);

  useEffect(() => {
    if (!permissions.includes("VIEW") && !hasTriggeredPopup.current) {
      hasTriggeredPopup.current = true;
      api?.setRowNodeExpanded(node, false, true);
      context.setNoPermissionPopup(node);
    }
  }, [permissions, node, context, api]);

  if (permissions.includes("VIEW")) {
    return <DetailCellRenderer data={data} />;
  }

  return <></>;
};

export const CheckDetailsPermissions = withErrorBoundary(
  React.memo(CheckDetailsPermissionsBase),
  "error.generic"
);
