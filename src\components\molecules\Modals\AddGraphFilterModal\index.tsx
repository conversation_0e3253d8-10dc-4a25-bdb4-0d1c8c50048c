import { styled } from "@linaria/react";
import { Button, Radio, notification } from "antd";
import { useTranslation } from "react-i18next";
import { Input } from "../../../atoms";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useMutation, useQueryClient } from "react-query";
import { saveLocalSettings, saveGlobalSettings } from "../../../../services";
import React, { useEffect, useState } from "react";
import {
  GET_GLOBAL_SETTINGS_KEY,
  GET_LOCAL_SETTINGS_KEY,
} from "../../../../constants";
import { Dialog } from "primereact/dialog";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

type FormData = {
  name: string;
};

const AddGraphFilterModalBase = ({
  isOpen,
  onClose,
  selectedTemplateKeys,
  displayOptions,
  filterType,
  afterSave,
  saveAs,
}) => {
  const { t } = useTranslation();
  const [filter, setFilter] = useState(null);

  useEffect(() => {
    setFilter(filterType);
  }, [filterType]);

  const validationSchema = yup.object({
    name: yup.string().required("Please enter"),
  });
  const queryClient = useQueryClient();
  const globalSettingsData = queryClient.getQueryData(
    GET_GLOBAL_SETTINGS_KEY
  ) as any;

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as any;

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = (values) => {
    const ID = Math.floor(1 + Math.random() * 999);
    let value = null;
    if (filter === "myFilters") {
      value = {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        myFilters: [
          ...(localSettingsData?.body[0]?.value?.myFilters || []),
          {
            id: ID,
            name: values.name,
            selectedTemplateKeys: selectedTemplateKeys,
            displayOptions: displayOptions,
          },
        ],
      };
    } else {
      value = {
        ...(globalSettingsData?.body
          ? globalSettingsData?.body[0]?.value || {}
          : {}),
        globalFilters: [
          ...(globalSettingsData?.body[0]?.value?.globalFilters || []),
          {
            id: ID,
            name: values.name,
            selectedTemplateKeys: selectedTemplateKeys,
            displayOptions: displayOptions,
          },
        ],
      };
    }

    mutation.mutateAsync({
      value: value,
    });
    afterSave(ID, filter);
  };

  const mutation = useMutation(
    filter === "myFilters" ? saveLocalSettings : saveGlobalSettings,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(
          filter === "myFilters"
            ? GET_LOCAL_SETTINGS_KEY
            : GET_GLOBAL_SETTINGS_KEY
        );
        notification.success({
          message: t("Success!"),
          description: t("Filter saved successfully!"),
        });
        reset();
        onClose();
      },
    }
  );

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      // cancelText={"Close"}
      footer={null}
      className="draggable-modal"
      header={saveAs ? t("Save As") : t("Add new filter")}
    >
      <Container onSubmit={handleSubmit(onSubmit)}>
        <div className="row">
          <h6>{t("Name")}</h6>
          <div>
            <Input error={errors.name?.message} name="name" control={control} />
            {filterType === "myFilters" && (
              <Radio.Group
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              >
                <Radio value="myFilters">My Filters</Radio>
                <Radio value="global">Global</Radio>
              </Radio.Group>
            )}
          </div>
        </div>

        <Button htmlType="submit" type="primary" loading={mutation.isLoading}>
          {t("Add")}
        </Button>
      </Container>
    </Dialog>
  );
};

const AddGraphFilterModal = withErrorBoundary(
  React.memo(AddGraphFilterModalBase),
  "error.generic"
);

export { AddGraphFilterModal };

const Container = styled.form`
  padding-bottom: 20px;
  & button {
    display: flex;
    margin-left: auto;
    font-size: 13px;
    box-shadow: none;
    margin-top: 10px;
    margin-right: 10px;
  }
  & .row {
    display: flex;
    border: 0.5px solid #eaeaea;
    border-radius: 4px;

    & h6 {
      font-size: 13px;
      font-weight: 400;
      background-color: #e3ecf861;
      border-right: 1px solid #eaeaea;
      flex: 1;
      color: #436196;
      padding: 6px 10px;
      display: flex;
    }

    & > div {
      padding: 8px 10px;
      flex: 2;
      margin-bottom: 0px;
      & > div {
        margin-bottom: 10px;
      }
      & .ant-radio-wrapper {
        color: #426196;
        font-size: 13px;
      }
    }
  }
`;
