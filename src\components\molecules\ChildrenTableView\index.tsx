import React, { ReactNode, memo, useEffect, useState } from "react";
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  getAllNodesWithBody,
  getHierarchyDetails,
} from "../../../services/node";
import { useQuery } from "react-query";
import {
  AUTHOR_USERS_TEMPLATE_ID,
  COMMENT_TEMPLATE_ID,
  GET_CHILDRENS,
  NODES_MENU_ITEMS,
  PERMISSION_PERSON_ID,
  TRASH_NODES_MENU_ITEMS,
  USER_GROUP_ID,
  getAttributeIcon,
} from "../../../constants";
import { IHyperlinks, ITreeData } from "../../../interfaces";
import { styled } from "@linaria/react";
import { DetailsContainer, MyTable } from "../../organisms";
import { Dropdown, Flex, Tabs } from "antd";
import { getParentID, useTheme } from "../../../utils";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { MyTooltip } from "../../atoms";
import {
  FullscreenExitOutlined,
  FullscreenOutlined,
  MinusOutlined,
} from "@ant-design/icons";
import { Tooltip } from "primereact/tooltip";
import { useTranslation } from "react-i18next";
import { ResizableDiv } from "../ResizableDiv";
import { css } from "@linaria/core";
import {
  setExpandedKeys,
  setMoveToFocused,
  setRefreshBreadcrumbs,
} from "../../../store/features";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { getParentIds } from "../../../utils/functions/getParentIds";

const STATE_KEY = "children-table-height";

// children table view for nodes that has childrens

const ChildrenTableView = memo(({ onClose }: Props) => {
  const location = useLocation();
  const theme = useTheme();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams();

  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [fullScreenClicked, setFullScreenClicked] = useState(false);
  const [hideIcon, setHideIcon] = useState(false);
  const [columns, setColumns] = useState([]);
  const [rows, setRows] = useState([]);
  const [tabsData, setTabsData] = useState([] as ITabs[]);
  const [activeTemplateId, setActiveTemplateId] = useState(null);
  const [height, setHeight] = useState(300);
  const [previousHeight, setPreviousHeight] = useState(300);
  const [loading, setLoading] = useState(false);

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const nodeId = searchParams.get("nodeId");
  const metamodel = location.pathname.includes("metamodel");
  const dataSource = location.pathname.includes("data-sources");
  const roles = location.pathname.includes("roles");
  const isUsers = location.pathname.includes("users");
  const isUserGroups = location.pathname.includes("user-groups");
  const isActions = location.pathname.includes("actions");
  const isAuthors = location.pathname.includes("authors");

  const { devMode, hiddenAttributes } = useSelector(
    (root: RootState) => root.globalSettings
  );

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const expandedKeys = useSelector(
    (root: RootState) => root.sidebar.expandedKeys
  );

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const { data, isFetching, isLoading, isError } = useQuery<ITreeData[], Error>(
    [GET_CHILDRENS, nodeId],
    () => getAllNodesWithBody(nodeId),
    {
      enabled: !!nodeId && !!templatesData,
    }
  );

  useEffect(() => {
    setActiveTemplateId(null);
    setColumns([]);
    setRows([]);
  }, [nodeId]);

  const generateTabsData = () => {
    const tabData = [];
    data.forEach((entry) => {
      // Filter out comment templates from tabs
      if (entry.templateId === COMMENT_TEMPLATE_ID) {
        return;
      }

      const isTabNotPresent =
        tabData.findIndex((temp) => temp.key === entry.templateId) === -1;

      if (isTabNotPresent) {
        const selectedTemplate = templatesData[entry.templateId];
        if (selectedTemplate) {
          tabData.push({
            label: (
              <div className={"tabs-title"}>
                {getAttributeIcon(selectedTemplate?.icon)}
                {selectedTemplate?.name}
              </div>
            ),
            key: entry.templateId,
          });
        }
      }
    });
    if (tabData.length > 0) {
      setActiveTemplateId(tabData[0].key);
    }

    setTabsData([...tabData]);
  };

  useEffect(() => {
    if (data) {
      setLoading(true);
      generateTabsData();
      generateRowsAndColumns();
    }
  }, [data, nodeId]);

  useEffect(() => {
    if (data) {
      generateRowsAndColumns();
    }
  }, [activeTemplateId]);

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        const parentID = await getParentID(id);

        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }
      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const getPath = (parentId, nodeId) => {
    if (metamodel) {
      return `/settings/metamodel/-1?nodeId=${nodeId}`;
    }
    if (dataSource) {
      return `/settings/data-sources/-2?nodeId=${nodeId}`;
    }
    if (isUsers) {
      return `/settings/users/${PERMISSION_PERSON_ID}?nodeId=${nodeId}`;
    }
    if (isUserGroups) {
      return `/settings/user-groups/${USER_GROUP_ID}?nodeId=${nodeId}`;
    }
    if (roles) {
      return `/settings/roles/-5?nodeId=${nodeId}`;
    }
    if (isActions) {
      return `/settings/actions/-6?nodeId=${nodeId}`;
    }
    if (isAuthors) {
      return `/authors/${AUTHOR_USERS_TEMPLATE_ID}?nodeId=${nodeId}`;
    }
    return `/details/${parentId}?nodeId=${nodeId}`;
  };

  const moveToNode = async (selectedNodeId) => {
    const parentPath = await getHierarchyDetails(selectedNodeId);
    const parentIds = getParentIds(parentPath.path);

    dispatch(setExpandedKeys([...expandedKeys, ...parentIds]));

    navigate(getPath(params?.nodeId, selectedNodeId));
    setTimeout(() => {
      dispatch(setRefreshBreadcrumbs(true));
      dispatch(setMoveToFocused(true));
    }, 400);
  };

  // dynamic generation of rows and columns
  const generateRowsAndColumns = () => {
    const columnsData = [];
    const rowsData = [];
    setColumns([]);
    setRows([]);

    data?.forEach((entry) => {
      // Skip comment templates in column generation as well
      if (
        entry.templateId === activeTemplateId &&
        entry.templateId !== COMMENT_TEMPLATE_ID
      ) {
        const selectedTemplate = templatesData[entry.templateId];
        if (selectedTemplate) {
          const isColumnNotPresent =
            columnsData.findIndex(
              (column) => column.field === selectedTemplate?.id?.toString()
            ) === -1;

          if (isColumnNotPresent) {
            columnsData.push({
              field: selectedTemplate?.id?.toString(),
              headerName: selectedTemplate?.name,
              minWidth: 200,
              flex: 1,
              cellRenderer: ({ data }) => {
                const nodeDetails = JSON.parse(data[selectedTemplate?.id]);
                const selectedNodeId = Object.keys(nodeDetails)[0];
                const nodeName = Object.values(nodeDetails)[0] as string;

                return (
                  <Dropdown
                    menu={{
                      items: NODES_MENU_ITEMS,
                      onClick: (e) =>
                        handleNodeClick(e.key, selectedNodeId, nodeName),
                    }}
                    trigger={["contextMenu"]}
                  >
                    <a
                      className="title-container"
                      onClick={async (e) => {
                        e.stopPropagation();
                        onClose();
                        moveToNode(selectedNodeId);
                      }}
                    >
                      {nodeName}
                    </a>
                  </Dropdown>
                );
              },
            });
          }

          const body = [];
          selectedTemplate?.attributeTemplates?.forEach((attribute) => {
            const selected = entry?.body?.find((b) => b.id === attribute.id);
            if (selected) body.push({ ...selected });
          });

          body?.forEach((attribute) => {
            const isColumnNotPresent =
              columnsData.findIndex(
                (column) => column.field === attribute.id?.toString()
              ) === -1;
            if (isColumnNotPresent) {
              columnsData.push({
                field: attribute.id?.toString(),
                type: attribute.type,
                headerName: attribute.name,
                cellRenderer: ({ data }) => {
                  if (attribute.type === "relation") {
                    if (data[attribute.id] === "-") {
                      return "-";
                    }
                    const hyperlinksValue = data[attribute.id];

                    return (
                      <Flex wrap="wrap">
                        {hyperlinksValue?.map(
                          (hyperlink: IHyperlinks, index) => (
                            <React.Fragment key={hyperlink.id}>
                              <Dropdown
                                menu={{
                                  items: hyperlink.inTrash
                                    ? TRASH_NODES_MENU_ITEMS
                                    : NODES_MENU_ITEMS,
                                  onClick: (e) =>
                                    handleNodeClick(
                                      e.key,
                                      hyperlink.id,
                                      hyperlink.name
                                    ),
                                }}
                                trigger={["contextMenu"]}
                              >
                                <a
                                  className={`title-container ${
                                    hyperlink.inTrash ? "trash-hyperlink" : ""
                                  }`}
                                  onClick={async (e) => {
                                    e.stopPropagation();
                                    onClose();
                                    handleHyperlinkAction(hyperlink);
                                  }}
                                >
                                  {hyperlink.name}
                                </a>
                              </Dropdown>
                              {index !== hyperlinksValue?.length - 1 && (
                                <p>, </p>
                              )}
                            </React.Fragment>
                          )
                        )}
                      </Flex>
                    );
                  } else if (attribute.type.startsWith("allowedChildren")) {
                    const allowedChildrensValue =
                      data[attribute.id] === "-"
                        ? []
                        : JSON.parse(data[attribute.id] || []);

                    return (
                      <Flex wrap="wrap">
                        {allowedChildrensValue?.map(
                          (templateId: number, index) => {
                            const templateName =
                              templatesData[templateId]?.name || "-";
                            return (
                              <React.Fragment key={templateId}>
                                <Dropdown
                                  menu={{
                                    items: NODES_MENU_ITEMS,
                                    onClick: (e) =>
                                      handleNodeClick(
                                        e.key,
                                        templateId,
                                        templateName
                                      ),
                                  }}
                                  trigger={["contextMenu"]}
                                >
                                  <a
                                    className={`title-container `}
                                    onClick={async (e) => {
                                      e.stopPropagation();
                                      onClose();
                                      moveToNode(templateId);
                                    }}
                                  >
                                    {templateName}
                                  </a>
                                </Dropdown>
                                {index !==
                                  allowedChildrensValue?.length - 1 && (
                                  <p>, </p>
                                )}
                              </React.Fragment>
                            );
                          }
                        )}
                      </Flex>
                    );
                  } else if (attribute.type === "icon") {
                    return data[attribute.id]
                      ? getAttributeIcon(data[attribute.id])
                      : "-";
                  } else if (attribute.type === "compound") {
                    // Handle compound type in cell renderer to show parent → child relationships
                    const compoundValue = data[attribute.id];
                    if (compoundValue === "-" || !compoundValue) {
                      return "-";
                    }
                    try {
                      const parsedValue =
                        typeof compoundValue === "string"
                          ? JSON.parse(compoundValue)
                          : compoundValue;
                      if (
                        Array.isArray(parsedValue) &&
                        parsedValue.length > 0
                      ) {
                        return parsedValue
                          .map((item) => {
                            if (
                              item.value &&
                              Array.isArray(item.value) &&
                              item.value.length > 0
                            ) {
                              // Show parent → child relationship
                              const childNames = item.value
                                .map((child) => child.name)
                                .join(", ");
                              return `${item.name} > ${childNames}`;
                            }
                            // If no children, just show parent name
                            return item.name;
                          })
                          .join("; ");
                      }
                      return "-";
                    } catch {
                      return compoundValue.toString();
                    }
                  }
                  return data[attribute.id] || "-";
                },
              });
            }
          });
        }
      }
    });

    data?.forEach((entry, index) => {
      // Skip comment templates in row generation as well
      if (
        entry.templateId === activeTemplateId &&
        entry.templateId !== COMMENT_TEMPLATE_ID
      ) {
        const selectedTemplate = templatesData[entry.templateId];

        if (selectedTemplate) {
          const rowData = {};
          rowData["id"] = entry.templateId + "-" + index;
          columnsData.forEach((column) => {
            rowData[column.key] = "-";
          });
          const nodeData = {};
          nodeData[entry.id] = entry.name;

          rowData[selectedTemplate.id] = JSON.stringify(nodeData);

          const body = devMode
            ? entry.body
            : entry.body?.filter(
                (attr) => !hiddenAttributes?.includes(attr.name)
              );

          body?.forEach((attribute) => {
            rowData[attribute.id.toString()] =
              attribute.type === "allowedChildren" ? (
                JSON.stringify(attribute?.value || [])
              ) : attribute.type === "relation" ? (
                attribute.value || "-"
              ) : attribute.type === "compound" ? (
                // Handle compound type properly to show parent → child relationships
                attribute.value &&
                Array.isArray(attribute.value) &&
                attribute.value.length > 0 ? (
                  attribute.value
                    .map((item) => {
                      if (
                        item.value &&
                        Array.isArray(item.value) &&
                        item.value.length > 0
                      ) {
                        // Show parent → child relationship
                        const childNames = item.value
                          .map((child) => child.name)
                          .join(", ");
                        return `${item.name} > ${childNames}`;
                      }
                      // If no children, just show parent name
                      return item.name;
                    })
                    .join("; ")
                ) : (
                  "-"
                )
              ) : typeof attribute.value === "object" ? (
                Object.values(attribute.value).length > 0 &&
                Object.values(attribute.value)[0] !== "" ? (
                  Object.values(attribute.value).join()
                ) : (
                  "-"
                )
              ) : attribute.type === "editor" ? (
                <span
                  dangerouslySetInnerHTML={{ __html: attribute.value || "-" }}
                />
              ) : (
                attribute.value || "-"
              );
          });

          rowsData.push(rowData);
        }
      }
    });
    setTimeout(() => {
      setColumns([...columnsData]);
      setRows([...rowsData]);
      setLoading(false);
    }, 200);
  };

  const handleResize = (_event, _direction, refToElement) => {
    const parentHeight = refToElement.parentElement.clientHeight;
    const resizableHeight = refToElement.clientHeight;
    setHideIcon(resizableHeight >= parentHeight);
    setHeight(refToElement.clientHeight);
  };

  const handleExpandClick = () => {
    setPreviousHeight(height);
    const parentHeight = document.getElementById(
      "details-content-container"
    ).clientHeight;
    // Reduce height by 80px when entering fullscreen mode to avoid extra bottom scroll in parent
    setHeight(parentHeight - 80);
    setFullScreenClicked(!fullScreenClicked);
  };

  useEffect(() => {
    const height = localStorage.getItem(STATE_KEY);
    if (height) setHeight(Number(height));
  }, []);

  return (
    <ResizableDiv
      height={height}
      defaultHeight="300px"
      resize="top"
      onResize={handleResize}
      className={`${resizeStyle} ${fullScreenClicked ? "fullscreen-mode" : ""}`}
      saveHeightToLocalStorage
      stateKey={STATE_KEY}
      minHeight={30}
    >
      <Wrapper theme={theme}>
        {mask && <Mask className="mask" />}
        <div className={"menu-wrapper"}>
          <Tabs
            items={tabsData}
            activeKey={activeTemplateId}
            onChange={(key) => {
              setActiveTemplateId(key);
            }}
          />
          <div className="right">
            {!hideIcon &&
              (fullScreenClicked ? (
                <>
                  <Tooltip target=".full-screen-exit" />

                  <FullscreenExitOutlined
                    className="full-screen-exit"
                    data-pr-position="left"
                    data-pr-tooltip={t("Exit full screen")}
                    data-pr-showdelay={500}
                    onClick={() => {
                      setHeight(previousHeight);
                      setFullScreenClicked(!fullScreenClicked);
                    }}
                  />
                </>
              ) : (
                <MyTooltip title="Full screen">
                  <FullscreenOutlined
                    onClick={() => {
                      handleExpandClick();
                    }}
                  />
                </MyTooltip>
              ))}
            <MyTooltip title="Collapse">
              <MinusOutlined onClick={onClose} />
            </MyTooltip>
          </div>
        </div>

        <MyTable
          isError={isError}
          excelFileName="datatable"
          columns={columns}
          data={rows}
          fullHeight
          loading={isFetching || loading || isLoading}
        />
      </Wrapper>

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </ResizableDiv>
  );
});

export { ChildrenTableView };

type ITabs = {
  key: string;
  label: ReactNode;
};

interface Props {
  onClose: () => void;
}

const resizeStyle = css`
  & .indicator {
    top: -17px;
  }
`;
const Mask = styled.div`
  height: 100%;
  width: 100%;
  z-index: 100;
  top: 0px;
  left: 0px;
`;

const Wrapper = styled.div<{ theme: any }>`
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100% - 90px);
  border-top: 3px solid ${({ theme }) => theme.colorSecondary};

  & .my-table {
    padding: 10px 20px;
  }

  .fullscreen-mode & {
    height: calc(100% - 90px);
  }

  & img {
    object-fit: contain;
  }

  & .data-table-wrapper {
    overflow: auto;
  }

  & .ant-tabs-nav {
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 0px;

    &::before {
      border-bottom: none;
    }
  }

  & .tabs-title {
    display: flex;
    align-items: center;
    color: ${({ theme }) => theme.colorPrimary};
    gap: 5px;
    font-size: 13px;

    & img {
      object-fit: contain;
      height: 18px;
      width: 18px;
    }
  }

  & .title-container {
    cursor: pointer;
    font-size: 12px;

    &:hover {
      text-decoration: underline;
      color: ${({ theme }) => theme.colorPrimary};
    }
  }
  & .menu-wrapper {
    display: flex;
    border-bottom: 1px solid #eee;

    & .right {
      display: flex;
      gap: 10px;
      align-items: center;
      padding-right: 20px;
      color: ${({ theme }) => theme.colorPrimary};
      font-size: 17px;
    }
  }

  & .ant-menu-title-content {
    color: ${({ theme }) => theme.colorPrimary};
  }
  & > .resizable {
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  & .data-table-wrapper {
    flex: 1;
    padding: 10px;
  }

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .ant-tabs {
    flex: 1;
  }
`;
