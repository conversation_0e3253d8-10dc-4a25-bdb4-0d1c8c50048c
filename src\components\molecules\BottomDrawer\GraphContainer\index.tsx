import { memo } from "react";
import { styled } from "@linaria/react";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

import { GraphComponent } from "../../../organisms";
import { useSearchParams } from "react-router-dom";

interface Props {
  id?: string;
  fromTrashcan: boolean;
}

// Graph container component to render graph component in bottom drawer
const GraphContainerBase = ({ id, fromTrashcan }: Props) => {
  const [searchParams] = useSearchParams();

  return (
    <Content>
      <GraphComponent
        fromTrashcan={fromTrashcan}
        id={id || searchParams.get("nodeId")}
      />
    </Content>
  );
};

const GraphContainer = withErrorBoundary(
  memo(GraphContainerBase),
  "error.generic"
);

export { GraphContainer };

const Content = styled.div`
  height: 100%;
  display: flex;
`;
