import type { <PERSON>a, StoryObj } from "@storybook/react";
import { HomeFilter } from ".";

const meta: Meta<typeof HomeFilter> = {
  title: "Components/Atoms/HomeFilter",
  component: HomeFilter,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof HomeFilter>;

export const Basic: Story = {
  args: {
    checkedList: {
      filter1: [],
      filter2: [],
      filter3: [],
    },
  },
};
