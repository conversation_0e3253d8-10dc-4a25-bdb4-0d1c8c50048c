import { styled } from "@linaria/react";
import { Tooltip } from "antd";
import { useTheme } from "../../../utils/useTheme";
import { useTranslation } from "react-i18next";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";

// Used in DQM for displaying test card

const TestCard = (props) => {
  const { name, testResults, id } = props;
  const theme = useTheme();
  const { t } = useTranslation();
  const { handleDQMTestClick, handleHyperlinkAction } = useHyperlinkActions();

  return (
    <Box
      className={testResults?.length === 0 ? "new-test" : testResults[0]?.type}
      theme={theme}
    >
      <div
        style={{ cursor: "pointer" }}
        onClick={() => {
          handleHyperlinkAction({
            id: id,
            inTrash: false,
          });
        }}
      >
        <h6 className={`h6-${testResults[0]?.type}`}>{t(name)}</h6>
      </div>
      <Grid>
        {testResults?.map((data, index) => (
          <Tooltip
            placement="bottom"
            key={index}
            title={`${t("Date of test")}: ${data?.date}`}
          >
            <div
              onClick={() => {
                handleDQMTestClick(id, data?.id);
              }}
            >
              {data?.type === "success" ? (
                <Success />
              ) : data?.type === "warning" ? (
                <Warning />
              ) : (
                <Error />
              )}
            </div>
          </Tooltip>
        ))}
      </Grid>
    </Box>
  );
};

export { TestCard };

const InfoDiv = styled.div`
  border-radius: 3px;
  min-height: 20px;
  min-width: 20px;
  max-width: 20px;
  border: 1.5px solid #fff;
  cursor: pointer;
  transition: all 0.5s;

  &:hover {
    transition: all 0.5s;
    box-shadow: 1px 2px 8px rgb(255 255 255 / 74%);
  }
`;

const Error = styled(InfoDiv)`
  background-color: #b61c07f2;
  border-color: #ffd3d3;
`;
const Success = styled(InfoDiv)`
  background-color: #31a354;
  border-color: #f4fff4;
`;

const Warning = styled(InfoDiv)`
  background-color: #ff9f40;
  border-color: #f4fff4;
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  flex-wrap: wrap;
  flex: 3;
  max-width: 300px;
  gap: 12px;
  width: fit-content;
  margin-top: 30px;
  justify-content: center;
  padding: 0px 20px;
  margin-left: auto;
  margin-right: auto;
`;

const Box = styled.div<{ theme: any }>`
  border-radius: 4px;
  padding-bottom: 30px;

  & a {
    text-decoration: none;
  }

  & h6 {
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    color: ${({ theme }) => theme.colorPrimary};
    padding: 6px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: #fff;
  }

  & p {
    color: #fff;
    margin-top: 24px;
    text-align: center;
  }
`;
