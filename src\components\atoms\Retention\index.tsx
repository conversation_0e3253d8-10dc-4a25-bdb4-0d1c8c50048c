import { styled } from "@linaria/react";
import { DatePicker, Select } from "antd";
import { RETENTION_OPTIONS, TIMEINTERVAL_OPTIONS } from "../../../constants";
import dayjs from "dayjs";
import { useEffect, useRef } from "react";
import { NumberComponent } from "../NumberComponent";

// Used in Retention attribute
// Might not be used now

const Retention = ({ value, setValue, onEdit, timeinterval }) => {
  const ref = useRef(null);

  useEffect(() => {
    ref?.current.focus();
  }, []);

  const selectAfter = (
    <Select
      defaultValue="DAY"
      value={Object.keys(value || {})[0] || "DAY"}
      onChange={(val) => {
        if (val === "custom") {
          setValue({ ...value, value: val, duration: null });
        } else {
          setValue({ [val]: Object.values(value || {})[0] });
          onEdit({ [val]: Object.values(value || {})[0] });
        }
      }}
      options={timeinterval ? TIMEINTERVAL_OPTIONS : RETENTION_OPTIONS}
      placeholder="Please Select"
    />
  );

  return (
    <Container>
      <NumberComponent
        addonAfter={selectAfter}
        min={1}
        ref={ref}
        value={Object.values(value || {})[0] as any}
        onChange={(val) => {
          setValue({ [value ? Object.keys(value || {})[0] : "DAY"]: val });
          onEdit({ [value ? Object.keys(value || {})[0] : "DAY"]: val });
        }}
      />

      {value?.value === "custom" && (
        <div className="custom">
          <label>Select Custom Date and Time</label>
          <DatePicker
            format="YYYY-MM-DD HH:mm"
            showTime
            value={value?.date ? dayjs(value?.date, "YYYY-MM-DD HH:mm") : null}
            onChange={(_, val) => {
              setValue({ ...value, date: val });
              onEdit({ ...value, date: val });
            }}
          />
        </div>
      )}
    </Container>
  );
};

export { Retention };

const Container = styled.div`
  & .custom {
    margin-top: 20px;
  }

  & label {
    margin-bottom: 7px;
    margin-top: 10px;
    display: block;
  }

  & .ant-input-number-group-wrapper {
    width: 100%;

    & .ant-select {
      width: max-content;
    }
  }
`;
