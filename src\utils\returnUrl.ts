const RETURN_URL_PARAM = 'returnUrl';

/**
 * Creates a login URL with return URL parameter
 * @param returnUrl - The URL to return to after login
 * @returns Login URL with encoded return URL parameter
 */
export const createLoginUrlWithReturn = (returnUrl: string): string => {
  if (!isValidReturnUrl(returnUrl)) {
    return '/login';
  }

  const encodedReturnUrl = encodeURIComponent(returnUrl);
  return `/login?${RETURN_URL_PARAM}=${encodedReturnUrl}`;
};

/**
 * Gets the return URL from current URL search parameters
 * @param searchParams - URLSearchParams from current location
 * @returns The decoded return URL or null if none exists or invalid
 */
export const getReturnUrlFromParams = (searchParams: URLSearchParams): string | null => {
  const returnUrl = searchParams.get(RETURN_URL_PARAM);
  if (!returnUrl) {
    return null;
  }

  try {
    const decodedUrl = decodeURIComponent(returnUrl);
    return isValidReturnUrl(decodedUrl) ? decodedUrl : null;
  } catch {
    return null;
  }
};

/**
 * Validates if a URL is safe to use as a return URL
 * @param url - The URL to validate
 * @returns true if the URL is valid and safe, false otherwise
 */
export const isValidReturnUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') {
    return false;
  }

  // Must be a relative URL (starts with /)
  if (!url.startsWith('/')) {
    return false;
  }

  // Cannot be the login page (prevents infinite loops)
  if (url === '/login' || url.startsWith('/login?') || url.startsWith('/login#')) {
    return false;
  }

  // Check for reasonable length (prevent abuse)
  if (url.length > 2000) {
    return false;
  }

  // Basic sanitization - check for dangerous characters
  const dangerousPatterns = [
    'javascript:',
    'data:',
    'vbscript:',
    'file:',
    'ftp:',
    '<script',
    'onload=',
    'onerror='
  ];

  const lowerUrl = url.toLowerCase();
  for (const pattern of dangerousPatterns) {
    if (lowerUrl.includes(pattern)) {
      return false;
    }
  }

  return true;
};

/**
 * Creates the current URL string from location
 * @param location - The current location object from React Router
 * @returns The current URL as a string
 */
export const getCurrentUrl = (location: { pathname: string; search: string }): string => {
  return location.pathname + location.search;
};

/**
 * Removes the return URL parameter from current URL search params
 * @param searchParams - URLSearchParams to clean
 * @returns New URLSearchParams without return URL parameter
 */
export const removeReturnUrlParam = (searchParams: URLSearchParams): URLSearchParams => {
  const newParams = new URLSearchParams(searchParams);
  newParams.delete(RETURN_URL_PARAM);
  return newParams;
};
