import { Dialog } from "primereact/dialog";
import { useQuery } from "react-query";
import { DATE_FORMAT, GET_DQM_ERROR_DETAILS } from "../../../../constants";
import { getDQMErrorDetails } from "../../../../services";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";
import { MyTable } from "../../../organisms";
import { memo, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import dayjs from "dayjs";

const HIDED_COLUMNS = ["run_by", "test_Timestamp"];

// display error details in bottom drawer
const ErrorDetailsModalBase = ({ onClose, visible, id }) => {
  const { t } = useTranslation();

  const ref = useRef(null);

  const [columns, setColumns] = useState([]);
  const [height, setHeight] = useState("400px");
  const [maximized, setMaximized] = useState(false);
  const [rows, setRows] = useState([]);
  const [key, setKey] = useState(0);

  // get dqm details
  const {
    data: errorDetails,
    isLoading,
    isError,
  } = useQuery<any, any>([GET_DQM_ERROR_DETAILS, id], () =>
    getDQMErrorDetails(id)
  );

  useEffect(() => {
    if (!errorDetails) {
      return;
    }

    const columns = errorDetails?.columns?.filter(
      (column) => !HIDED_COLUMNS.includes(column)
    );
    setColumns(columns);
    setRows(errorDetails?.rows);
    setKey((key) => key + 1);
  }, [errorDetails]);

  return (
    <Dialog
      maximizable
      maximized={maximized}
      onHide={onClose}
      header={t("View Details")}
      visible={visible}
      style={{ width: "80%" }}
      resizable
      onResizeEnd={() => {
        setHeight(`${ref?.current?.clientHeight - 50}px`);
      }}
      onMaximize={(e) => {
        setMaximized(e.maximized);
        setTimeout(() => {
          setHeight(`${ref?.current?.clientHeight - 50}px`);
        }, 100);
      }}
    >
      <Wrapper id="error-details-wrapper" ref={ref}>
        <div className="info">
          <div>
            <p>test_Timestamp</p>
            <p>{dayjs(rows?.[0]?.test_Timestamp).format(DATE_FORMAT)}</p>
          </div>

          <div>
            <p>run_by</p>
            <p>{rows?.[0]?.run_by}</p>
          </div>
        </div>
        <MyTable
          excelFileName="test_details"
          height={height}
          key={key}
          loading={isLoading}
          isError={isError}
          data={rows}
          columns={columns?.map((column) => {
            return { field: column, headerName: column, flex: 1 };
          })}
        />
      </Wrapper>
    </Dialog>
  );
};

const ErrorDetailsModal = withErrorBoundary(
  memo(ErrorDetailsModalBase),
  "error.generic"
);

export { ErrorDetailsModal };

const Wrapper = styled.div`
  height: 100%;

  & .info {
    display: flex;
    margin-bottom: -40px;
    flex-wrap: wrap;
    justify-content: space-between;
    border: 1px solid green;
    padding: 6px 10px;
    margin-right: 200px;
    margin-top: 10px;
    width: fit-content;
    gap: 50px;

    & > div {
      display: flex;
      gap: 10px;
    }
  }
`;
