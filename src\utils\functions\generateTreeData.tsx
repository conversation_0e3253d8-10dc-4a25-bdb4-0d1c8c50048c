import { EDIT_HEADER_ITEMS } from "../../constants/contextMenus";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { Trans } from "react-i18next";
import { MENU_CREATOR_HOME_ID } from "../../constants";

// dropdown items for menu creator nodes
export const getDropdownListForMenuCreator1 = (
  id: string,
  PERMISSIONS: string[],
  type: string,
  children: any[],
  hasAllowedChildren: boolean,
  isMultiple?: boolean,
  isNew?: boolean
) => {
  if (PERMISSIONS?.includes("DELETE") && isMultiple) {
    return [
      {
        label: <Trans>Delete Selected</Trans>,
        key: "delete-selected",
        icon: <DeleteOutlined />,
      },
    ];
  }
  if (type === "menu") {
    const addAssetMenu = {
      label: <Trans>Define allowed children types</Trans>,
      icon: <PlusOutlined />,
      key: "add-asset",
    };
    const updateAssetMenu = {
      label: <Trans>Define allowed children types</Trans>,
      icon: <EditOutlined />,
      key: "update-asset",
    };
    // const permissionsMenu = {
    //   label: <Trans>Set Permissions</Trans>,
    //   icon: <UserOutlined />,
    //   key: "set-permissions",
    // };

    const addMenu = {
      label: <Trans>Add menu</Trans>,
      icon: <PlusOutlined />,
      key: "add-menu",
    };

    const renameMenu = {
      label: <Trans>Rename</Trans>,
      icon: <EditOutlined />,
      key: "rename",
    };

    if (Number(id) === MENU_CREATOR_HOME_ID) {
      return [addMenu];
    }
    const hasAsset = children?.some((child) => child.type === "asset");
    if (children.length === 0 && !hasAsset) {
      if (isNew) {
        return [
          ...(PERMISSIONS?.includes("ADD") ? [addMenu, addAssetMenu] : []),
          ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
          ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
        ];
      }
      return [
        ...(PERMISSIONS?.includes("ADD") ? [addMenu, addAssetMenu] : []),
        // ...(PERMISSIONS?.includes("PERMISSION") ? [permissionsMenu] : []),
        ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
        ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
      ];
    }
    if (hasAsset) {
      if (isNew)
        return [
          ...(PERMISSIONS?.includes("EDIT")
            ? [updateAssetMenu, renameMenu]
            : []),
          ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
        ];

      return [
        ...(PERMISSIONS?.includes("EDIT") ? [updateAssetMenu, renameMenu] : []),
        // ...(PERMISSIONS?.includes("PERMISSION") ? [permissionsMenu] : []),
        ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
      ];
    }
    if (children.length !== 0) {
      if (isNew)
        return [
          ...(PERMISSIONS?.includes("ADD") ? [addMenu] : []),
          ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
          ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
        ];
      return [
        ...(PERMISSIONS?.includes("ADD") ? [addMenu] : []),
        ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
        // ...(PERMISSIONS?.includes("PERMISSION") ? [permissionsMenu] : []),
        ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
      ];
    }
    if (isNew)
      return [
        ...(PERMISSIONS?.includes("ADD") ? [addAssetMenu] : []),
        ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
        ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
      ];
    return [
      ...(PERMISSIONS?.includes("ADD") ? [addAssetMenu] : []),
      ...(PERMISSIONS?.includes("EDIT") ? [renameMenu] : []),
      // ...(PERMISSIONS?.includes("PERMISSION") ? [permissionsMenu] : []),
      ...(PERMISSIONS?.includes("DELETE") ? EDIT_HEADER_ITEMS : []),
    ];
  }
  // const changeTemplate = {
  //   label: "Change template",
  //   icon: <EditOutlined />,
  //   key: "change-template",
  // };
  if (PERMISSIONS.includes("DELETE")) {
    return [
      {
        label: <Trans>Delete</Trans>,
        key: "delete",
        icon: <DeleteOutlined />,
      },
    ];
  }
  return [];
};
