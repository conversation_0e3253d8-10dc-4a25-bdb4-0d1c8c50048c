import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";

// composite attribute

const CompositeBase = ({ value }) => {
  const templateKeys = Object.keys(value || {});
  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const getAttributes = (attributes, selected) => {
    const selectedAttributes = [];
    selected?.forEach((attr) => {
      const attribute = attributes?.find((att) => att.id == attr);
      selectedAttributes.push(attribute?.name);
    });
    return selectedAttributes?.join(", ");
  };

  if (templateKeys.length > 0) {
    return (
      <div>
        {templateKeys?.map((templateId) => {
          const selectedTemplate = templatesData[Number(templateId)];
          return (
            <div key={templateId}>
              {selectedTemplate?.name}
              {value[templateId]?.length > 0
                ? `(${getAttributes(
                    selectedTemplate?.attributeTemplates,
                    value[templateId]
                  )})`
                : ""}
            </div>
          );
        })}
      </div>
    );
  }
};

export const Composite = withErrorBoundary(
  React.memo(CompositeBase),
  "error.generic"
);
