import { styled } from "@linaria/react";
import { Button, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { deleteNodeService, getAllNodes } from "../../../../services/node";
import { useRef } from "react";
import {
  GET_CHILDRENS,
  GET_NODE_ATTRIBUTES_DETAILS,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import {
  setRemoveTemplate,
  setUpdateFlag,
} from "../../../../store/features/workingVersion";
import { useSearchParams } from "react-router-dom";
import { NEWTEMPLATE } from "../../../../interfaces";
import { Dialog } from "primereact/dialog";
import {
  setBottomNavbarOpen,
  setSelectedBottomNavbar,
} from "../../../../store/features/navigation";

const DeleteWorkingVersion = ({ onClose, isOpen, id }: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const buttonRef = useRef<HTMLButtonElement>(null);

  const selectedSidebarItems = useSelector(
    (state: RootState) => state.sidebar.selected
  );

  const mutation = useMutation(deleteNodeService, {
    onSuccess: async () => {
      dispatch(setBottomNavbarOpen(false));
      dispatch(setSelectedBottomNavbar(""));

      const selectedSidebarItem = selectedSidebarItems.info[0];

      notification.success({
        message: t("Success!"),
        description: t("Draft version deleted successfully!"),
      });

      const nodeDetails = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        searchParams.get("nodeId"),
      ]) as any;
      if (nodeDetails?.flag?.includes(NEWTEMPLATE)) {
        dispatch(setRemoveTemplate(true));
      } else {
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          selectedSidebarItem.id.toString(),
        ]);
        setTimeout(() => {
          dispatch(setUpdateFlag(true));
        }, 600);
      }

      await getAllNodes(selectedSidebarItem.parentId.toString()).then(
        async (childrens: any) => {
          queryClient.setQueryData(
            [GET_CHILDRENS, selectedSidebarItem.parentId.toString()],
            childrens
          );
        }
      );

      onClose();
    },
    onError: (e: any) => {
      notification.error({
        message: "Error in deleting draft version!",
        description: e?.data?.error || "Please try again after sometime",
      });
      captureException(e?.data?.error);
    },
  });

  const handleDelete = () => {
    mutation.mutate({ id: id });
  };

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      className="export-modal draggable-modal"
      header={t("Delete draft version?")}
      onShow={() => {
        buttonRef?.current?.focus();
      }}
    >
      <Wrapper>
        <h5>{t("Do you want to delete draft version?")}</h5>
        <Button
          onClick={handleDelete}
          type="primary"
          ref={buttonRef}
          loading={mutation.isLoading}
        >
          {t("Delete")}
        </Button>
      </Wrapper>
    </Dialog>
  );
};

export { DeleteWorkingVersion };

interface Props {
  onClose: () => void;
  isOpen: boolean;
  id: string;
  label: string;
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      font-size: 12px;
      font-style: italic;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    margin-left: auto;
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
