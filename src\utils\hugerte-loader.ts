/**
 * HugeRTE Bundle Loader - Production ready bundler-first approach
 */

let loadPromise: Promise<void> | null = null;

export const loadHugeRTEBundle = (): Promise<void> => {
  if (loadPromise) return loadPromise;
  if (typeof window === "undefined") return Promise.resolve();
  if ((window as any).hugerte) return Promise.resolve();

  loadPromise = (async () => {
    await import("hugerte/hugerte");

    let attempts = 0;
    while (attempts < 50 && !(window as any).hugerte) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      attempts++;
    }

    if (!(window as any).hugerte) {
      throw new Error("HugeRTE core initialization failed");
    }

    await Promise.all([
      // Core components
      import("hugerte/models/dom/model"),
      import("hugerte/themes/silver"),
      import("hugerte/icons/default"),
      import("hugerte/skins/ui/oxide/skin"),
      import("hugerte/skins/content/default/content"),
      import("hugerte/skins/ui/oxide/content"),

      // All Open Source plugins
      import("hugerte/plugins/accordion"),
      import("hugerte/plugins/advlist"),
      import("hugerte/plugins/anchor"),
      import("hugerte/plugins/autolink"),
      import("hugerte/plugins/autoresize"),
      import("hugerte/plugins/autosave"),
      import("hugerte/plugins/charmap"),
      import("hugerte/plugins/code"),
      import("hugerte/plugins/codesample"),
      import("hugerte/plugins/directionality"),
      import("hugerte/plugins/emoticons"),
      import("hugerte/plugins/fullscreen"),
      import("hugerte/plugins/help"),
      import("hugerte/plugins/image"),
      import("hugerte/plugins/importcss"),
      import("hugerte/plugins/insertdatetime"),
      import("hugerte/plugins/link"),
      import("hugerte/plugins/lists"),
      import("hugerte/plugins/media"),
      import("hugerte/plugins/nonbreaking"),
      import("hugerte/plugins/pagebreak"),
      import("hugerte/plugins/preview"),
      import("hugerte/plugins/quickbars"),
      import("hugerte/plugins/save"),
      import("hugerte/plugins/searchreplace"),
      import("hugerte/plugins/table"),
      import("hugerte/plugins/visualblocks"),
      import("hugerte/plugins/visualchars"),
      import("hugerte/plugins/wordcount"),

      // Plugin resources
      import("hugerte/plugins/emoticons/js/emojis"),
      import("hugerte/plugins/help/js/i18n/keynav/en"),
    ]);
  })();

  return loadPromise;
};

export const isHugeRTEAvailable = (): boolean => {
  return typeof window !== "undefined" && !!(window as any).hugerte;
};
