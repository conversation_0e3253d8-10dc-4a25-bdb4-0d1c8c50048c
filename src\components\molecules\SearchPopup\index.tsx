import { styled } from "@linaria/react";
import { Button, Input } from "antd";
import { MyTooltip } from "../../atoms";
import { ReactComponent as SearchIcon } from "../../../assets/search_menu.svg";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { GET_SEARCH_ENGINE_INFO } from "../../../constants";
import { ISearchEngineInfo } from "../../../interfaces";

const SearchPopup = ({ setSearch, searchText, setSearchText, loading }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const versionInfo = queryClient.getQueryData(
    GET_SEARCH_ENGINE_INFO
  ) as ISearchEngineInfo;

  return (
    <Wrapper>
      <Input
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        placeholder={t("Search...")}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            setSearch(searchText);
          }
        }}
        addonAfter={
          <Button
            type="primary"
            onClick={() => setSearch(searchText)}
            loading={loading}
          >
            <MyTooltip title="Search">
              <SearchIcon />
            </MyTooltip>
          </Button>
        }
      />
      {versionInfo && (
        <p className="version-info">
          Powered by {versionInfo?.engineName}(v{versionInfo?.engineVersion})
        </p>
      )}
    </Wrapper>
  );
};

export { SearchPopup };

const Wrapper = styled.div`
  margin-left: auto;
  margin-right: auto;
  position: relative;
  width: 60%;

  & input {
    min-height: 46px !important;
    padding-left: 20px !important;
    padding-right: 35px;
  }
`;
