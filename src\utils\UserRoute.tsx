import { useSelector } from "react-redux";
import { Navigate, useLocation } from "react-router-dom";
import { RootState } from "../store";
import { createLoginUrlWithReturn, getCurrentUrl } from "./returnUrl";

export const UserRoute = ({ children }) => {
  const { authenticated } = useSelector((root: RootState) => root.auth);
  const location = useLocation();

  if (authenticated === null) {
    return null; // Wait for authentication state to be determined
  }
  if (!authenticated) {
    // Create login URL with return URL parameter
    const currentUrl = getCurrentUrl(location);
    const loginUrl = createLoginUrlWithReturn(currentUrl);
    return <Navigate to={loginUrl} replace />;
  }

  return children;
};
