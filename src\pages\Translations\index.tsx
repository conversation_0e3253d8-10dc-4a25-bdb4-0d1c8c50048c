import { <PERSON><PERSON>, <PERSON>, notification } from "antd";
import { useTheme } from "../../utils/useTheme";
import { useEffect, useState } from "react";
import { AddNewLanguageModal, BreadCrumb, MyTable } from "../../components";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import translationsPl from "../../locales/pl/translation.json";
import translationsEn from "../../locales/en/translation.json";
import { useMutation, useQuery } from "react-query";
import { getTranslationsData, saveTranslationsData } from "../../services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { setLanguages as setGlobalLanguages } from "../../store/features/globalSettings";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { GET_TRANSLATIONS_DATA } from "../../constants";
import { deepClone } from "../../utils";

const TranslationsPage = () => {
  const theme = useTheme();
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();

  const [triggerChange, setTriggerChange] = useState(null);
  const [allTranslations, setAllTranslations] = useState(null);
  const [translationsData, setTranslationsData] = useState([]);
  const [addLanguage, setAddLanguage] = useState(false);
  const [languages, setLanguages] = useState([]);
  const [columns, setColumns] = useState([]);

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const globalLanguages = useSelector(
    (root: RootState) => root.globalSettings.languages
  );

  useEffect(() => {
    setLanguages(globalLanguages);
  }, [globalLanguages]);

  useEffect(() => {
    const allColumns = [];

    languages?.forEach((lang) => {
      allColumns.push({
        headerName: lang.label,
        field: lang.value,
        minWidth: 100,
        flex: 1,
        editable: true,
        withDelete: !(lang.value === "en" || lang.value === "pl"),
      });
    });

    allColumns.push({
      headerName: "",
      field: "actions",
      isEditActions: true,
    });

    setColumns(allColumns);
    setTriggerChange((prev) => (!prev ? 1 : prev + 1));

    if (allTranslations) {
      const data = [];
      Object.keys(translationsPl)?.forEach((key) => {
        if (allTranslations["pl"][key]) {
          const instance = {};
          instance["key"] = key;
          instance["id"] = key;
          languages?.forEach((lang) => {
            instance[lang.value] = allTranslations[lang.value][key];
          });
          data.push(instance);
        }
      });

      setTranslationsData(data);
    }
  }, [languages]);

  useEffect(() => {
    if (allTranslations) {
      const data = [];
      Object.keys(translationsPl)?.forEach((key) => {
        if (allTranslations["pl"][key]) {
          const instance = {};
          instance["key"] = key;
          instance["id"] = key;
          languages?.forEach((lang) => {
            instance[lang.value] = allTranslations[lang.value][key];
          });
          data.push(instance);
        }
      });

      setTranslationsData(data);
    }
  }, [allTranslations]);

  const { isFetching, data, refetch } = useQuery(
    GET_TRANSLATIONS_DATA,
    getTranslationsData,
    {
      onSuccess: (data: any) => {
        if (data.body.length === 0 || !data.body[0].value.translations) {
          setAllTranslations({
            en: translationsEn,
            pl: translationsPl,
          });
        } else {
          setAllTranslations(deepClone(data.body[0].value.translations));
        }
      },
    }
  );

  const deleteMutation = useMutation(saveTranslationsData, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Translations Deleted Successfully!"),
      });
      refetch();
    },
    onError: () => {
      notification.error({
        message: t("Error Occurred!"),
        description: t("Error in deleting translations!"),
      });
    },
  });

  const mutation = useMutation(saveTranslationsData, {
    onSuccess: () => {
      notification.success({
        message: t("Success!"),
        description: t("Translations Published Successfully!"),
      });
      dispatch(setMask(false));
    },
    onError: () => {
      notification.error({
        message: t("Error Occurred!"),
        description: t("Error in saving translations!"),
      });
    },
  });

  const handleSave = () => {
    dispatch(setGlobalLanguages(languages));
    mutation.mutate({
      value: {
        ...(data?.body ? data?.body[0]?.value || {} : {}),
        translations: allTranslations,
        languages: languages,
      },
    });

    Object.keys(allTranslations).forEach((lang) => {
      i18n.addResourceBundle(
        lang,
        "translation",
        allTranslations[lang],
        true,
        true
      );
    });
  };

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs(breadcrumb));
  }, []);

  const detectChange = () => {
    if (!mask) dispatch(setMask(true));
  };

  const handleCancel = () => {
    setTriggerChange((prev) => (!prev ? 1 : prev + 1));
    let prevTranslations = null;
    let prevLanguages = null;

    if (data.body.length !== 0 && !!data.body[0].value.languages) {
      prevLanguages = data.body[0].value.languages;
    } else {
      prevLanguages = [
        {
          label: "English",
          value: "en",
        },
        {
          label: "Polski",
          value: "pl",
        },
      ];
    }
    setLanguages(prevLanguages);

    if (data.body.length === 0 || !data.body[0].value.translations) {
      prevTranslations = {
        en: translationsEn,
        pl: translationsPl,
      };
    } else {
      prevTranslations = data.body[0].value.translations;
    }
    setAllTranslations(prevTranslations);

    const allColumns = [];
    prevLanguages?.forEach((lang) => {
      allColumns.push({
        headerName: lang.label,
        field: lang.value,
        width: 300,
        editable: true,
        withDelete: !(lang.value === "en" || lang.value === "pl"),
      });
    });

    allColumns.push({
      headerName: "",
      field: "actions",
      isEditActions: true,
    });

    setColumns(allColumns);
    setTriggerChange((prev) => (!prev ? 1 : prev + 1));
    dispatch(setMask(false));
  };

  return (
    <Wrapper theme={theme} style={{ border: mask ? "1px solid red" : "none" }}>
      <Spin spinning={deleteMutation.isLoading} fullscreen size="large" />
      <BreadCrumb
        extra={
          <ExtraContainer>
            <Button type="primary" onClick={() => setAddLanguage(true)}>
              {t("Add new language")}
            </Button>
            {mask && (
              <Button
                type="primary"
                onClick={handleCancel}
                className="cancel-button"
              >
                {t("Cancel")}
              </Button>
            )}

            {mask && (
              <Button
                type="primary"
                onClick={handleSave}
                className="save-button"
                loading={mutation.isLoading}
              >
                {t("Save")}
              </Button>
            )}
          </ExtraContainer>
        }
      />
      <div className="content">
        <MyTable
          columns={columns}
          data={translationsData}
          height={"calc(100vh - 165px)"}
          resetTrigger={triggerChange}
          editable
          loading={isFetching}
          excelFileName="properties"
          onRowsEdit={(record) => {
            const newData = [...translationsData];
            const index = newData.findIndex((item) => record.key === item.key);
            const item = newData[index];
            newData.splice(index, 1, {
              ...item,
              ...record,
            });

            setTranslationsData(newData);

            const allTranslationDatas = { ...allTranslations };
            languages.forEach((lang) => {
              allTranslationDatas[lang.value][record.key] = record[lang.value];
            });
            setAllTranslations(allTranslationDatas);
            detectChange();
          }}
          onColumnsDelete={(key) => {
            const newTranslations = { ...allTranslations };
            delete newTranslations[key];
            const newLanguages = languages.filter((lang) => lang.value !== key);
            setLanguages(newLanguages);
            setAllTranslations(newTranslations);

            dispatch(setGlobalLanguages(newLanguages));
            if (i18n.language === key) {
              i18n.changeLanguage("en");
            }
            deleteMutation.mutate({
              value: {
                ...(data?.body ? data?.body[0]?.value || {} : {}),
                translations: newTranslations,
                languages: newLanguages,
              },
            });
          }}
        />
      </div>

      {addLanguage && (
        <AddNewLanguageModal
          onClose={() => {
            setAddLanguage(false);
          }}
          isOpen={addLanguage}
          onSave={(values) => {
            setAllTranslations({
              ...allTranslations,
              [values.value]: { ...allTranslations[values.initializeWith] },
            });

            setLanguages([
              ...languages,
              {
                ...values,
              },
            ]);
            detectChange();
          }}
        />
      )}
    </Wrapper>
  );
};

export default TranslationsPage;

const ExtraContainer = styled.div`
  display: flex;
  gap: 9px;

  & button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }
`;
const Wrapper = styled.div<{ theme: any }>`
  overflow: hidden;

  & td .p-inputtext {
    padding: 6px 12px;
    width: 100%;
  }

  & .content {
    & > div {
      padding: 10px;
    }
  }
`;

const breadcrumb = [
  {
    title: "Settings",
    to: "/settings",
  },
  {
    title: "Translations",
    to: "/translations",
  },
];
