import { styled } from "@linaria/react";
import { Dropdown, Switch, Tooltip } from "antd";
import { InlineSkeleton } from "../SkeletonComponents";
import {
  LineOutlined,
  QuestionCircleOutlined,
  WarningFilled,
} from "@ant-design/icons";
import { EditAttribute } from "../../molecules";
import { useTheme } from "../../../utils/useTheme";
import dayjs from "dayjs";
import {
  ATTRIBUTE_ICONS,
  GET_NODE_ATTRIBUTES_DETAILS,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
  METAATTRIBUTE_ID_LIST1,
  METAATTRIBUTE_ID_LIST2,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
  METAATTRIBUTE_ID_PARENT1,
  METAATTRIBUTE_ID_PARENT1_PREVIEW,
  METAATTRIBUTE_ID_PARENT2,
  ME<PERSON>ATTRIBUTE_ID_PARENT2_PREVIEW,
  RETEN<PERSON>ON_OPTIONS,
  TIMEINTERVAL_OPTIONS,
} from "../../../constants";
import React, { useEffect, useState } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import { DetailsContainer } from "../../organisms";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
import { useSelector } from "react-redux";
import {
  useTargetLocation,
  TARGET_LOCATION_ATTRIBUTE_IDS,
} from "../../../utils/functions/customHooks";

import { RootState } from "../../../store";
import { Composite } from "./Composite";
import { css } from "@linaria/core";
import { SQL } from "./SQL";
import { ShowCodeModal } from "../ShowCodeModal";
import { Compound } from "./Compound";
import { COMPOUND_VISUALIZATION } from "../../../constants/dropdownOptions";
import { Iframe } from "./Iframe";
import { useQueryClient } from "react-query";
import { ViewAllowedChildren } from "./ViewAllowedChildren";
import { Scheduler } from "./Scheduler";
import { DropdownItems } from "./DropdownItems";
import { IAttributeValueType } from "../../../interfaces";
import { Relation } from "./Relation";
import { withErrorBoundary } from "../../withErrorBoundary";

interface Props {
  title: string;
  value: any;
  type: string;
  onEdit?: any;
  dropdownItems?: any[];
  attributeId?: number;
  isEditing?: any;
  onEditClick?: any;
  id?: any;
  help?: any;
  mandatory?: boolean;
  regex?: string;
  readOnly?: boolean;
  titleClassName?: string;
  allowedChildren?: string[];
  disabled?: boolean;
  workingVersion?: boolean;
  attributeValueType?: IAttributeValueType;
  multiplicity?: string;
  noTitle?: boolean;
}

const AttributeItemBase = ({
  title,
  value,
  type,
  onEdit,
  dropdownItems,
  attributeId,
  isEditing,
  onEditClick,
  id,
  noTitle,
  help,
  regex,
  mandatory,
  readOnly,
  titleClassName,
  allowedChildren,
  disabled,
  workingVersion,
  attributeValueType,
  multiplicity: attributeMultiplicity,
}: Props) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();

  const devMode = useSelector((root: RootState) => root.globalSettings.devMode);
  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );

  const [isDetailsOpen, setIsDetailsOpen] = useState(null);
  const [showCode, setShowCode] = useState(false);
  const [multiplicity, setMultiplicity] = useState("");
  // Track edited value for live preview of target location
  const [editedTargetLocationValue, setEditedTargetLocationValue] =
    useState<any>(value);

  const nodeDetails = queryClient.getQueryData([
    GET_NODE_ATTRIBUTES_DETAILS,
    searchParams.get("nodeId"),
  ]) as any;

  useEffect(() => {
    if (
      type === "relation" ||
      type === "compound" ||
      type === "multipleSelect"
    ) {
      if (searchParams?.get("template")) {
        const selectedTemplate =
          templatesData[Number(searchParams?.get("template"))];
        const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
          (attr: any) => attr.id === id
        );
        if (type === "compound") {
          setMultiplicity(selectedAttribute?.multiplicityList1);
        } else {
          setMultiplicity(selectedAttribute?.multiplicity);
        }
      } else {
        const selectedTemplate = templatesData[nodeDetails?.templateId];
        const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
          (attr: any) => attr.id === id
        );
        if (type === "compound") {
          setMultiplicity(selectedAttribute?.multiplicityList1);
        } else {
          setMultiplicity(selectedAttribute?.multiplicity);
        }
      }
    }
  }, [id, templatesData, type, searchParams.get("draft")]);

  const getValue = (attributeType: string, attributeValue: any) => {
    switch (attributeType) {
      case "String": {
        return COMPOUND_VISUALIZATION?.find((val) => val.value === value)
          ?.label;
      }

      case "composite": {
        return <Composite value={value} />;
      }
      case "compound":
      case "compoundSimple":
      case "roleMatrix": {
        return <Compound val={value} id={id} />;
      }
      case "icon": {
        if (
          attributeValue?.startsWith("http") ||
          attributeValue?.startsWith("data:")
        ) {
          return <img src={attributeValue} width={22} height={22} />;
        }

        const selectedIcon = ATTRIBUTE_ICONS.find(
          (item) => item.value === attributeValue
        );
        return <span className="selected-icon"> {selectedIcon?.icon}</span>;
      }

      case "select": {
        return attributeValue ? Object.values(attributeValue)[0] : "";
      }
      case "relation":
      case "lifecycle": {
        return <Relation id={id} className="relations" val={attributeValue} />;
      }
      case "sql":
        return <SQL setShowCode={setShowCode} />;

      case "multipleSelect": {
        return (
          <span onClick={(e) => e.stopPropagation()}>
            {Object.keys(attributeValue || {})?.map((templateId, index) => {
              const delimiter =
                index === Object.keys(attributeValue || {}).length - 1
                  ? " "
                  : ", ";

              return (
                <React.Fragment key={templateId}>
                  {/* <Dropdown
                    menu={{
                      items: NODES_MENU_ITEMS,
                      onClick: (e) =>
                        handleNodeClick(
                          e.key,
                          templateId,
                          attributeValue[templateId]
                        ),
                    }}
                    trigger={["contextMenu"]}
                  > */}
                  <span
                    // onClick={(e) => generateURL(e, templateId)}
                    className={"dropdown-items"}
                  >
                    {attributeValue[templateId]}
                  </span>
                  {/* </Dropdown> */}
                  {delimiter}
                </React.Fragment>
              );
            })}
          </span>
        );
      }

      case "multiselect": {
        return <ViewAllowedChildren value={attributeValue} />;
      }

      case "allowedChildren":
      case "allowedLinks":
        return (
          <ViewAllowedChildren value={attributeValue} />
          // <span onClick={(e) => e.stopPropagation()}>
          //   {attributeValue?.map((attribute, index) => {
          //     const delimiter =
          //       index === attributeValue.length - 1 ? " " : ", ";

          //     return (
          //       <React.Fragment key={attribute?.id}>
          //         <Dropdown
          //           menu={{
          //             items: NODES_MENU_ITEMS,
          //             onClick: (e) =>
          //               handleNodeClick(e.key, attribute?.id, attribute?.name),
          //           }}
          //           trigger={["contextMenu"]}
          //         >
          //           <a
          //             onClick={(e) => generateURL(e, attribute?.id)}
          //             className={"links"}
          //           >
          //             {attribute?.name}
          //           </a>
          //         </Dropdown>
          //         {delimiter}
          //       </React.Fragment>
          //     );
          //   })}
          // </span>
        );

      case "editor": {
        return (
          <span
            className="editor-content content"
            dangerouslySetInnerHTML={{ __html: attributeValue }}
          />
        );
      }

      case "iframe": {
        return (
          <Iframe
            attributeValue={attributeValue}
            readOnly={readOnly}
            onEditClick={() => {
              // eslint-disable-next-line @typescript-eslint/no-unused-expressions
              !readOnly && onEditClick(isEditing ? null : id);
            }}
          />
        );
      }

      case "multiplicity": {
        return `${attributeValue?.text1} ${attributeValue?.text1 && attributeValue?.text2 && ".."
          } ${attributeValue?.text2 || ""}`;
      }

      case "link": {
        return (
          <Dropdown
            menu={{
              items: [
                {
                  label: t("Open in new tab"),
                  key: "open-in-new-tab",
                },
              ],
              onClick: () => {
                onEditClick(null);
                window.open(attributeValue?.url);
              },
            }}
            trigger={["contextMenu"]}
          >
            <a
              onClick={(e) => e.stopPropagation()}
              href={attributeValue?.url}
              target="_blank"
              className="active-link"
            >
              {attributeValue?.label}
            </a>
          </Dropdown>
        );
      }

      case "switch": {
        return (
          <Switch
            checked={attributeValue}
            disabled={readOnly}
            onChange={(e) => {
              if (!readOnly) onEdit(e);
            }}
          />
        );
      }
      case "number":
      case "decimal": {
        if (
          ["ALARM_THRESHOLD", "WARNING_THRESHOLD"]?.includes(attributeValueType)
        )
          return `${attributeValue * 100}%`;
        return i18next.language === "pl"
          ? new Intl.NumberFormat("pl-PL").format(Number(attributeValue))
          : new Intl.NumberFormat("en-US").format(Number(attributeValue));
      }
      case "timeout": {
        return (
          <span>
            {attributeValue?.toLocaleString()} {t("seconds")}
          </span>
        );
      }
      case "password": {
        return "********";
      }
      case "scheduler": {
        return <Scheduler attributeValue={attributeValue} />;
      }

      case "timeInterval": {
        return (
          <span>
            {Object.values(attributeValue)[0] ? (
              <>
                {Object.values(attributeValue)[0] as any}{" "}
                {
                  TIMEINTERVAL_OPTIONS.find(
                    (item) =>
                      item.value === Object.keys(attributeValue || {})[0]
                  )?.label
                }
              </>
            ) : (
              "-"
            )}
          </span>
        );
      }
      case "retention": {
        return (
          <span>
            {attributeValue?.duration}{" "}
            {
              RETENTION_OPTIONS.find(
                (item) => item.value === attributeValue?.value
              )?.label
            }
            {attributeValue?.value === "custom" && (
              <span>
                {" "}
                ({attributeValue?.date} {attributeValue?.time})
              </span>
            )}
          </span>
        );
      }

      case "dropdownItems": {
        return <DropdownItems attributeValue={attributeValue} />;
      }
      case "dateTime": {
        return dayjs(attributeValue).format("YYYY-MM-DD HH:mm");
      }
      case "date": {
        return dayjs(attributeValue).format("YYYY-MM-DD");
      }

      case "externalKeys":
        return "-";

      default:
        // Prevent rendering plain objects
        if (typeof attributeValue === "object" && attributeValue !== null) {
          return JSON.stringify(attributeValue);
        }
        return attributeValue ?? "-";
    }
  };

  // const handleAnchorClick = (event) => {
  //   const { href } = event.currentTarget;
  //   if (!!href && !isWhitelisted(href)) {
  //     api.warning({
  //       message: `Warning!`,
  //       description: `Blocked attempt to non-whitelist URL: ${href}`,
  //     });
  //     event.preventDefault();
  //     if (type === "editor") event.stopPropagation();
  //   }
  // };

  // useEffect(() => {
  //   if (type === "editor") {
  //     const anchorTags = document.querySelectorAll("a");
  //     anchorTags.forEach((anchor) => {
  //       anchor.addEventListener("click", handleAnchorClick);
  //     });

  //     return () => {
  //       anchorTags.forEach((anchor) => {
  //         anchor.removeEventListener("click", handleAnchorClick);
  //       });
  //     };
  //   }
  // }, [value, type]);

  const hasInvalidMultiplicity = () => {
    if (type === "multipleSelect" && !!multiplicity) {
      const multiplicities = multiplicity?.split("..");
      const multiplicityFrom = multiplicities[0];
      const multiplicityTo = multiplicities[1];
      if (multiplicityTo === "n") {
        return false;
      }
      return (
        Number(multiplicityTo) < Object.keys(value || {})?.length ||
        Number(multiplicityFrom) > Object.keys(value || {})?.length
      );
    }

    if ((type === "relation" || type === "lifecycle") && !!multiplicity) {
      const multiplicities = multiplicity?.split("..");
      const multiplicityFrom = multiplicities[0];
      const multiplicityTo = multiplicities[1];
      if (multiplicityTo === "n") {
        return false;
      }
      return (
        Number(multiplicityTo) < value?.length ||
        Number(multiplicityFrom) > value?.length
      );
    }

    return false;
  };
  const hasHyperlinkIsse =
    !isEditing &&
    !readOnly &&
    (["relation", "lifecycle"].includes(type) ||
      (type === "multipleSelect" && hasInvalidMultiplicity())) &&
    (type === "multipleSelect" ||
      (Array.isArray(value) &&
        value.length > 0 &&
        (value.some((obj) => !obj.name) || hasInvalidMultiplicity())));

  const requiredBG =
    (!readOnly &&
      mandatory &&
      ((type === "switch" && value === null) ||
        (type === "relation" && value?.length === 0) ||
        (type === "lifecycle" && value?.length === 0) ||
        !value ||
        value?.length === 0)) ||
    (type === "multiplicity" && (!value?.text1 || !value?.text2)) ||
    hasHyperlinkIsse ||
    (type === "dropdownItems" && (value || []).length === 0) ||
    (regex && !new RegExp(regex).test(value));

  const SM_ROW_CLASSNAME = [
    METAATTRIBUTE_ID_PARENT1,
    METAATTRIBUTE_ID_PARENT2,
    METAATTRIBUTE_ID_PARENT1_PREVIEW,
    METAATTRIBUTE_ID_PARENT2_PREVIEW,
  ];

  const ROW_CLASSNAME = [
    METAATTRIBUTE_ID_LIST1,
    METAATTRIBUTE_ID_LIST2,
    METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
    METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
    METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
    METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
  ];

  const isTableView = ["relation", "lifecycle"];

  const [wrapperDivWidth, setWrapperDivWidth] = useState(null);

  useEffect(() => {
    const workingVersionElement = document.getElementById(
      workingVersion ? "working-version-area" : "workspace-area"
    );

    if (!workingVersionElement) {
      return;
    }

    setWrapperDivWidth(workingVersionElement.offsetWidth);

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setWrapperDivWidth(entry.contentRect.width); // Update width when the div is resized
      }
    });

    resizeObserver.observe(workingVersionElement); // Start observing the div

    // Clean up on unmount
    return () => {
      resizeObserver.unobserve(workingVersionElement);
    };
  }, []);

  // Check if this is one of the Target Location attributes that needs a second column
  const isTargetLocationAttribute = TARGET_LOCATION_ATTRIBUTE_IDS.includes(id);

  // Use edited value for live preview during editing, otherwise use value
  const targetLocationValue =
    isTargetLocationAttribute && isEditing ? editedTargetLocationValue : value;

  // All target location logic is handled inside the hook
  const { targetLocationName, showSpinner } =
    useTargetLocation(targetLocationValue);

  const getClassName = () => {
    if (isTargetLocationAttribute) {
      return flexCSS;
    } else if (ROW_CLASSNAME?.includes(id)) {
      if (wrapperDivWidth > 475) return flexCSS;
    } else if (SM_ROW_CLASSNAME?.includes(id)) {
      if (wrapperDivWidth > 760) {
        return flexSMCSS;
      }
      if (wrapperDivWidth > 475) return flexCSS;
    }
    return "";
  };

  return (
    <>
      <Item
        theme={theme}
        style={
          type === "relation" && value?.length === 1 && !isEditing
            ? { overflow: "hidden" }
            : {}
        }
        className={`${getClassName()} ${noTitle ? "no-title" : ""} ${requiredBG ||
            hasHyperlinkIsse ||
            (type === "dropdownItems" && (value || []).length === 0) ||
            (regex && !new RegExp(regex).test(value))
            ? "required-bg"
            : ""
          } attribute-item`}
      >
        {disabled && <div className="disabled-mask" />}
        {location.pathname.includes("metamodel") &&
          title === "Attribute Type" &&
          !!value &&
          !workingVersionActive && <div className="attribute-mask" />}

        {!noTitle &&
          !(type === "iframe" && !!value && !!value?.url && !isEditing) && (
            <h6
              onClick={() => {
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                !readOnly && onEditClick(isEditing ? null : id);
              }}
              className={`${titleClassName}`}
              style={{
                cursor: readOnly ? "default" : "pointer",
                pointerEvents: readOnly ? "none" : "auto", // Completely disable clicks when readOnly
              }}
            >
              <p className="attribute-title">
                {title}{" "}
                {!!multiplicity && isEditing && <span>({multiplicity})</span>}{" "}
                {!multiplicity && !!attributeMultiplicity && isEditing && (
                  <span>({attributeMultiplicity})</span>
                )}{" "}
                {mandatory && <span className="required">*</span>}
              </p>{" "}
              {devMode && <span>({id})</span>}
              {!!help && (
                <Tooltip title={help}>
                  <QuestionCircleOutlined
                    onClick={(e) => e.stopPropagation()}
                  />
                </Tooltip>
              )}
            </h6>
          )}
        <AttributeContent
          theme={theme}
          className={`${isTableView.includes(type) && "relations-wrapper"}`}
          style={
            type === "relation" && value?.length === 1 && !isEditing
              ? { margin: "-8px 0 -8px -6px" }
              : {}
          }
        >
          {isEditing && !readOnly ? (
            <EditAttribute
              attributeId={attributeId}
              value={value}
              type={type}
              id={id}
              onEdit={(val) => {
                if (isTargetLocationAttribute) {
                  setEditedTargetLocationValue(val);
                }
                onEdit(val);
              }}
              onClose={() => {
                onEditClick(null);
                if (isTargetLocationAttribute) {
                  setEditedTargetLocationValue(value);
                }
              }}
              regex={regex}
              dropdownItems={dropdownItems}
              allowedChildren={allowedChildren}
              attributeMultiplicity={attributeMultiplicity}
            />
          ) : (
            <div className="wrapper">
              {/* Children view requires to have clickable hyper links in readonly mode so applied 'type !== 'allowedChildren'' */}
              <span
                className={readOnly ? "readonly" : "pointer"}
                style={{
                  pointerEvents:
                    readOnly && type !== "allowedChildren" ? "none" : "auto",
                }}
              // onClick={() => {
              //   !readOnly && type !== "link" && onEditClick(id);
              // }}
              >
                {type !== "switch" &&
                  (!value ||
                    value === "" ||
                    value?.length === 0 ||
                    value === null ||
                    (type === "iframe" && !value?.url) ||
                    value === "<p><br></p>" ||
                    ((type === "multiSelect" ||
                      type === "relation" ||
                      type === "lifecycle") &&
                      Object.keys(value).length === 0) ||
                    (type === "link" && !value?.label) ||
                    (type === "multipleSelect" &&
                      Object.keys(value || {})?.length === 0) ||
                    (type === "multiplicity" &&
                      !value?.text1 &&
                      !value?.text2)) ? (
                  <LineOutlined />
                ) : (
                  getValue(type, value)
                )}
              </span>
            </div>
          )}

          {(hasHyperlinkIsse || requiredBG) && !isEditing && (
            <Tooltip
              title={
                hasHyperlinkIsse
                  ? t("Multiplicity value mismatch!")
                  : t("Required")
              }
            >
              <div className="warning">
                <WarningFilled />
              </div>
            </Tooltip>
          )}
        </AttributeContent>

        {!!isDetailsOpen && (
          <DetailsContainer
            id={isDetailsOpen.id}
            isOpen={!!isDetailsOpen}
            onClose={() => setIsDetailsOpen(null)}
            title={isDetailsOpen.name}
          />
        )}

        {showCode && (
          <ShowCodeModal
            isOpen={showCode}
            value={value}
            onCancel={() => {
              setShowCode(false);
            }}
          />
        )}
      </Item>

      {/* Render the second column for Target Location attributes */}
      {isTargetLocationAttribute && (
        <Item theme={theme} className={`${flexCSS} attribute-item`}>
          <h6
            className={`${titleClassName}`}
            style={{
              cursor: "default",
            }}
          >
            <p className="attribute-title">{t("Preview")}</p>
            {devMode && <span>({id}-preview)</span>}
          </h6>
          <AttributeContent theme={theme}>
            <div className="wrapper">
              <span className="readonly">
                {showSpinner ? (
                  <InlineSkeleton />
                ) : (
                  targetLocationName || <LineOutlined />
                )}
              </span>
            </div>
          </AttributeContent>
        </Item>
      )}
    </>
  );
};

export const AttributeItem = withErrorBoundary(
  React.memo(AttributeItemBase),
  "error.generic"
);

const flexCSS = css`
  width: calc(50% - 3px) !important;
`;

const flexSMCSS = css`
  width: calc(25% - 3px) !important;
`;
export const AttributeContent = styled.div<{ theme: any }>`
  overflow-x: visible;
  position: relative;
  /* margin-left: 10px; */
  overflow: auto;

  & .anticon-warning {
    cursor: pointer;
  }
  & .editor-content {
    display: flex;
    flex-direction: column;
    white-space: normal;
    & div {
      padding: 0px !important;
      display: contents;
      flex-direction: column;
    }
    & p {
      display: block !important;
    }

    & iframe {
      border: 1px solid #eee;
    }
    & ul,
    ol {
      display: flex;
      flex-direction: column;
    }
  }
  & .code-block {
    width: fit-content;
  }
  & .warning {
    position: absolute;
    right: 10px;
    transform: translate(0px, -50%);
    font-size: 15px;
    color: #ed4f4f;
    background: white;
    bottom: -13px;
    padding: 7px;
    display: flex;
    align-items: center;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    border-radius: 50%;
  }
  /* & .editor-content img {
    object-fit: contain;
    max-height: 500px;
  } */

  & .editor-content tbody,
  tr,
  td {
    border-style: inherit !important;
  }

  & .active-link {
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
  & .pointer {
    cursor: pointer;
    width: fit-content;
    width: 100%;
    white-space: break-spaces;
  }

  & .readonly {
    width: 100%;
    cursor: default;
  }
  & .links {
    font-size: 13px;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }

  & .ant-breadcrumb {
    border-bottom: 1px solid #eee;
    margin-bottom: 6px;
    padding-bottom: 8px;
  }
  & .wrapper {
    display: flex;
    width: 100%;
    gap: 5px;

    & > a > svg {
      fill: #4277a2;
      width: 18px;
      height: 18px;
    }
  }
  padding: 6px 6px 6px 0px;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: ${({ theme }) => theme.colorPrimary};
  justify-content: center;
  flex: 1;

  & .text {
    display: flex;
    align-items: center;
  }
  & .edit {
    cursor: pointer;
  }
  & .relations .data-table-wrapper {
    /* margin-left: -15px; */

    /* & .ant-dropdown-trigger {
      margin-left: -2px;
    } */
  }
  & a {
    color: ${({ theme }) => theme.colorPrimary};
  }
  & > a {
    color: #439fe9;
  }
  & p {
    display: flex;
    align-items: center;
    font-size: 13px;
    & a {
      cursor: pointer;
    }
    & svg {
      width: 20px;
      fill: #cdcdcd;

      margin-right: 6px;
    }
  }
`;

export const Item = styled.div<{ theme: any }>`
  display: flex;
  align-items: stretch;
  width: 100%;
  gap: 6px;
  background: #fff;
  border: 0.5px solid #eaeaea;
  position: relative;
  border-radius: 4px;
  margin-bottom: 6px;

  & .disabled-mask {
    position: absolute;
    left: 0;
    background: #0000001a;
    width: 100%;
    height: 100%;
    z-index: 10000;
    cursor: not-allowed;
  }
  & .relations-wrapper {
    margin-left: 0px;
  }

  & .attribute-mask {
    position: absolute;
    height: 100%;
    background: #505a620f;
    width: 100%;
    z-index: 1;
    cursor: not-allowed;
  }

  & .p-panel-header-icon i {
    font-size: 14px;
  }

  & .p-panel-header {
    padding: 0px;
    background: #fff;
    border: none;
  }
  & .p-panel-content {
    border: 0px;
    margin-top: 5px;
    padding: 10px 0px 0px 0px;
    border-top: 1px solid #eee;

    & * {
      cursor: default;
    }

    & a {
      cursor: pointer;
    }
  }

  & .p-panel-title {
    font-weight: 400;
  }

  & img {
    margin-right: 5px;
  }
  & h6 {
    font-size: 13px;
    cursor: pointer;
    font-weight: 400;
    align-items: center;
    background-color: ${({ theme }) => theme.bgAttributes};
    border-right: 1px solid #eaeaea;
    color: ${({ theme }) => theme.colorPrimary};
    padding: 6px;
    display: flex;
    gap: 6px;
    max-width: 80%;

    /* & .attribute-title {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    } */

    & .required {
      color: #e52222;
      font-size: 15px;
      line-height: 15px;
    }
    & .search {
      width: 20px;
      fill: #cdcdcd;
      margin-right: 5px;
    }

    & img {
      object-fit: contain;
      margin-top: -3px;
    }
    & > span:first-child {
      height: fit-content;
    }
    & .anticon {
      font-size: 13px;
    }
  }

  & .selected-icon img {
    object-fit: contain;
  }

  &:hover {
    background-color: #f7fcff91;
    & h6 {
      border-color: #98b5cc4d;
    }
    & > div {
      opacity: 1;
    }
  }

  /* & svg {
    cursor: pointer;
  } */
`;
