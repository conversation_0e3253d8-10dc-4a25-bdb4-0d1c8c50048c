// check for unique name on add click
export const checkForUniqueness = (
  name: string,
  templateId,
  data,
  parentId: number | "root",
  id?: number,
  paramsId?: string
) => {
  let isUnique = true;
  const BreakException = {};

  try {
    if (
      parentId === "root" ||
      parentId === 0 ||
      parentId === -1 ||
      parentId === Number(paramsId)
    ) {
      data?.forEach((item) => {
        if (id) {
          if (
            item?.name?.trim()?.toLowerCase() === name?.trim()?.toLowerCase() &&
            templateId == item?.templateId &&
            id !== item.key
          ) {
            isUnique = false;
            throw BreakException;
          }
        } else {
          if (
            item?.name?.trim()?.toLowerCase() === name?.trim()?.toLowerCase() &&
            templateId == item?.templateId
          ) {
            isUnique = false;
            throw BreakException;
          }
        }
      });
    } else if (isUnique) {
      const index = data?.findIndex((item) => item?.key == parentId);
      if (index !== -1 && isUnique) {
        data[index]?.children?.forEach((item) => {
          if (id) {
            if (
              item?.name?.trim()?.toLowerCase() ===
                name?.trim()?.toLowerCase() &&
              templateId == item?.templateId &&
              id !== item.key
            ) {
              isUnique = false;
              throw BreakException;
            }
          } else {
            if (
              item?.name?.trim()?.toLowerCase() ===
                name?.trim()?.toLowerCase() &&
              templateId == item?.templateId
            ) {
              isUnique = false;
              throw BreakException;
            }
          }
        });
      } else if (isUnique) {
        data.forEach((item) => {
          if (isUnique && item.children && item.children.length > 0) {
            isUnique = checkForUniqueness(
              name,
              templateId,
              item.children,
              parentId
            );
            if (!isUnique) throw BreakException;
          }
        });
      }
    }
  } catch (e) {
    if (e !== BreakException) throw e;
  }
  return isUnique;
};
