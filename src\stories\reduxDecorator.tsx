import { Provider } from "react-redux";
import { store } from "../store";
import { QueryClient, QueryClientProvider } from "react-query";
import { MemoryRouter } from "react-router-dom";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

const ReduxDecorator = (Story) => (
  <MemoryRouter>
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <Story />
      </Provider>
    </QueryClientProvider>
  </MemoryRouter>
);

export { ReduxDecorator };
