import { Button } from "antd";
import { Input, Select } from "../../../atoms";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { styled } from "@linaria/react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { Dialog } from "primereact/dialog";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSave: any;
}

type FormData = {
  label: string;
  value: string;
  initializeWith: string;
};

const AddNewLanguageModal = ({ isOpen, onClose, onSave }: Props) => {
  const { t } = useTranslation();
  const { languages } = useSelector((root: RootState) => root.globalSettings);

  const validationSchema = yup.object({
    label: yup.string().required("Required"),
    value: yup.string().required("Required"),
    initializeWith: yup.string().required("Required"),
  });

  const {
    control,
    formState: { errors },
    handleSubmit,
    setFocus,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = (values) => {
    onClose();
    onSave(values);
  };

  useEffect(() => {
    setFocus("label");
  }, [setFocus]);

  return (
    <Dialog
      visible={isOpen}
      onHide={onClose}
      className="draggable-modal"
      footer={null}
      header={t("Add new language")}
    >
      <Container onSubmit={handleSubmit(onSubmit)}>
        <Input
          control={control}
          label="Label"
          name="label"
          error={errors.label?.message}
        />

        <Input
          control={control}
          label="Language Code"
          name="value"
          error={errors.value?.message}
        />
        <Select
          control={control}
          options={[...languages]?.map((item) => {
            return { ...item, title: null };
          })}
          label="Initialize with"
          name="initializeWith"
          error={errors.initializeWith?.message}
        />
        <Button htmlType="submit" type="primary">
          {t("Add")}
        </Button>
      </Container>
    </Dialog>
  );
};

export { AddNewLanguageModal };

const Container = styled.form`
  margin-top: 20px;
  padding-bottom: 40px;

  & .ant-select {
    width: 100%;
  }
`;
