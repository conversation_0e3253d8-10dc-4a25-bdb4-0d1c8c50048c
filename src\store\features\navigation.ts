import { createSlice } from "@reduxjs/toolkit";

export interface NavigationState {
  selectedBottomNavbar: string;
  navigationItems: string[];
  bottomNavbarOpen: boolean;
  tourOpen: boolean;
}

const initialState: NavigationState = {
  selectedBottomNavbar: "",
  tourOpen: false,
  bottomNavbarOpen: false,
  navigationItems: [
    "assets",
    "relation",
    "comment",
    "working-version",
    "history",
    "logs",
    "attachments",
    "graph",
  ],
};

export const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    setNavigationItems: (state, action) => {
      state.navigationItems = action.payload;
    },
    setTourOpen: (state, action) => {
      state.tourOpen = action.payload;
    },
    setBottomNavbarOpen: (state, action) => {
      state.bottomNavbarOpen = action.payload;
    },
    setSelectedBottomNavbar: (state, action) => {
      state.selectedBottomNavbar = action.payload;
    },
  },
});

export const {
  setNavigationItems,
  setTourOpen,
  setSelectedBottomNavbar,
  setBottomNavbarOpen,
} = navigationSlice.actions;

export default navigationSlice.reducer;
