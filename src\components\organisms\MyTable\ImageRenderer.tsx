import React, { memo, useState } from "react";

import type { CustomCellEditorProps } from "ag-grid-react";
import { Flex, notification, Upload } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { uploadAttachment } from "../../../services";
import { useNotification } from "../../../utils/functions/customHooks";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { withErrorBoundary } from "../../withErrorBoundary";

export default withErrorBoundary(
  memo(({ value, onValueChange }: CustomCellEditorProps) => {
    const { t } = useTranslation();
    const [image, setImage] = useState("");
    const { contextHolder, showErrorNotification } = useNotification();

    const beforeUpload = (file) => {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        notification.warning({
          message: t("File must be smaller than 1MB!"),
        });
      }
      return isLt1M;
    };

    const uploadAttachmentMutation = useMutation(uploadAttachment, {
      onError: () => {
        showErrorNotification("Error in uploading!");
      },
    });

    const handleCustomRequest = async ({ onSuccess, file }: any) => {
      const formData = new FormData();
      formData.append("fileType", "DATAFILE");
      formData.append("multipartFile", file.originFileObj || file);

      const fileID = await uploadAttachmentMutation.mutateAsync(formData);
      setImage(`${import.meta.env.VITE_APP_API_URL}file/get/${fileID}`);
      onValueChange(`${import.meta.env.VITE_APP_API_URL}file/get/${fileID}`);
      onSuccess("ok");
    };

    return (
      <div tabIndex={1}>
        {contextHolder}
        {/* {globalPermissions.includes("ATTACH") && ( */}
        <Upload
          beforeUpload={beforeUpload}
          customRequest={handleCustomRequest}
          accept="image/png, image/jpeg"
          showUploadList={false}
        >
          <Flex gap={10}>
            <img src={value} width={100} />
            <div className="edit">
              <EditOutlined /> {t("Edit")}
            </div>
            {image && (
              <div
                className="edit"
                onClick={(e) => {
                  e.stopPropagation();
                  setImage("");
                  onValueChange("");
                }}
              >
                <DeleteOutlined /> {t("Delete")}
              </div>
            )}
          </Flex>
        </Upload>
        {/* )} */}
      </div>
    );
  }),
  "error.generic"
);
