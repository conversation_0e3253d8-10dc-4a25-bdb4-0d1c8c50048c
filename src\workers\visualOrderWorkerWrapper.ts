import * as Comlink from "comlink";
import { ITreeData } from "../interfaces";
import {
  extractVisualOrderMap,
  generateMoveDataRecursively,
} from "../utils/functions/visualOrderExtractor";

const sanitizeTreeData = (
  treeData: ITreeData[] | null | undefined
): any[] | null | undefined => {
  if (!treeData) return treeData;

  return treeData.map((node) => ({
    key: node.key,
    children: node.children ? sanitizeTreeData(node.children) : undefined,
  }));
};

let worker: Worker | null = null;
let workerApi: any = null;
let workerInitialized = false;

try {
  worker = new Worker(new URL("./visualOrderWorker.ts", import.meta.url), {
    type: "module",
  });
  workerApi = Comlink.wrap(worker);
  workerInitialized = true;
} catch {
  // Silently fail and use fallback implementation
}

export const terminateWorker = (): void => {
  if (worker) {
    worker.terminate();
    worker = null;
    workerApi = null;
    workerInitialized = false;
  }
};

export const extractVisualOrderMapAsync = async (
  treeData: ITreeData[] | null | undefined
): Promise<Record<string, number>> => {
  if (!workerInitialized || !workerApi) {
    return extractVisualOrderMap(treeData);
  }

  try {
    const sanitizedTreeData = sanitizeTreeData(treeData);
    return await workerApi.extractVisualOrderMap(sanitizedTreeData);
  } catch {
    return extractVisualOrderMap(treeData);
  }
};

export const generateMoveDataRecursivelyAsync = async (
  currentTreeData: ITreeData[] | null | undefined,
  originalOrderMap: Record<string, number>,
  movePayload: { [key: string]: any } = {}
): Promise<Record<string, number>> => {
  if (!workerInitialized || !workerApi) {
    return generateMoveDataRecursively(
      currentTreeData,
      originalOrderMap,
      {},
      movePayload
    );
  }

  try {
    const sanitizedTreeData = sanitizeTreeData(currentTreeData);
    return await workerApi.generateMoveDataRecursively(
      sanitizedTreeData,
      originalOrderMap,
      movePayload
    );
  } catch {
    return generateMoveDataRecursively(
      currentTreeData,
      originalOrderMap,
      {},
      movePayload
    );
  }
};

export const extractNodeKeysInOrderAsync = async (
  nodes: ITreeData[]
): Promise<string[]> => {
  if (!workerInitialized || !workerApi) {
    return nodes.map((node) => node.key.toString());
  }

  try {
    const sanitizedNodes = sanitizeTreeData(nodes);
    return await workerApi.extractNodeKeysInOrder(sanitizedNodes);
  } catch {
    return nodes.map((node) => node.key.toString());
  }
};

export const identifyChangedPositionsAsync = async (
  originalOrder: string[],
  newOrder: string[]
): Promise<Record<string, number>> => {
  if (!workerInitialized || !workerApi) {
    const result: Record<string, number> = {};
    const originalPositions: Record<string, number> = {};

    originalOrder.forEach((key, index) => {
      originalPositions[key] = index + 1;
    });

    newOrder.forEach((key, index) => {
      const newPosition = index + 1;
      const originalPosition = originalPositions[key];

      if (originalPosition !== newPosition) {
        result[key] = newPosition;
      }
    });

    return result;
  }

  try {
    return await workerApi.identifyChangedPositions(originalOrder, newOrder);
  } catch {
    return {};
  }
};

export const compareTreeDataAndGeneratePayloadAsync = async (
  originalTreeData: ITreeData[] | null | undefined,
  updatedTreeData: ITreeData[] | null | undefined,
  movePayload: { [key: string]: any } = {}
): Promise<Record<string, number>> => {
  if (!workerInitialized || !workerApi) {
    const originalOrderMap = extractVisualOrderMap(originalTreeData);
    return generateMoveDataRecursively(
      updatedTreeData,
      originalOrderMap,
      {},
      movePayload
    );
  }

  try {
    const sanitizedOriginalTreeData = sanitizeTreeData(originalTreeData);
    const sanitizedUpdatedTreeData = sanitizeTreeData(updatedTreeData);

    return await workerApi.compareTreeDataAndGeneratePayload(
      sanitizedOriginalTreeData,
      sanitizedUpdatedTreeData,
      movePayload
    );
  } catch {
    const originalOrderMap = extractVisualOrderMap(originalTreeData);
    return generateMoveDataRecursively(
      updatedTreeData,
      originalOrderMap,
      {},
      movePayload
    );
  }
};
