import Home from "../HomeWithCards";
import HomeV1 from "../HomeWithTables";
import HomeV2 from "../HomeV2";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { useEffect } from "react";
import { setBreadcrumb, setParentBreadcrumbs } from "../../store/features";

const MainHome = () => {
  const homeVersion = useSelector(
    (root: RootState) => root.localSettings.homeVersion
  );

  const getHomeLayout = () => {
    switch (homeVersion) {
      case "v1":
        return <Home />;
      case "v2":
        return <HomeV1 />;
      case "v3":
        return <HomeV2 />;
    }
  };
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(
      setParentBreadcrumbs([
        {
          title: "Home",
          to: "/",
        },
      ])
    );
    dispatch(setBreadcrumb([]));
  }, []);

  return <>{getHomeLayout()}</>;
};

export default MainHome;
