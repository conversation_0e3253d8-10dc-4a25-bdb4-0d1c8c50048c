import { createSlice } from "@reduxjs/toolkit";
import { INodes } from "../../interfaces";

export interface TrashcanState {
  trashCollapsed: boolean;
  selectedTrash: {
    keys: number[];
    info: INodes[];
  };
  expandedTrashKeys: number[];
  performTrash: any;
  trashcanDrawerMask: boolean;
}

const initialState: TrashcanState = {
  trashCollapsed: true,
  selectedTrash: {
    keys: [],
    info: [],
  },
  expandedTrashKeys: [],
  performTrash: null,
  trashcanDrawerMask: false,
};

export const trashSlice = createSlice({
  name: "trashcan",
  initialState,
  reducers: {
    setSelectedTrash: (state, action) => {
      state.selectedTrash = action.payload;
    },
    setExpandedTrashKeys: (state, action) => {
      state.expandedTrashKeys = action.payload;
    },

    setTrashCollapsed: (state, action) => {
      state.trashCollapsed = action.payload;
    },
    setPerformTrash: (state, action) => {
      state.performTrash = action.payload;
    },
    setTrashcanDrawerMask: (state, action) => {
      state.trashcanDrawerMask = action.payload;
    },
  },
});

export const {
  setSelectedTrash,
  setTrashCollapsed,
  setExpandedTrashKeys,
  setPerformTrash,
  setTrashcanDrawerMask,
} = trashSlice.actions;

export default trashSlice.reducer;
