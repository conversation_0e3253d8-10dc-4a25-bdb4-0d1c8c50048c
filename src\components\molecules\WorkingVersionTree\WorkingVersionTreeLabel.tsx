import { ReactNode, memo } from "react";
import { useTheme } from "../../../utils";
import { styled } from "@linaria/react";
import { Dropdown } from "antd";
import { getWorkingVersionMenus } from "../../../constants";
import { useTranslation } from "react-i18next";
import { IBreadcrumbs } from "../../../interfaces";
import { usePermissions } from "../../../utils/functions/customHooks";

const WorkingVersionTreeLabel = memo(
  ({
    label,
    id,
    setAction,
    parentId,
    isLeaf,
    setDropdownOpen,
    templateId,
    allowedChildrens,
    icon,
    setSelected,
    permissionsId,
    count,
  }: Props) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const { getPermissions } = usePermissions();

    const contextMenuItems = getWorkingVersionMenus(
      templateId,
      getPermissions(permissionsId)
    ) as any;

    const handleSidebarMenuChange = ({ key }, id, value) => {
      const selectedContextMenu = contextMenuItems
        // selectedTrash.keys.length > 1
        // ? SIDEBAR_MENUS_MULTIPLE
        .find((item) => item.key == key);

      setDropdownOpen(true);
      switch (key) {
        default: {
          setAction({
            id: id,
            label: value,
            key: key,
            parentId: parentId,
            title: key.startsWith("add") ? (
              <>
                {selectedContextMenu?.label} {t("to")} {value}
              </>
            ) : (
              selectedContextMenu?.label
            ),
            templateId: selectedContextMenu?.templateid || templateId,
            allowedChildrens: allowedChildrens,
          });
        }
      }
    };

    return (
      <Item theme={theme} key={id.toString()}>
        {!!count && <div className="count">{count}</div>}
        <Dropdown
          onOpenChange={(open) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            setDropdownOpen && setDropdownOpen(open);
            if (open) {
              // if (!selectedTrash.keys.includes(id)) {
              const selectedNodeInfo = {
                id: id,
                parentId: parentId,
                name: label,
                isLeaf: isLeaf,
                templateId: templateId,
              };
              setSelected({ keys: [id], info: [selectedNodeInfo] });
              // dispatch(
              //   setSelectedTrash({
              //     keys: [Number(id)],
              //     info: [selectedNodeInfo],
              //   })
              // );
              // dispatch(setBreadcrumb([...breadcrumbs]));
              // navigate(`${getPathname()}?nodeId=${id}&trash=true`);
              // const focusedNodes = { ...focusedNode };
              // focusedNodes[params?.nodeId] = id;
              // dispatch(setFocusedNode(focusedNodes));
            }
            // }
          }}
          menu={{
            items: contextMenuItems,
            onClick: (e) => handleSidebarMenuChange(e, id, label),
          }}
          trigger={["contextMenu"]}
        >
          <div className="sidebar-title">
            {icon}
            <p>{label}</p>
          </div>
        </Dropdown>
      </Item>
    );
  }
);

export { WorkingVersionTreeLabel };

interface Props {
  // label
  label: string;
  id: number;
  setAction: any;
  permissionsId: number;
  parentId: number;
  isMultiple?: boolean;
  setDropdownOpen: any;
  templateId?: number;
  icon: ReactNode;
  allowedChildrens: any[];
  breadcrumbs: IBreadcrumbs[];
  isLeaf?: boolean;
  childrens?: number;
  expandNode: any;
  selected: any;
  setSelected: any;
  count?: number;
}

const Item = styled.div<{ theme: any }>`
  position: relative;
  display: flex;
  color: ${({ theme }) => theme.colorPrimary};
  font-size: 13px;
  width: 100%;

  & .count {
    position: absolute;
    right: 0px;
    top: 0px;
    bottom: 0px;
    margin: auto;
    min-width: 23px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    max-height: 15px;
    background-color: ${({ theme }) => theme.bgAttributes};
    color: ${({ theme }) => theme.colorPrimary};
  }

  & .sidebar-title {
    display: flex;
    gap: 5px;
    padding-left: 3px;
    align-items: center;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }

  & .anticon-pushpin {
    right: 22px;
    font-size: 14px;
    top: 3px;
  }
  & span {
    color: ${({ theme }) => theme.colorPrimary};
    position: absolute;
    opacity: 0;
    top: 2px;
    font-size: 18px;
    transition: all 0.3s ease-in;
    right: 0px;
    &:hover {
      opacity: 0.8;
    }
  }
  &:hover span {
    opacity: 1;
    transition: all 0.2s ease-in;
  }

  & .ant-dropdown-trigger {
    width: 100%;
  }
`;
