import { useEffect, startTransition } from "react";
import { API } from "../../api";
import { useDispatch } from "react-redux";
import { setAuthenticated } from "../../../store/features/auth";
import { useNavigate, useLocation } from "react-router-dom";
import { useQueryClient } from "react-query";
import { createLoginUrlWithReturn, getCurrentUrl } from "../../returnUrl";

const useAxiosInterceptor = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();

  useEffect(() => {
    const requestInterceptor = API.interceptors.request.use(
      async (config) => {
        const token = localStorage.getItem("token");
        if (token) {
          config.headers["CDO-TOKEN"] = token;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    const responseInterceptor = API.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error?.status === 401) {
          // Clear token immediately
          localStorage.removeItem("token");

          // Create login URL with return URL parameter
          const currentUrl = getCurrentUrl(location);
          const loginUrl = createLoginUrlWithReturn(currentUrl);

          // Wrap state updates and navigation in startTransition
          startTransition(() => {
            queryClient.clear();
            dispatch(setAuthenticated(false));
            navigate(loginUrl);
          });
        }
        return Promise.reject(error);
      }
    );

    return () => {
      API.interceptors.request.eject(requestInterceptor);
      API.interceptors.response.eject(responseInterceptor);
    };
  }, []);
};

export { useAxiosInterceptor };
