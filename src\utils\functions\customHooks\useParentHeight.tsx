import { useState, useLayoutEffect, useRef, useCallback } from "react";
import { throttle } from "lodash";

export function useParentHeight(fallbackHeight = 400) {
  const elementRef = useRef(null);
  const observerRef = useRef(null);
  const prevHeightRef = useRef(fallbackHeight);

  const [height, setHeight] = useState(fallbackHeight);

  const MAX_DEPTH = 4;

  const measureHeight = useCallback(
    throttle(
      () => {
        const el = elementRef.current;
        if (!el) return;

        let parentHeight = 0;
        let currentEl = el.parentElement;
        let depth = 0;

        // In some scenarios, we may face an issue where the parent element's height is 0.
        // This ensures, we traverse at least up to MAX_DEPTH to find a valid height.
        // If not, we fallback to the provided fallbackHeight.
        while (currentEl && parentHeight === 0 && depth < MAX_DEPTH) {
          parentHeight = currentEl.clientHeight;
          if (parentHeight === 0) {
            currentEl = currentEl.parentElement;
            depth++;
          }
        }

        const newHeight = parentHeight > 0 ? parentHeight : fallbackHeight;
        if (newHeight !== prevHeightRef.current) {
          prevHeightRef.current = newHeight;
          setHeight(newHeight);
        }
      },
      50,
      { leading: true, trailing: true }
    ),
    [fallbackHeight]
  );

  useLayoutEffect(() => {
    measureHeight();

    observerRef.current = new ResizeObserver(measureHeight);

    let currentEl = elementRef.current?.parentElement;
    let depth = 0;

    while (currentEl && depth < MAX_DEPTH) {
      observerRef.current.observe(currentEl);
      currentEl = currentEl.parentElement;
      depth++;
    }

    return () => {
      observerRef.current?.disconnect();
      measureHeight.cancel();
    };
  }, [measureHeight]);

  return { ref: elementRef, height };
}
