import { styled } from "@linaria/react";
import { BreadCrumb, GraphComponent } from "../../components";
import { useParams } from "react-router-dom";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setBreadcrumb } from "../../store/features";
import { getHierarchyDetails, getAllNodeDetails } from "../../services/node";
import { useTemplateActions } from "../../utils/functions/customHooks/useTemplateActions";
import { withErrorBoundary } from "../../components/withErrorBoundary";

const GraphPageBase = () => {
  const params = useParams();
  const dispatch = useDispatch();
  const { getTemplateName } = useTemplateActions();

  useEffect(() => {
    (async () => {
      try {
        const hierarchy = await getHierarchyDetails(params.nodeId);
        const pathIds = hierarchy?.path?.map((item) => String(item.key)) || [];
        const allIds = [...pathIds, String(params.nodeId)];
        const nodeDetailsArr = await getAllNodeDetails(allIds.join(","));
        const nodeMap = Object.fromEntries(
          nodeDetailsArr.map((n) => [String(n.id), n])
        );
        const breadcrumbArr = [...pathIds, String(params.nodeId)]
          .map((id, index) => {
            const n = nodeMap[id];
            if (!n) return null;
            return {
              id: n.id,
              title: n.name,
              parentId: n.parentId,
              templateName: getTemplateName(n.templateId),
              to: `/details/${n.id}`,
              pathname: `/details/${n.id}`,
              index,
              disabled: false,
            };
          })
          .filter(Boolean);
        dispatch(setBreadcrumb(breadcrumbArr));
      } catch {
        dispatch(setBreadcrumb([]));
      }
    })();
    // Cleanup: clear breadcrumb on unmount
    return () => {
      dispatch(setBreadcrumb([]));
    };
  }, [params?.nodeId, dispatch, getTemplateName]);

  return (
    <Main>
      <BreadCrumb />

      <Wrapper>
        <Container>
          <GraphComponent fromTrashcan={false} id={params?.nodeId} />
        </Container>
      </Wrapper>
    </Main>
  );
};

export default withErrorBoundary(React.memo(GraphPageBase), "error.generic");

const Main = styled.div`
  overflow: hidden;
  height: 100%;
  width: 100%;
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
`;

const Wrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f3f2ed;
  height: calc(100% - 32px);
`;
