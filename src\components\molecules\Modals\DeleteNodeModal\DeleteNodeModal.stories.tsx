import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { DeleteNodeModal } from ".";
import { Button, Space } from "antd";
import { useState } from "react";
import { QueryClient, QueryClientProvider } from "react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

const ModalComponent = () => {
  const [open, setOpen] = useState(false);
  const [isMultiple, setIsMultiple] = useState(false);

  return (
    <QueryClientProvider client={queryClient}>
      <Space>
        <Button type="primary" onClick={() => setOpen(true)}>
          Click to delete
        </Button>
        <Button
          type="primary"
          onClick={() => {
            setIsMultiple(true);
            setOpen(true);
          }}
        >
          Click to delete multiple
        </Button>
      </Space>
      <DeleteNodeModal
        id="1"
        label="My Folder"
        isMultiple={isMultiple}
        isOpen={open}
        onClose={() => {
          setOpen(false);
          setIsMultiple(false);
        }}
      />
    </QueryClientProvider>
  );
};

const meta: Meta<typeof DeleteNodeModal> = {
  title: "Components/Molecules/Modals/DeleteNodeModal",
  component: ModalComponent,
};

export default meta;

type Story = StoryObj<typeof ModalComponent>;

export const Basic: Story = {
  args: {
    label: "",
    id: "",
    isOpen: false,
    isMultiple: false,
  },
};

export const Multiple: Story = {
  args: {},
};
