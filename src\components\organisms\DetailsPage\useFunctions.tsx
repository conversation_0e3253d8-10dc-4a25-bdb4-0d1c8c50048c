import { useSearchParams } from "react-router-dom";
import { ITreeData } from "../../../interfaces";

const useFunctions = () => {
  const [searchParams] = useSearchParams();
  // const treeData = useSelector((state: RootState) => state.sidebar.treeData);
  const nodeId = Number(searchParams.get("nodeId"));

  const updateAllowedChildrens = (
    inMemoryTree,
    templateIds: number[],
    setAction,
    setDropdownOpen
  ) => {
    if (nodeId) {
      updateAllowedChildrenRecursively(
        inMemoryTree,
        templateIds,
        setAction,
        setDropdownOpen
      );
      // dispatch(setTreeData([...inMemoryTree]));
    }
  };

  const updateAllowedChildrenRecursively = (
    data: ITreeData[],
    templateIds,
    setAction,
    setDropdownOpen
  ) => {
    const BreakException = {};
    try {
      const index = data?.findIndex(
        (instance: ITreeData) => instance.key === nodeId
      );
      if (index !== -1) {
        data[index].allowedChildrens = templateIds;
        throw BreakException;
      }
      data?.forEach((item) => {
        if (item?.children && item?.children?.length > 0) {
          updateAllowedChildrenRecursively(
            item?.children,
            templateIds,
            setAction,
            setDropdownOpen
          );
        }
      });
    } catch (e) {
      if (e !== BreakException) throw e;
    }
  };

  return { updateAllowedChildrens };
};

export { useFunctions };
