import { PlusOutlined } from "@ant-design/icons";
import { ReactComponent as NewTabIcon } from "../../assets/newTab.svg";
import { IDropdownOptions, ITemplates } from "../../interfaces";
import { Trans } from "react-i18next";
import { getSortOptions } from "../../constants/menus/sidebar";

// used in breadcrumbs to generate options for templates
export const generateMetamodelOptions = (
  templatesData: { number: ITemplates },
  id: number,
  globalPermissions: string[],
  isLast?: boolean,
  t?: any,
  hasMultipleRootNodes?: boolean
): IDropdownOptions[] => {
  if (!templatesData) {
    return;
  }
  const metamodelTemplate = templatesData[Number(id)];
  const templates = [] as IDropdownOptions[];

  if (metamodelTemplate && globalPermissions.includes("ADD")) {
    const sortedAllowedChildrens = [
      ...(metamodelTemplate?.allowedChildren || []),
    ].sort((a, b) => a.name.localeCompare(b.name));

    sortedAllowedChildrens?.forEach((allowedChild) => {
      templates.push({
        key: allowedChild?.id,
        label: (
          <span>
            <Trans>Add</Trans> {allowedChild?.name}
          </span>
        ),
        icon: <PlusOutlined />,
      });
    });
  }

  // Add sort options for special menu items (metamodel, roles, actions, etc.)
  // These are always menu items, never tree nodes
  // Only show if there are more than 1 root-level nodes
  if (globalPermissions?.includes("SORT") && t && hasMultipleRootNodes) {
    if (templates.length > 0) templates.push({ type: "divider" });
    templates.push({
      label: (
        <div className="header-menus">
          <Trans>Sort</Trans>
        </div>
      ),
      icon: <i className="pi pi-sort-alt" />,
      key: "sort",
      children: getSortOptions(t),
    });
  }

  if (!isLast) {
    if (
      globalPermissions.includes("ADD") ||
      globalPermissions?.includes("SORT")
    )
      templates.push({ type: "divider" });
    templates.push({
      label: (
        <div className="header-menus">
          <NewTabIcon className="new-tab" /> <Trans>Open in new tab</Trans>
        </div>
      ),
      key: "open",
    });
  }
  return templates;
};
