import { styled } from "@linaria/react";

export const SearchWrapper = styled.div`
  padding: 20px 12px;
  border: 1px solid #eee;
  border-radius: 8px;

  & input {
    display: block;
    margin-bottom: 14px;
    border-radius: 4px;
  }

  & .button-wrapper {
    display: flex;
    align-items: center;

    & button {
      min-width: 38px;
      font-size: 12px;
      border-radius: 4px;
      box-shadow: none;
      padding: 4px 10px;
    }

    & a {
      margin-left: auto;
      color: #134d81;
      font-size: 13px;
    }
  }
`;

export const Wrapper = styled.div<{ theme: any }>`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  & .title-container {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  /* background-color: #fff !important; */

  & .has-mask {
    border: 1px solid red;
  }

  & img {
    object-fit: contain;
  }
  & .content {
    flex: 1;
  }
  & .table-wrapper {
    padding-bottom: 0px !important;
    height: 100%;
  }

  & .ant-table-body {
    background-color: #f3f3f3 !important;
  }
  & .ant-tag {
    background: ${({ theme }) => theme.bgAttributes};
    margin-right: 0px;
    border: 1px solid ${({ theme }) => theme.colorSecondary};
    color: ${({ theme }) => theme.colorSecondary};
  }

  & .ant-dropdown-trigger {
    font-size: 13px;
  }

  & .content {
    & > div {
      padding: 10px;
    }
  }
`;

export const LoadMore = styled.div`
  font-size: 20px;
  display: flex;
  padding: 5px !important;
  justify-content: center;
`;
