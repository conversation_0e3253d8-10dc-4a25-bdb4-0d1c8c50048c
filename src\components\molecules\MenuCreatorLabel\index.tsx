import { Dropdown } from "antd";
import { ReactComponent as FolderIcon } from "../../../assets/folder.svg";
import { ReactComponent as FileIcon } from "../../../assets/file.svg";
import { IMenuCreatorKey } from "../../../pages/MenuCreator";
import { getDropdownListForMenuCreator1 } from "../../../utils";
import { DELETED_FLAG } from "../../../interfaces";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

interface Props {
  handleActionsSelect: (
    key: IMenuCreatorKey,
    id: string,
    name: string,
    parentId: string,
    templateId?: string[]
  ) => void;
  id: string;
  name: string;
  parentId: string;
  type: string;
  childrens: any[];
  hasAllowedChildren: boolean;
  isMultiple?: boolean;
  setOpen?: any;
  count: number;
  selected: any;
  setSelected: any;
  isNew: boolean;
}

const MenuCreatorLabel = ({
  handleActionsSelect,
  id,
  name,
  parentId,
  type,
  childrens,
  hasAllowedChildren,
  isMultiple,
  count,
  selected,
  setSelected,
  isNew,
}: Props) => {
  const templateIds = [];
  const globalPermissions = useSelector(
    (root: RootState) => root.auth.globalPermissions
  );

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  if (templatesData && hasAllowedChildren && childrens.length > 0) {
    childrens.forEach((child) => {
      const selectedTemplate = templatesData[Number(child.templateId)];
      templateIds.push({
        id: child.templateId,
        name: selectedTemplate?.name,
        inTrash: selectedTemplate?.flag?.includes(DELETED_FLAG),
      });
    });
  }

  return (
    <Dropdown
      onOpenChange={() => {
        if (!selected.keys.includes(id)) {
          const selectedNodeInfo = {
            id: id,
            parentId: parentId,
            name: name,
            type: type,
            isNew: isNew,
          };
          setSelected({
            keys: [Number(id)],
            info: [selectedNodeInfo],
          });
        }
      }}
      menu={{
        items: getDropdownListForMenuCreator1(
          id,
          globalPermissions,
          type,
          childrens,
          hasAllowedChildren,
          isMultiple,
          isNew
        ),
        onClick: (e) => {
          handleActionsSelect(
            e.key as IMenuCreatorKey,
            id,
            name,
            parentId,
            templateIds
          );
        },
      }}
      trigger={["contextMenu"]}
    >
      <div style={{ position: "relative" }}>
        {!!count && <div className="tree-count">{count}</div>}

        {isNew ? (
          <div className={"new-menu"} />
        ) : type === "asset" ? (
          <FileIcon className="folder-icon" />
        ) : (
          <FolderIcon className="folder-icon" />
        )}
        {name}
      </div>
    </Dropdown>
  );
};

export { MenuCreatorLabel };
