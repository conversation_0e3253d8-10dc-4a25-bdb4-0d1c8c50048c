import { styled } from "@linaria/react";
import { <PERSON><PERSON>, Modal, notification } from "antd";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getNodeDetails, restoreNodes } from "../../../../services/node";
import React, { useEffect, useRef } from "react";
import {
  GET_CHILDRENS,
  GET_LOCAL_SETTINGS_KEY,
  GET_NODE_ATTRIBUTES_DETAILS,
  GET_NODE_DETAILS,
  getAttributeIcon,
  TRASHCAN_PARENT_NODE_ID,
} from "../../../../constants";
import { captureException } from "@sentry/react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { ILocalSettings } from "../../../../interfaces";
import { setPerformTrash } from "../../../../store/features/trashcan";
import { useNavigate, useSearchParams } from "react-router-dom";
import { transformObjectPath } from "../../../../utils";
import { setSelected } from "../../../../store/features";
import { saveLocalSettings } from "../../../../services";
import { setPinned } from "../../../../store/features/pinned";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

const RestoreNodeModalBase = ({
  onClose,
  isOpen,
  isMultiple,
  action,
}: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const ref = useRef<any>();

  const { label, templateId, isLeaf } = action;
  const { selectedTrash } = useSelector((state: RootState) => state.trash);
  const pinned = useSelector((state: RootState) => state.pinned.pinned);
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  useEffect(() => {
    ref?.current?.focus();
  }, [isOpen]);

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const nodeDetails = useQuery(
    [GET_NODE_DETAILS, selectedTrash.keys[0].toString()],
    () => getNodeDetails(selectedTrash.keys[0])
  );

  const pinnedMutation = useMutation(saveLocalSettings);

  const restoreMutation = useMutation(restoreNodes, {
    onSuccess: async () => {
      const newPinned = [];
      pinned?.forEach((pin) => {
        if (selectedTrash.keys.includes(pin.id)) {
          newPinned.push({ ...pin, inTrash: false });
        } else {
          newPinned.push({ ...pin });
        }
      });

      if (newPinned.length > 0) {
        updatePinned(newPinned);
      }

      const newParentInfo = await getNodeDetails(selectedTrash.keys[0]);

      if (newParentInfo) {
        queryClient.invalidateQueries([
          GET_CHILDRENS,
          newParentInfo?.parentId.toString(),
        ]);

        if (searchParams.get("nodeId"))
          queryClient.invalidateQueries([
            GET_NODE_ATTRIBUTES_DETAILS,
            searchParams.get("nodeId"),
          ]);

        dispatch(
          setPerformTrash({
            treeNode: newParentInfo?.parentId,
          })
        );
        queryClient.invalidateQueries([GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID]);
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);

        notification.success({
          message: t("Success!"),
          description: t("Node restored successfully!"),
        });

        onClose();
      }
    },
    onError: (e: any) => {
      notification.error({
        message: t("Error in restoring!"),
        description:
          t(e?.data?.details) || t("Please try again after sometime"),
      });
      captureException(e?.data?.error);
    },
  });

  const restoreAndJumpMutation = useMutation(restoreNodes, {
    onSuccess: async (originalParentId: number) => {
      const newPinned = [];

      pinned?.forEach((pin) => {
        if (selectedTrash.keys.includes(pin.id)) {
          newPinned.push({ ...pin, inTrash: false });
        } else {
          newPinned.push({ ...pin });
        }
      });

      if (newPinned.length > 0) {
        updatePinned(newPinned);
      }

      const newParentInfo = await getNodeDetails(selectedTrash.keys[0]);
      if (newParentInfo) {
        queryClient.invalidateQueries([
          GET_CHILDRENS,
          newParentInfo?.parentId.toString(),
        ]);

        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);

        dispatch(
          setPerformTrash({
            treeNode: newParentInfo?.parentId,
          })
        );
        dispatch(
          setSelected({
            keys: [newParentInfo.id],
            info: [
              {
                id: newParentInfo.id,
                parentId: newParentInfo.parentId,
                name: newParentInfo.name,
                isAsset: newParentInfo.templateId === 2,
                templateId: newParentInfo?.templateId,
                body: newParentInfo?.body,
                isLeaf: newParentInfo?.isLeaf,
                flag: newParentInfo?.flag,
              },
            ],
          })
        );
        // dispatch(setExpandedKeys([newParentInfo.id]));
        queryClient.invalidateQueries([GET_CHILDRENS, TRASHCAN_PARENT_NODE_ID]);
        queryClient.invalidateQueries([
          GET_NODE_ATTRIBUTES_DETAILS,
          searchParams.get("nodeId"),
        ]);
        notification.success({
          message: t("Success!"),
          description: t("Node restored successfully!"),
        });

        setTimeout(() => {
          navigate(
            `/details/${originalParentId}?nodeId=${selectedTrash.keys[0]}`
          );
        }, 500);

        onClose();
      }
    },
    onError: (e: any) => {
      notification.error({
        message: t("Error in restoring!"),
        description: t(e?.data?.error) || t("Please try again after sometime"),
      });
      captureException(e?.data?.error);
    },
  });

  const updatePinned = (newPinned) => {
    pinnedMutation.mutateAsync({
      value: {
        ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
        pinned: newPinned,
      },
    });
    dispatch(setPinned([...newPinned]));
  };

  const handleRestore = () => {
    restoreMutation.mutate(selectedTrash.keys);
  };

  const handleRestoreAndJump = () => {
    restoreAndJumpMutation.mutateAsync(selectedTrash.keys);
  };

  let templateIcon = null;
  if (!isMultiple) {
    templateIcon = templatesData[Number(templateId)]?.icon || "_30_folder";
  }

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      centered
      className="export-modal"
      title={
        isMultiple ? `${t("Restore Selected")}?` : `${t("Restore")} ${label}?`
      }
      width="50%"
    >
      <Wrapper>
        {!isMultiple && (
          <p>
            {getAttributeIcon(templateIcon)} {label}
          </p>
        )}

        <h5>
          {isMultiple ? (
            t("Are you sure you want to restore these selected items?")
          ) : (
            <>
              {t("Are you sure you want to restore the item")}?
              {!isLeaf && (
                <span>
                  {t("Be minded that the entire branch will be moved")}
                </span>
              )}
            </>
          )}
        </h5>

        <p className="path">
          {t("Original path:")}{" "}
          <span>{transformObjectPath(nodeDetails?.data?.pathName, false)}</span>
        </p>
        <div className="flex">
          {!isMultiple && (
            <Button
              onClick={handleRestoreAndJump}
              type="primary"
              loading={restoreAndJumpMutation.isLoading}
            >
              {t("Restore and jump")}
            </Button>
          )}

          <Button
            onClick={handleRestore}
            type="primary"
            ref={ref}
            loading={restoreMutation.isLoading}
          >
            {isMultiple ? t("Restore Selected") : t("Restore")}
          </Button>
        </div>
      </Wrapper>
    </Modal>
  );
};

const RestoreNodeModal = withErrorBoundary(
  React.memo(RestoreNodeModalBase),
  "error.generic"
);

export { RestoreNodeModal };

interface Props {
  onClose: () => void;
  isOpen: boolean;
  isMultiple?: boolean;
  trashData: any;
  setTrashData: any;
  action: {
    id: string;
    label: string;
    templateId?: number;
    isLeaf: boolean;
  };
}

const Wrapper = styled.form`
  padding-bottom: 20px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;

  & .flex {
    display: flex;
    justify-content: right;
    gap: 10px;
    width: 100%;
  }

  & .path {
    margin-top: 10px;
    color: grey;
    font-size: 13px;
    gap: 5px;

    & span {
      color: #4277a2;
    }
  }

  & img {
    object-fit: contain;
  }

  & h5 {
    font-size: 13px;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 6px;

    & span {
      color: #949494;
      font-size: 12px;
      display: block;
    }
  }

  & p {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #4277a2;

    & svg {
      width: 20px;
      height: 20px;
    }
  }
  & button {
    display: flex;
    margin-top: 20px;
    font-size: 13px;
  }
`;
