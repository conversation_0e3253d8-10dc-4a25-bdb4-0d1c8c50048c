import React, { useEffect, useRef } from "react";
import { Tree } from "antd";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import { handleMenuCreatorDrag } from "../../../utils";
import { updateDropDownForMultipleSelect } from "../../../utils/functions/menuCreator";
import { useParentHeight } from "../../../utils/functions/customHooks";
import { useTranslation } from "react-i18next";
import { MenuCreatorLabel } from "../MenuCreatorLabel";
import { useDispatch } from "react-redux";
import { setMask } from "../../../store/features";

type Props = {
  treeData: any[];
  defaultExpanded: string[];
  setDefaultExpanded: any;
  setTreeData: any;
  handleActionsSelect: any;
  setSelected: any;
  selectedKeys: any;
  dropdownOpen: boolean;
  setDropdownOpen: any;
};

const MenuCreatorTree = ({
  treeData,
  defaultExpanded,
  setDefaultExpanded,
  setTreeData,
  handleActionsSelect,
  setSelected,
  selectedKeys,
  setDropdownOpen,
  dropdownOpen,
}: Props) => {
  const isOpenRef = useRef(dropdownOpen);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  useEffect(() => {
    isOpenRef.current = dropdownOpen;
  });

  const handleExpand = (info, event) => {
    // Use closest() to support clicks on SVG children
    const iconEl = event.nativeEvent.target.closest
      ? event.nativeEvent.target.closest(".ant-tree-switcher-icon")
      : null;
    const isExpandIconClicked = !!iconEl;

    if (!isExpandIconClicked) {
      return;
    }
    setDefaultExpanded([...info]);
  };

  const handleSelect = (keys: string[], info) => {
    if (!dropdownOpen) {
      const selectedKeys = [];
      info?.selectedNodes.forEach((item) => {
        selectedKeys.push({
          id: item.key,
          parentId: item.parentId,
          name: item.name,
          type: item.type,
          isNew: item.id === 0,
        });
      });
      setSelected({ keys: [...keys], info: [...selectedKeys] });
      const data = updateDropDownForMultipleSelect(
        keys,
        treeData,
        handleActionsSelect,
        setDropdownOpen
      );
      setTreeData([...data]);
    }
  };
  return (
    <div ref={containerRef} className="tree-container">
      <Tree.DirectoryTree
        multiple
        showLine
        showIcon
        draggable
        virtual
        height={containerHeight - 15}
        selectedKeys={(selectedKeys?.keys as any) || []}
        onSelect={handleSelect}
        blockNode
        expandedKeys={[...defaultExpanded]}
        switcherIcon={React.useCallback(
          (val) => (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          ),
          []
        )}
        onDragStart={(e) => {
          if (selectedKeys?.keys?.indexOf(e.node.id) === -1) {
            setSelected({
              keys: [e.node.key],
              info: [
                {
                  ...e.node,
                  isNew: e.node?.id === 0,
                },
              ],
            });
          }
        }}
        treeData={treeData}
        onExpand={(info, event) => {
          if (!isOpenRef.current) {
            handleExpand(info, event);
          }
        }}
        onDrop={(e) => {
          const hasNoErrors = handleMenuCreatorDrag(
            selectedKeys,
            e,
            treeData,
            setTreeData,
            handleActionsSelect,
            t,
            defaultExpanded,
            setDefaultExpanded
          );
          if (hasNoErrors) {
            dispatch(setMask(true));
          }
        }}
        titleRender={(node) => (
          <MenuCreatorLabel
            handleActionsSelect={handleActionsSelect}
            id={node?.key}
            name={node?.name}
            parentId={node?.parentId}
            type={node?.type}
            childrens={node?.children}
            hasAllowedChildren={node?.hasAllowedChildren}
            isMultiple={node?.isMultiple}
            count={node?.children?.length}
            selected={selectedKeys}
            setSelected={setSelected}
            isNew={node?.id === 0}
          />
        )}
      />
    </div>
  );
};

export { MenuCreatorTree };
