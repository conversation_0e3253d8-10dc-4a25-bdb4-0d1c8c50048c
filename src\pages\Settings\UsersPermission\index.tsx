import { styled } from "@linaria/react";
import React, { useState, useMemo, useEffect } from "react";
import { Flex, Tabs } from "antd";
import { isEqual } from "lodash";
import { But<PERSON> } from "primereact/button";
import { BreadCrumb, MyTable, SimpleAttributeValue } from "../../../components";
import AddUserDialog from "./AddUserDialog";
import DeleteDialog from "./DeleteDialog";
import ConvertUserDialog from "./ConvertUserDialog";
import AddGroupDialog from "./AddGroupDialog";
import AddToGroupDialog from "./AddToGroupDialog";
import {
  useHyperlinkActions,
  useNotification,
  useParentHeight,
} from "../../../utils/functions/customHooks";
import { useTranslation } from "react-i18next";
import { INodeDetails, ILocalSettings, IHyperlinks } from "../../../interfaces";
import { useQuery, useQueryClient, useMutation } from "react-query";
import {
  getPermissionUsers,
  deletePermissionUser,
  convertToAuthor,
  convertToReader,
  deleteNodeService,
  editNodeService,
  addAttributes,
} from "../../../services/node";
import { saveLocalSettings } from "../../../services/settings";
import {
  AUTHOR_USERS_GROUPS_TEMPLATE_ID,
  AUTHOR_USERS_TEMPLATE_ID,
  GET_ALL_PERMISSION_USERS,
  GET_LOCAL_SETTINGS_KEY,
  READER_USERS_GROUPS_TEMPLATE_ID,
  READER_USERS_TEMPLATE_ID,
  PERMISSIONS_NODE_ID,
  GET_CHILDRENS,
} from "../../../constants";
import { useTheme } from "../../../utils";
import { ColDef } from "ag-grid-community";
import { Container, Wrapper } from "../GeneralSettings/styles";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { setMask } from "../../../store/features";
import { AgGridReact } from "ag-grid-react";
import { withErrorBoundary } from "../../../components/withErrorBoundary";
import { Link } from "react-router-dom";

const LinkWrapper = styled.div<{ theme: any }>`
  > a {
    text-decoration: none;
    font-size: 13px;
    cursor: pointer;
    color: ${({ theme }) => theme.colorTextDisabled};

    &:hover {
      text-decoration: underline;
    }
  }
`;

const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  gap: 10px;
  padding-bottom: 5px;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 12px;
`;

const TableWrapper = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100% - 80px);

  & .ag-cell-edit-wrapper,
  & .ag-cell-inline-editing {
    min-height: 30px;
    max-height: 40px;
    padding: 2px 7px;
    margin: 0;
  }
`;

const StyledTabs = styled(Tabs)`
  flex: 1;
  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .ant-tabs-nav::before {
    border-bottom: none;
  }
`;

enum ActionButtons {
  NEW = "new",
  DOWNLOAD = "download",
  DELETE = "delete",
  ADD_TO_GROUP = "add to group",
  CONVERT_TO_AUTHOR = "convert to author",
}

enum TabItem {
  READERS = "readers",
  AUTHORS = "authors",
  GROUP_OF_READERS = "group of readers",
  GROUP_OF_AUTHORS = "group of authors",
}

const TAB_ITEMS = [
  {
    key: TabItem.READERS,
    label: "Readers",
    templateId: READER_USERS_TEMPLATE_ID,
  },
  {
    key: TabItem.AUTHORS,
    label: "Authors",
    templateId: AUTHOR_USERS_TEMPLATE_ID,
  },
  {
    key: TabItem.GROUP_OF_READERS,
    label: "Group of Readers",
    templateId: READER_USERS_GROUPS_TEMPLATE_ID,
  },
  {
    key: TabItem.GROUP_OF_AUTHORS,
    label: "Group of Authors",
    templateId: AUTHOR_USERS_GROUPS_TEMPLATE_ID,
  },
];

// Readers and Authors Group attributes
const ATTRIBUTE_ID_GROUP_READERS = -431; // Group attribute for readers
const ATTRIBUTE_ID_GROUP_AUTHORS = -429; // Group attribute for authors

// Source attributes for each type
const ATTRIBUTE_ID_SOURCE_READERS = -439; // Source attribute for readers
const ATTRIBUTE_ID_SOURCE_AUTHORS = -436; // Source attribute for authors
const ATTRIBUTE_ID_SOURCE_GRP_READERS = -442; // Source attribute for group of readers
const ATTRIBUTE_ID_SOURCE_GRP_AUTHORS = -443; // Source attribute for group of authors

// Description attributes for groups
const ATTRIBUTE_ID_DESCRIPTION_READERS = -407; // Description for group of readers
const ATTRIBUTE_ID_DESCRIPTION_AUTHORS = -427; // Description for group of authors

const processUserDataForTable = (
  nodes: INodeDetails[],
  activeTabKey: TabItem,
  t: (key: string) => string,
  handleHyperlinkAction: (hyperlink: IHyperlinks) => void,
  theme: any
) => {
  // Always define columns for the current tab, even if there is no data
  const isGroupTab =
    activeTabKey === TabItem.GROUP_OF_READERS ||
    activeTabKey === TabItem.GROUP_OF_AUTHORS;

  const getGroupAttributeId = () => {
    return activeTabKey === TabItem.READERS
      ? ATTRIBUTE_ID_GROUP_READERS
      : ATTRIBUTE_ID_GROUP_AUTHORS;
  };

  const userColumns: ColDef[] = [
    {
      headerName: t("Person"),
      field: "nodeName",
      minWidth: 150,

      cellRenderer: ({ data }) => {
        return activeTabKey === TabItem.AUTHORS ? (
          <LinkWrapper theme={theme}>
            <Link
              to={
                data?.nodeInTrash
                  ? null
                  : `/authors/${AUTHOR_USERS_TEMPLATE_ID}?nodeId=${data.id}`
              }
              className="clickable-link"
              style={{
                color: data.nodeInTrash
                  ? theme["trashBreadcrumbsColor"]
                  : theme.colorPrimary,
              }}
            >
              {data.nodeName}
            </Link>
          </LinkWrapper>
        ) : (
          <p>{data.nodeName}</p>
        );
      },
    },
    {
      headerName: t("Group"),
      field: String(getGroupAttributeId()),
      minWidth: 120,
    },
    {
      headerName: t("Source"),
      field: String(
        activeTabKey === TabItem.READERS
          ? ATTRIBUTE_ID_SOURCE_READERS
          : ATTRIBUTE_ID_SOURCE_AUTHORS
      ),
      minWidth: 100,
      editable: false, // Ensure Source is never editable
    },
  ];

  const groupColumns: ColDef[] = [
    {
      headerName: t("Group"),
      field: "nodeName",
      minWidth: 150,
      editable: true,
      autoHeight: false,
      cellEditor: "agTextCellEditor",
      isPlainTextEditor: true,
    } as ColDef,
    {
      headerName: t("Source"),
      field: String(
        activeTabKey === TabItem.GROUP_OF_AUTHORS
          ? ATTRIBUTE_ID_SOURCE_GRP_AUTHORS
          : ATTRIBUTE_ID_SOURCE_GRP_READERS
      ),
      minWidth: 100,
      editable: false, // Ensure Source is never editable
      cellEditor: "agTextCellEditor",
      isPlainTextEditor: true,
    } as ColDef,
    {
      headerName: t("Description"),
      field: String(
        activeTabKey === TabItem.GROUP_OF_AUTHORS
          ? ATTRIBUTE_ID_DESCRIPTION_AUTHORS
          : ATTRIBUTE_ID_DESCRIPTION_READERS
      ),
      minWidth: 200,
      flex: 1,
      autoHeight: true,
      cellRenderer: (params) => {
        return (
          <SimpleAttributeValue
            attributeType="editor"
            attributeValue={params?.value || "-"}
          />
        );
      },
      editable: true,
      cellEditor: "agTextCellEditor",
      isPlainTextEditor: true,
    } as ColDef,
    {
      headerName: "",
      field: "actions",
      isEditActions: true,
    } as ColDef,
  ];

  const columns = isGroupTab ? groupColumns : userColumns;

  if (!nodes || !nodes.length) {
    // Always return columns, even if there are no rows
    return { columns, rows: [] };
  }

  // ...existing code for mapping rows...
  const rows = nodes.map((node) => {
    // Initialize row with appropriate attributes based on active tab
    const row: any = {
      id: node.id,
      nodeName: node.name,
      originalNode: node,
      nodePath: node.pathName,
      permissionType: "-",
      templateType: "-",
    };

    // Add attributes based on tab type
    if (activeTabKey === TabItem.READERS) {
      row[String(ATTRIBUTE_ID_GROUP_READERS)] = "-";
      row[String(ATTRIBUTE_ID_SOURCE_READERS)] = "-";
    } else if (activeTabKey === TabItem.AUTHORS) {
      row[String(ATTRIBUTE_ID_GROUP_AUTHORS)] = "-";
      row[String(ATTRIBUTE_ID_SOURCE_AUTHORS)] = "-";
    } else if (activeTabKey === TabItem.GROUP_OF_READERS) {
      row[String(ATTRIBUTE_ID_SOURCE_GRP_READERS)] = "-";
      row[String(ATTRIBUTE_ID_DESCRIPTION_READERS)] = "-";
    } else if (activeTabKey === TabItem.GROUP_OF_AUTHORS) {
      row[String(ATTRIBUTE_ID_SOURCE_GRP_AUTHORS)] = "-";
      row[String(ATTRIBUTE_ID_DESCRIPTION_AUTHORS)] = "-";
    }

    if (isGroupTab) {
      row.templateType =
        node.templateId === READER_USERS_GROUPS_TEMPLATE_ID
          ? "Readers Group"
          : "Authors Group";
    }
    // Set disableCheck for AUTHORS tab and permissionId -25 or -499
    if (
      (activeTabKey === TabItem.READERS || activeTabKey === TabItem.AUTHORS) &&
      (+node.id === -25 || +node.id === -499)
    ) {
      row["disableCheck"] = true;
    }

    node.body.forEach((attribute) => {
      const attributeIdStr = attribute.id.toString();

      switch (attribute.id) {
        case PERMISSIONS_NODE_ID:
          {
            const [permission] = attribute.value || [];
            row.permissionType = permission?.name || "-";
            row.permissionId = permission?.id;
          }
          break;

        // Group attributes for users
        case ATTRIBUTE_ID_GROUP_READERS:
        case ATTRIBUTE_ID_GROUP_AUTHORS: {
          // More robust handling of group attributes
          let groupValue = "-";
          if (attribute.value) {
            if (Array.isArray(attribute.value) && attribute.value.length > 0) {
              // Standard case: array of group objects with name property
              const names = attribute.value
                .filter((g) => g && typeof g === "object" && "name" in g)
                .map((g) => g.name)
                .filter(Boolean);
              if (names.length > 0) {
                groupValue = names.join(", ");
              }
            } else if (typeof attribute.value === "string") {
              // Handle case where value might be a direct string
              groupValue = attribute.value;
            } else if (typeof attribute.value === "object") {
              // If it's a single object, try to extract name
              if ("name" in attribute.value) {
                groupValue = attribute.value.name;
              } else {
                groupValue = JSON.stringify(attribute.value);
              }
            }
          }
          row[attributeIdStr] = groupValue;
          break;
        }
        // Description attributes for groups (editable)
        case ATTRIBUTE_ID_DESCRIPTION_READERS:
        case ATTRIBUTE_ID_DESCRIPTION_AUTHORS:
          // Handle both text and editor types for description
          row[attributeIdStr] = attribute.value || "-";
          break;

        // Source attributes (all types)
        case ATTRIBUTE_ID_SOURCE_READERS:
        case ATTRIBUTE_ID_SOURCE_AUTHORS:
        case ATTRIBUTE_ID_SOURCE_GRP_AUTHORS:
        case ATTRIBUTE_ID_SOURCE_GRP_READERS:
          {
            // More robust extraction of source value
            let sourceValue = "-";
            if (attribute.value) {
              if (
                typeof attribute.value === "object" &&
                !Array.isArray(attribute.value)
              ) {
                // Extract first value from object (handles {key01: "CDO.tools"} case)
                const values = Object.values(attribute.value);
                if (values.length > 0) {
                  sourceValue = String(values[0]); // Convert to string to ensure type safety
                }
              } else if (typeof attribute.value === "string") {
                // Handle direct string value
                sourceValue = attribute.value;
              }
            }

            row[attributeIdStr] = sourceValue;
          }
          break;
      }
    });
    return row;
  });

  return { columns, rows };
};

// Helper to map group nodes to dialog format
const mapGroupNodes = (nodes, sourceAttrId) =>
  (nodes || []).map((node) => {
    let source = "-";
    const sourceAttr = node.body.find((a) => a.id === sourceAttrId);
    if (sourceAttr && sourceAttr.value) {
      if (typeof sourceAttr.value === "string") {
        source = sourceAttr.value;
      } else if (typeof sourceAttr.value === "object") {
        const values = Object.values(sourceAttr.value).filter(
          (v) => typeof v === "string"
        );
        if (values.length > 0) source = values[0] as string;
      }
    }
    return {
      id: typeof node.id === "number" ? node.id : Number(node.id),
      resourceName: node.name,
      objectTemplate: source,
    };
  });

const Users2Settings: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { height, ref: parentRef } = useParentHeight(400);
  const hideAllPathNames = useSelector(
    (state: RootState) => state.breadcrumbs.hideAllpathNames
  );

  const [gridRef, setGridRef] = useState<AgGridReact>(null);

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();
  const { handleHyperlinkAction } = useHyperlinkActions();

  const [activeTabKey, setActiveTabKey] = useState<TabItem>(TAB_ITEMS[0].key);
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [convertDialogOpen, setConvertDialogOpen] = useState(false);
  const [addToGroupDialogOpen, setAddToGroupDialogOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [convertLoading, setConvertLoading] = useState(false);
  const [convertTo, setConvertTo] = useState<"author" | "reader">("author");
  // State for tracking modified group rows
  const [modifiedRows, setModifiedRows] = useState({});
  // State for Save button loading
  const [rowUpdateLoading, setRowUpdateLoading] = useState(false);

  // State for table UI changes (pinning, sorting, etc.)
  const [isTableEdited, setTableEdited] = useState(false);
  const [tableColumns, setTableColumns] = useState<ColDef[]>([]);
  const [tablePinned, setTablePinned] = useState([]);
  const [tableSort, setTableSort] = useState([]);
  const [tableFilters, setTableFilters] = useState({});
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [resetTrigger, setResetTrigger] = useState(0);

  const { data: localSettingsData } = useQuery<ILocalSettings>(
    GET_LOCAL_SETTINGS_KEY
  );

  // Query to fetch users/groups based on active tab
  const { data: rawNodes, isLoading } = useQuery(
    [GET_ALL_PERMISSION_USERS, activeTabKey],
    async () => {
      const templateId = TAB_ITEMS.find(
        (c) => c.key === activeTabKey
      ).templateId;

      const data = await getPermissionUsers(templateId);
      return data;
    },
    { staleTime: Infinity }
  );

  // Add separate queries for group of readers and group of authors
  const { data: groupOfReadersNodes = [] } = useQuery(
    [GET_ALL_PERMISSION_USERS, "group-of-readers-dialog"],
    () => getPermissionUsers(READER_USERS_GROUPS_TEMPLATE_ID),
    { staleTime: Infinity }
  );
  const { data: groupOfAuthorsNodes = [] } = useQuery(
    [GET_ALL_PERMISSION_USERS, "group-of-authors-dialog"],
    () => getPermissionUsers(AUTHOR_USERS_GROUPS_TEMPLATE_ID),
    { staleTime: Infinity }
  );

  // Store the processed rows in state to allow updating them
  const [tableRows, setTableRows] = useState([]);

  // Process the columns and rows from raw data
  const { columns: processedColumns } = useMemo(() => {
    // Get columns/rows as usual
    const { columns, rows } = processUserDataForTable(
      rawNodes,
      activeTabKey,
      t,
      handleHyperlinkAction,
      theme
    );
    // Hide "Path" column if hideAllPathNames is true
    const filteredColumns = hideAllPathNames
      ? columns.filter((col) => col.field !== "nodePath")
      : columns;

    // Update the tableRows state with the processed rows
    setTableRows(rows);

    return { columns: filteredColumns, rows };
  }, [rawNodes, activeTabKey, t, hideAllPathNames]);

  useEffect(() => {
    const tableSettings =
      localSettingsData?.body?.[0]?.value?.userSettingsTable;

    if (
      tableSettings &&
      Array.isArray(tableSettings.columns) &&
      tableSettings.columns.length > 0
    ) {
      const pinned = tableSettings.pinned || [];
      const sort = tableSettings.sort || [];
      const allColumns = tableSettings.columns.map((field) => {
        const colDef = processedColumns.find((c) => c.field === field);
        if (!colDef) return null;
        return {
          ...colDef,
          pinned: pinned.includes(field) ? "left" : null,
          sort: sort.find((s) => s.colId === field)?.sort || null,
        };
      });

      // If any column is null, fallback to processedColumns (default for tab)
      if (allColumns.some((col) => col === null)) {
        setTableColumns(processedColumns);
        setColumnsRequest(processedColumns.map((c) => c.field).filter(Boolean));
        setTablePinned([]);
        setTableSort([]);
        setTableFilters({});
      } else {
        setTableColumns(allColumns.filter(Boolean));
        setColumnsRequest(tableSettings.columns);
        setTablePinned(pinned);
        setTableSort(sort);
        setTableFilters(tableSettings.filters || {});
      }
    } else if (processedColumns.length > 0) {
      setTableColumns(processedColumns);
      setColumnsRequest(processedColumns.map((c) => c.field).filter(Boolean));
      setTablePinned([]);
      setTableSort([]);
      setTableFilters({});
    }
  }, [processedColumns, localSettingsData, resetTrigger]);

  const tableMutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      // Only invalidate and update state, do not show notification here
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      setTableEdited(false);
    },
    onError: () => {
      showErrorNotification("Unable to save table settings!");
    },
  });

  const deleteUserMutation = useMutation(deletePermissionUser, {
    onSuccess: () => {
      queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
      setDeleteDialogOpen(false);
      setSelectedRows([]);
      showSuccessNotification("Deleted");
    },
    onError: () => {
      showErrorNotification("Error in deletion!");
    },
    onSettled: () => {
      setDeleteLoading(false);
    },
  });

  const deleteMutation = useMutation(deleteNodeService, {
    onError: () => {
      showErrorNotification("Error in deletion!");
    },
  });

  const convertUserMutation = useMutation(
    async (ids: number[]) => {
      if (activeTabKey === TabItem.READERS) {
        // Convert to author
        setConvertTo("author");
        return convertToAuthor(ids);
      } else if (activeTabKey === TabItem.AUTHORS) {
        // Convert to reader (do not delete, use convert2reader endpoint)
        setConvertTo("reader");
        return convertToReader(ids);
      }
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
        queryClient.invalidateQueries(GET_CHILDRENS); // Invalidate authors tree after conversion
        setConvertDialogOpen(false);
        setSelectedRows([]);
        showSuccessNotification("Conversion successful!");
      },
      onError: () => {
        showErrorNotification("Error in conversion!");
      },
      onSettled: () => {
        setConvertLoading(false);
      },
    }
  );

  useEffect(() => {
    queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
  }, [activeTabKey]);

  // Helper: get attribute IDs for current group tab
  const getGroupAttributeIds = () => {
    if (activeTabKey === TabItem.GROUP_OF_AUTHORS) {
      // For Group of Authors tab
      return {
        desc: ATTRIBUTE_ID_DESCRIPTION_AUTHORS,
        source: ATTRIBUTE_ID_SOURCE_GRP_AUTHORS,
      };
    } else if (activeTabKey === TabItem.GROUP_OF_READERS) {
      // For Group of Readers tab
      return {
        desc: ATTRIBUTE_ID_DESCRIPTION_READERS,
        source: ATTRIBUTE_ID_SOURCE_GRP_READERS,
      };
    } else if (activeTabKey === TabItem.READERS) {
      // For Readers tab (shouldn't normally happen)
      return {
        desc: ATTRIBUTE_ID_DESCRIPTION_READERS,
        source: ATTRIBUTE_ID_SOURCE_READERS,
        group: ATTRIBUTE_ID_GROUP_READERS,
      };
    }
    // For Authors tab (shouldn't normally happen)
    return {
      desc: ATTRIBUTE_ID_DESCRIPTION_AUTHORS,
      source: ATTRIBUTE_ID_SOURCE_AUTHORS,
      group: ATTRIBUTE_ID_GROUP_AUTHORS,
    };
  };

  const handleSave = async () => {
    setRowUpdateLoading(true);
    let uiSettingsUpdated = false;
    let anyRowUpdated = false;
    const errorOccurred = false;
    const errorMessages = [];

    // Save table settings (columns, filters, etc.)
    const currentSettings = localSettingsData?.body?.[0]?.value || {};
    const updatedSettings = {
      ...currentSettings,
      userSettingsTable: {
        columns: columnsRequest,
        filters: tableFilters,
        sort: tableSort,
        pinned: tablePinned,
      },
    };
    // Only update UI settings if changed
    const prevTable = (currentSettings as any)?.userSettingsTable || {};
    const nextTable = updatedSettings.userSettingsTable || {};
    if (!isEqual(prevTable, nextTable)) {
      tableMutation.mutate({ value: updatedSettings });
      uiSettingsUpdated = true;
    }

    if (
      activeTabKey === TabItem.GROUP_OF_READERS ||
      activeTabKey === TabItem.GROUP_OF_AUTHORS
    ) {
      for (const id in modifiedRows) {
        const { oldRow, newRow, changed } = modifiedRows[id];

        // 1. Rename if needed
        if (changed.name) {
          await editNodeService({
            id: newRow.id,
            oldName: oldRow.nodeName,
            newName: newRow.nodeName,
          });
          anyRowUpdated = true;
        }

        // 2. Update attributes if needed
        if (changed.desc || changed.source) {
          const body = buildBodyForUpdate(newRow, oldRow);

          if (body.length > 0) {
            await addAttributes({ id: newRow.id, body });
            anyRowUpdated = true;
          }
        }
      }
      if (anyRowUpdated) {
        setModifiedRows({});
        setTableEdited(false);
        queryClient.invalidateQueries(GET_CHILDRENS); // Invalidate authors tree after author is updated
      }
      // Always reset mask and invalidate data if any update
      if (anyRowUpdated || uiSettingsUpdated) {
        queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
        dispatch(setMask(false));
      }
      // Show only one notification: error > group data > UI
      if (errorOccurred) {
        showErrorNotification(errorMessages.join("\n"));
      } else if (anyRowUpdated) {
        showSuccessNotification("Group updated successfully!");
      } else if (!anyRowUpdated && uiSettingsUpdated) {
        showSuccessNotification("Table settings saved successfully!");
      }
    } else {
      // Not a group tab: only UI settings
      if (errorOccurred) {
        showErrorNotification(errorMessages.join("\n"));
      } else if (uiSettingsUpdated) {
        showSuccessNotification("Table settings saved successfully!");
        queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
        dispatch(setMask(false));
      }
    }
    setRowUpdateLoading(false);
  };

  const handleCancel = () => {
    // Reset the table rows to their original state
    if (rawNodes) {
      const { rows } = processUserDataForTable(
        rawNodes,
        activeTabKey,
        t,
        handleHyperlinkAction,
        theme
      );
      setTableRows(rows);
    }

    // Reset the table UI state
    setResetTrigger((prev) => prev + 1);

    // Hide the mask overlay
    setTimeout(() => {
      dispatch(setMask(false));
      setTableEdited(false);
    }, 100);
  };

  const handleAction = (key: string) => {
    if (key === ActionButtons.NEW) setModalOpen(true);
    if (key === ActionButtons.DOWNLOAD) {
      if (!gridRef?.api) return;
      gridRef.api.exportDataAsCsv({
        onlySelected: true,
        fileName: `user-permission-${new Date().getTime()}.csv`,
      });
    }
    if (key === ActionButtons.DELETE) {
      setDeleteDialogOpen(true);
    }
    if (
      key === ActionButtons.CONVERT_TO_AUTHOR ||
      key === "convert-to-reader"
    ) {
      setConvertTo(activeTabKey === TabItem.READERS ? "author" : "reader");
      setConvertDialogOpen(true);
    }
    if (key === ActionButtons.ADD_TO_GROUP) {
      setAddToGroupDialogOpen(true);
    }
    // Download action is handled by MyTable component directly
  };

  const handleDeleteConfirm = async () => {
    setDeleteLoading(true);
    let hasAnyError = false;
    let hasAllSuccess = false;
    const ids = selectedRows.map((row) => row.id);
    if (
      activeTabKey === TabItem.GROUP_OF_READERS ||
      activeTabKey === TabItem.GROUP_OF_AUTHORS
    ) {
      try {
        for (const id of ids) {
          await deleteMutation.mutateAsync({ id });
        }

        setDeleteDialogOpen(false);
        setSelectedRows([]);
        showSuccessNotification("Group deleted successfully!");
        hasAllSuccess = true;
      } catch {
        hasAnyError = true;
        hasAllSuccess = false;
        // Error notification handled in mutation
      } finally {
        setDeleteLoading(false);
      }
    } else {
      // then permanent deletion
      deleteUserMutation.mutate(ids);
    }

    if (hasAllSuccess || hasAnyError) {
      queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
    }

    if (hasAnyError) {
      showErrorNotification("Error in deletion!");
    }
  };

  const handleConvertConfirm = () => {
    setConvertLoading(true);
    const ids = selectedRows.map((row) => row.id);
    convertUserMutation.mutate(ids);
  };

  const handleOnSuccess = () => {
    queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
    setModalOpen(false);
    const prefix =
      activeTabKey === TabItem.READERS
        ? "Reader"
        : activeTabKey === TabItem.AUTHORS
        ? "Author"
        : activeTabKey === TabItem.GROUP_OF_READERS ||
          activeTabKey === TabItem.GROUP_OF_AUTHORS
        ? "Group"
        : "";
    showSuccessNotification(`${prefix} added successfully!`);
  };

  const detectChange = () => {
    if (!mask && isTableEdited) {
      setTimeout(() => {
        dispatch(setMask(true));
      });
    }
  };

  // Handler for adding users to group (now mapping group nodes to required minimal fields, using defaults if missing)
  const handleAddToGroup = async (selectedGroups) => {
    if (!selectedRows.length || !selectedGroups.length) {
      setAddToGroupDialogOpen(false);
      return;
    }
    dispatch(setMask(true));
    try {
      // Find the full group node objects for the selected group IDs
      const allGroups = [...groupOfReadersNodes, ...groupOfAuthorsNodes];
      // Map to only the required fields for backend, using defaults if missing
      const selectedGroupNodes = selectedGroups
        .map((g) => {
          const node = allGroups.find(
            (node) => Number(node.id) === Number(g.id)
          );
          if (!node) return null;
          return {
            id: node.id,
            name: node.name,
            templateId: node.templateId,
            //bitFlag: node.bitFlag,
            //inTrash:  false,
            //pathName: node.pathName,
            //attributeLookup: null,
            //templateHasAttributes: node.templateHasAttributes ?? true,
          };
        })
        .filter(Boolean);

      const groupAttrId =
        activeTabKey === TabItem.READERS
          ? ATTRIBUTE_ID_GROUP_READERS
          : ATTRIBUTE_ID_GROUP_AUTHORS;
      const groupAttrName = "Grupy";

      for (const person of selectedRows) {
        await addAttributes({
          id: person.id,
          body: [
            {
              type: "relation",
              id: groupAttrId,
              name: groupAttrName,
              value: selectedGroupNodes,
            },
          ],
        });
      }
      showSuccessNotification(t("Groups updated successfully!"));
      // Invalidate all GET_CHILDRENS queries (including authors tree)
      queryClient.invalidateQueries(GET_CHILDRENS);
      queryClient.invalidateQueries(GET_ALL_PERMISSION_USERS);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      showErrorNotification(t("Failed to update groups!"));
    } finally {
      setAddToGroupDialogOpen(false);
      dispatch(setMask(false));
    }
  };

  // Button rendering logic
  const getVisibleActions = () => {
    // Common actions for all tabs
    const commonActions = [
      {
        key: ActionButtons.NEW,
        label: t("New"),
        isDisabled: () => false,
      },
    ];

    // Only show 'Add to group' for non-group tabs
    const addToGroupAction = {
      key: ActionButtons.ADD_TO_GROUP,
      label: t("Add to group"),
      isDisabled: (isSelected?: boolean) => !isSelected,
    };

    const lastElements = [
      // Conditionally add 'Add to group' button
      ...(activeTabKey !== TabItem.GROUP_OF_READERS &&
      activeTabKey !== TabItem.GROUP_OF_AUTHORS
        ? [addToGroupAction]
        : []),
      {
        key: ActionButtons.DOWNLOAD,
        label: t("Download"),
        isDisabled: (isSelected?: boolean) => !isSelected,
      },
    ];

    if (activeTabKey === TabItem.READERS) {
      return [
        ...commonActions,
        {
          key: ActionButtons.DELETE,
          label: t("Delete"),
          isDisabled: (isSelected?: boolean) => !isSelected,
        },
        {
          key: ActionButtons.CONVERT_TO_AUTHOR,
          label: t("Convert to author"),
          isDisabled: (isSelected?: boolean) => !isSelected,
        },
        ...lastElements,
      ];
    } else if (activeTabKey === TabItem.AUTHORS) {
      return [
        {
          key: "convert-to-reader",
          label: t("Convert to reader"),
          isDisabled: (isSelected?: boolean) => !isSelected,
        },
        ...lastElements,
      ];
    } else if (
      activeTabKey === TabItem.GROUP_OF_READERS ||
      activeTabKey === TabItem.GROUP_OF_AUTHORS
    ) {
      return [
        ...commonActions,
        {
          key: ActionButtons.DELETE,
          label: t("Delete"),
          isDisabled: (isSelected?: boolean) => !isSelected,
        },
        ...lastElements,
      ];
    }
  };

  // Helper: get attribute body for update
  const buildBodyForUpdate = (row, originalRow) => {
    const { desc, source } = getGroupAttributeIds();
    const body = [];

    // Find the attribute name from the original node data
    const getAttributeName = (attrId) => {
      const attribute = originalRow.originalNode.body.find(
        (a) => a.id === attrId
      );
      return attribute
        ? attribute.name
        : attrId === desc
        ? "Description"
        : "Source";
    };

    // Description
    if (row[desc.toString()] !== originalRow[desc.toString()]) {
      const descAttr = originalRow.originalNode.body.find((a) => a.id === desc);
      const descType = descAttr ? descAttr.type : "editor"; // Default to editor if not found
      const descName = descAttr ? descAttr.name : "Description";

      // Process the description value based on the attribute type
      let descValue = row[desc.toString()];

      // If it's an editor type and the value doesn't look like HTML, wrap it in paragraph tags
      if (
        descType === "editor" &&
        descValue &&
        typeof descValue === "string" &&
        !descValue.trim().startsWith("<")
      ) {
        descValue = `<p>${descValue}</p>`;
      }

      body.push(
        descAttr
          ? { ...descAttr, value: descValue, touched: true }
          : {
              id: desc,
              value: descValue,
              type: descType,
              name: descName,
              touched: true,
            }
      );
    }

    // Source
    if (row[source.toString()] !== originalRow[source.toString()]) {
      const sourceName = getAttributeName(source);
      const sourceAttr = originalRow.originalNode.body.find(
        (a) => a.id === source
      );

      // Handle source value format correctly - format as an object like {key01: "value"}
      let sourceValue;

      if (
        sourceAttr &&
        sourceAttr.value &&
        typeof sourceAttr.value === "object"
      ) {
        // Preserve the original key if available (e.g., "key01")
        const originalKey = Object.keys(sourceAttr.value)[0] || "key01";
        sourceValue = { [originalKey]: row[source.toString()] };
      } else {
        // Default fallback if we can't determine the original format
        sourceValue = { key01: row[source.toString()] };
      }

      body.push(
        sourceAttr
          ? { ...sourceAttr, value: sourceValue, touched: true }
          : {
              id: source,
              value: sourceValue,
              type: "select",
              name: sourceName,
              touched: true,
            }
      );
    }

    return body;
  };

  return (
    <Wrapper>
      {contextHolder}
      <BreadCrumb
        items={[]}
        extra={
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            {isTableEdited && (
              <Flex gap={10}>
                <Button
                  className="breadcrumb-button cancel-button"
                  severity="secondary"
                  size="small"
                  onClick={handleCancel}
                >
                  {t("Cancel")}
                </Button>
                <Button
                  className="breadcrumb-button save-button"
                  severity="success"
                  size="small"
                  onClick={handleSave}
                  loading={tableMutation.isLoading || rowUpdateLoading}
                >
                  {t("Save")}
                </Button>
              </Flex>
            )}
          </div>
        }
      />
      <Container
        theme={theme}
        style={{
          border: mask ? "1px solid red" : "none",
          margin: 10,
          paddingBottom: 0,
        }}
      >
        <HeaderContainer>
          <StyledTabs
            activeKey={activeTabKey}
            onChange={(key: string) => setActiveTabKey(key as TabItem)}
            items={TAB_ITEMS.map((item) => ({ ...item, label: t(item.label) }))}
          />
          <ButtonContainer>
            {getVisibleActions().map((action) => (
              <Button
                style={{ paddingTop: "5px", paddingBottom: "5px" }}
                size="small"
                disabled={action.isDisabled(selectedRows.length > 0)}
                key={action.key}
                rounded
                onClick={() => handleAction(action.key)}
                className="primary-button"
              >
                {action.label}
              </Button>
            ))}
          </ButtonContainer>
        </HeaderContainer>

        <TableWrapper ref={parentRef}>
          <MyTable
            columns={tableColumns}
            data={tableRows}
            onSelect={setSelectedRows}
            onDownload={handleAction}
            onGridReady={(grid) => {
              setGridRef(grid);

              // Force column visibility refresh - this is crucial
              // setTimeout(() => {
              //   if (grid?.api) {
              //     const allColIds = grid.api
              //       .getAllGridColumns()
              //       .map((col) => col.getColId());
              //     // Ensure all columns are visible
              //     grid.api.setColumnsVisible(allColIds, true);

              //     // Force redraw
              //     grid.api.refreshCells({ force: true });
              //     grid.api.sizeColumnsToFit();
              //   }
              // }, 200);
            }}
            editable
            preventCellClickEdit
            loading={isLoading}
            height={height - 110 + "px"}
            // Props for UI state management
            noDownload
            setPinned={setTablePinned}
            setColumnsRequest={setColumnsRequest}
            setFilters={setTableFilters}
            setSort={setTableSort}
            detectChange={() => {
              setTableEdited(true);
              detectChange();
            }}
            initialFilters={tableFilters}
            resetTrigger={resetTrigger}
            onRowsEdit={(values) => {
              // Find and update the edited row
              const editIndex = tableRows.findIndex(
                (row) => row.id === values.id
              );
              if (editIndex > -1) {
                const newRows = [...tableRows];
                const oldRow = tableRows[editIndex];
                const newRow = { ...newRows[editIndex], ...values };
                newRows[editIndex] = newRow;
                setTableRows(newRows);

                // Track changes for group tabs
                if (
                  activeTabKey === TabItem.GROUP_OF_READERS ||
                  activeTabKey === TabItem.GROUP_OF_AUTHORS
                ) {
                  const { desc, source } = getGroupAttributeIds();

                  const changed = {
                    name: newRow.nodeName !== oldRow.nodeName,
                    desc: newRow[desc.toString()] !== oldRow[desc.toString()],
                    source:
                      newRow[source.toString()] !== oldRow[source.toString()],
                  };

                  if (changed.name || changed.desc || changed.source) {
                    setModifiedRows((prev) => ({
                      ...prev,
                      [newRow.id]: { oldRow, newRow, changed },
                    }));

                    // Make sure the table is marked as edited
                    setTableEdited(true);
                    dispatch(setMask(true));
                  } else {
                    setModifiedRows((prev) => {
                      const copy = { ...prev };
                      delete copy[newRow.id];
                      return copy;
                    });
                  }
                }
              }
              // Show mask and save/cancel buttons
              setTableEdited(true);
              detectChange();
              dispatch(setMask(true));
            }}
          />
        </TableWrapper>
        {modalOpen &&
          (activeTabKey === TabItem.GROUP_OF_READERS ||
          activeTabKey === TabItem.GROUP_OF_AUTHORS ? (
            <AddGroupDialog
              visible={modalOpen}
              onCancel={() => setModalOpen(false)}
              onSuccess={handleOnSuccess}
              templateId={
                activeTabKey === TabItem.GROUP_OF_READERS
                  ? READER_USERS_GROUPS_TEMPLATE_ID
                  : AUTHOR_USERS_GROUPS_TEMPLATE_ID
              }
              parentId={-31} // Use the correct parentId as per your payload example
            />
          ) : (
            <AddUserDialog
              visible={modalOpen}
              onCancel={() => setModalOpen(false)}
              onSuccess={handleOnSuccess}
            />
          ))}
        {deleteDialogOpen && (
          <DeleteDialog
            visible={deleteDialogOpen}
            onCancel={() => setDeleteDialogOpen(false)}
            onConfirm={handleDeleteConfirm}
            loading={deleteLoading}
          />
        )}
        {convertDialogOpen && (
          <ConvertUserDialog
            visible={convertDialogOpen}
            onCancel={() => setConvertDialogOpen(false)}
            onConfirm={handleConvertConfirm}
            loading={convertLoading}
            to={convertTo}
            count={selectedRows.length}
          />
        )}
        {addToGroupDialogOpen && (
          <AddToGroupDialog
            visible={addToGroupDialogOpen}
            onCancel={() => setAddToGroupDialogOpen(false)}
            groups={(() => {
              if (activeTabKey === TabItem.READERS) {
                return mapGroupNodes(
                  groupOfReadersNodes,
                  ATTRIBUTE_ID_SOURCE_GRP_READERS
                );
              } else if (activeTabKey === TabItem.AUTHORS) {
                return [
                  ...mapGroupNodes(
                    groupOfReadersNodes,
                    ATTRIBUTE_ID_SOURCE_GRP_READERS
                  ),
                  ...mapGroupNodes(
                    groupOfAuthorsNodes,
                    ATTRIBUTE_ID_SOURCE_GRP_AUTHORS
                  ),
                ];
              }
              return [];
            })()}
            onAdd={handleAddToGroup}
          />
        )}
      </Container>
    </Wrapper>
  );
};

export default withErrorBoundary(Users2Settings, "error.generic");
