import { styled } from "@linaria/react";
import { useTheme } from "../../../utils/useTheme";
import { useQuery, useQueryClient } from "react-query";
import { getVersion } from "../../../services/version";

const Footer = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const settingsData = queryClient.getQueryData(["get-settings"]) as any;

  const versionData = useQuery<any>(["get-version"], getVersion, {
    staleTime: Infinity,
  });

  return (
    <Wrapper theme={theme} style={{ borderTop: "1px solid #eee" }}>
      <p>© ValueTank Sp. z o.o., 2018-{new Date().getFullYear()}</p>
      {!!settingsData?.body[0]?.value?.general?.footerText && (
        <p
          className="footer-text"
          style={{
            textAlign:
              settingsData?.body[0]?.value?.general?.footerTextAlignment ||
              "center",
          }}
        >
          <span
            dangerouslySetInnerHTML={{
              __html: settingsData?.body[0]?.value?.general?.footerText,
            }}
          />
        </p>
      )}

      <p>
        Version - FE:{import.meta.env.VITE_FE_VERSION} {versionData?.data}
      </p>
    </Wrapper>
  );
};

export { Footer };

const Wrapper = styled.footer<{ theme: any }>`
  padding: 8px 30px;
  gap: 10px;
  margin-top: auto;
  color: ${({ theme }) => theme.colorPrimary};
  display: flex;
  justify-content: space-between;
  background: #fff;
  align-items: center;

  /* & .footer-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  } */
  & p {
    font-size: 12px;
  }
`;
