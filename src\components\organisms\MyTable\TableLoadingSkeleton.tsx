import { memo } from "react";
import { Skeleton } from "antd";
import { styled } from "@linaria/react";

/**
 * Checkbox placeholder that looks like a skeleton
 */
const CheckboxPlaceholder = memo(function CheckboxPlaceholder() {
  return (
    <Skeleton.Button
      active
      size="small"
      style={{
        width: 16,
        height: 16,
        minWidth: 16,
      }}
    />
  );
});

/**
 * Memoized skeleton input to prevent unnecessary re-renders
 */
const SkeletonInput = memo(function SkeletonInput() {
  return (
    <Skeleton.Input
      block
      active
      size="small"
      style={{ width: "90%", height: 16 }}
    />
  );
});

/**
 * Memoized table row to prevent unnecessary re-renders
 */
const TableRow = memo(function TableRow({ rowIndex }: { rowIndex: number }) {
  const isEven = rowIndex % 2 === 1;

  return (
    <tr className={isEven ? "even-row" : ""}>
      <td className="checkbox-column">
        <CheckboxPlaceholder />
      </td>
      <td>
        <SkeletonInput />
      </td>
      <td>
        <SkeletonInput />
      </td>
      <td>
        <SkeletonInput />
      </td>
    </tr>
  );
});

/**
 * TableLoadingSkeletonComponent - A simple table skeleton that matches the AG Grid layout
 */
function TableLoadingSkeletonComponent() {
  const rowCount = 4;

  return (
    <TableWrapper>
      <table>
        <colgroup>
          <col style={{ width: "60px" }} />
          <col style={{ width: "30%" }} />
          <col style={{ width: "35%" }} />
          <col style={{ width: "35%" }} />
        </colgroup>
        <tbody>
          {Array(rowCount)
            .fill(0)
            .map((_, index) => (
              <TableRow key={`row-${index}`} rowIndex={index} />
            ))}
        </tbody>
      </table>
    </TableWrapper>
  );
}

export const TableLoadingSkeleton = memo(TableLoadingSkeletonComponent);

const TableWrapper = styled.div`
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #fff;

  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }

  th,
  td {
    padding: 8px;
    text-align: center;
    height: 30px;
    vertical-align: middle;
  }

  td {
    font-size: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .checkbox-column {
    text-align: center;
    padding-left: 20px; /* Add extra padding to match AG Grid's checkbox spacing */
  }

  .even-row {
    background-color: #fafafa;
  }
`;
