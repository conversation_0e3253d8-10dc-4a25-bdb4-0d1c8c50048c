import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "../../../components/withErrorBoundary";
import React from "react";

const HourlyBase = ({ hour, minute, onHourChange, onMinuteChange }) => {
  const { t } = useTranslation();

  return (
    <label>
      {t("Every")}
      <input
        type="number"
        value={hour}
        onChange={onHourChange}
        min="1"
        max="23"
      />
      {t("hour(s)")}
      <input type="number" value={minute} onChange={onMinuteChange} min="1" />
      {t("minute(s)")}
    </label>
  );
};

const Hourly = withErrorBoundary(React.memo(HourlyBase), "error.generic");

export { Hourly };
