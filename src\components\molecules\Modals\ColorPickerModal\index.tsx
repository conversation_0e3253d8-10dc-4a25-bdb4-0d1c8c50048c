import {
  HeartOutlined,
  HistoryOutlined,
  HomeOutlined,
  MessageOutlined,
  PlusOutlined,
  SettingOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { styled } from "@linaria/react";
import { <PERSON><PERSON>crumb, <PERSON><PERSON>, Divider } from "antd";
import React, { useEffect, useState } from "react";
import { SketchPicker } from "react-color";
import { useTheme } from "../../../../utils/useTheme";
import { AttributeItem } from "../../../atoms";
import { useTranslation } from "react-i18next";
import { DEFAULT_COLORS } from "../../../../constants";
import { StyledModal } from "../StyledModal";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

const ColorPickerModalBase = ({
  onCancel,
  open,
  colors,
  setColors,
  action,
}) => {
  const [selectedColor, setSelectedColor] = useState("");
  const theme = useTheme() as any;
  const { t } = useTranslation();

  useEffect(() => {
    setSelectedColor(colors[action] || theme[action]);
  }, [open]);

  const Background = (
    <BackgroundDiv color={selectedColor}>
      <p>{t("Background color used in homepage.")}</p>
      <div className="bg">
        <div />
      </div>
    </BackgroundDiv>
  );

  const SecondaryColor = (
    <SecondaryColorDiv color={selectedColor || "#000"}>
      <p>{t("Breadcrumbs background and relations area boder color.")}</p>
      <TopBar bg={selectedColor} color={colors.breadcrumbsColor}>
        <Breadcrumb>
          <Breadcrumb.Item>{t("Settings")}</Breadcrumb.Item>
          <Breadcrumb.Item>{t("Theme & Layout")}</Breadcrumb.Item>
        </Breadcrumb>
      </TopBar>
    </SecondaryColorDiv>
  );

  const BreadcrumbsColor = (
    <SecondaryColorDiv color={selectedColor || "#000"}>
      <p>{t("Font color used in breadcrumbs.")}</p>
      <TopBar bg={colors.colorSecondary} color={selectedColor}>
        <Breadcrumb>
          <Breadcrumb.Item>{t("Settings")}</Breadcrumb.Item>
          <Breadcrumb.Item>{t("Theme & Layout")}</Breadcrumb.Item>
        </Breadcrumb>
      </TopBar>
    </SecondaryColorDiv>
  );

  const TrashBreadcrumbsColor = (
    <SecondaryColorDiv color={colors.trashBreadcrumbsFontColor || "#000"}>
      <p>{t("Trashcan breadcrumbs background color")}</p>
      <TopBar bg={selectedColor} color={colors.trashBreadcrumbsFontColor}>
        <Breadcrumb>
          <Breadcrumb.Item>{t("Trashcan")}</Breadcrumb.Item>
          <Breadcrumb.Item>{t("Node1")}</Breadcrumb.Item>
        </Breadcrumb>
      </TopBar>
    </SecondaryColorDiv>
  );

  const TrashBreadcrumbsFontColor = (
    <SecondaryColorDiv color={selectedColor || "#000"}>
      <p>{t("Font color used in trashcan breadcrumbs.")}</p>
      <TopBar bg={colors.trashBreadcrumbsColor} color={selectedColor}>
        <Breadcrumb
          separator=">"
          items={[
            {
              title: t("Trashcan"),
            },
            {
              title: t("Node1"),
            },
          ]}
        />
      </TopBar>
    </SecondaryColorDiv>
  );

  const MetamodelBreadcrumbsColor = (
    <SecondaryColorDiv color={colors.metamodelBreadcrumbsFontColor || "#000"}>
      <p>{t("Metamodel breadcrumbs background color")}</p>
      <TopBar bg={selectedColor} color={colors.metamodelBreadcrumbsFontColor}>
        <Breadcrumb>
          <Breadcrumb.Item>{t("Metamodel")}</Breadcrumb.Item>
          <Breadcrumb.Item>{t("Node1")}</Breadcrumb.Item>
        </Breadcrumb>
      </TopBar>
    </SecondaryColorDiv>
  );

  const MetamodelBreadcrumbsFontColor = (
    <SecondaryColorDiv color={selectedColor || "#000"}>
      <p>{t("Font color used in metamodel breadcrumbs.")}</p>
      <TopBar bg={colors.metamodelBreadcrumbsColor} color={selectedColor}>
        <Breadcrumb
          separator=">"
          items={[
            {
              title: t("Metamodel"),
            },
            {
              title: t("Template"),
            },
          ]}
        />
      </TopBar>
    </SecondaryColorDiv>
  );

  const PrimaryColor = (
    <PrimaryColorDiv color={selectedColor}>
      <h1>{t("Font color.")}</h1>
      <h2>{t("Font color.")}</h2>
      <h3>{t("Font color.")}</h3>
      <h4>{t("Font color.")}</h4>
      <h5>{t("Font color.")}</h5>
      <h6>{t("Font color.")}</h6>

      <Divider />
      <div className="icons">
        <HomeOutlined />
        <UserOutlined />
        <PlusOutlined />
        <HeartOutlined />
        <SettingOutlined />
      </div>
    </PrimaryColorDiv>
  );

  const BgLight = (
    <BgLightDiv bg={selectedColor} textColor={colors.colorPrimary}>
      <p>{t("Cards and tabs background color in homepage.")}</p>
      <Divider />
      <div className="card-container">
        <div className="cards">
          <MessageOutlined />
          {t("Messages")}
        </div>
        <div className="cards">
          <HeartOutlined />
          {t("Pinned")}
        </div>

        <div className="cards">
          <HistoryOutlined />
          {t("History")}
        </div>
      </div>
    </BgLightDiv>
  );

  const BgAttributes = (
    <BgAttributesDiv bg={selectedColor} textColor={colors.colorPrimary}>
      <p>{t("Attributes background color")}</p>
      <Divider />
      <AttributeItem
        title="************"
        value={"**********"}
        readOnly
        type="text"
      />
    </BgAttributesDiv>
  );
  const getContent = () => {
    switch (action) {
      case "colorPrimary":
        return PrimaryColor;
      case "colorSecondary":
        return SecondaryColor;
      case "background":
        return Background;
      case "bgLight":
        return BgLight;
      case "bgAttributes":
        return BgAttributes;
      case "breadcrumbsColor":
        return BreadcrumbsColor;
      case "trashBreadcrumbsFontColor":
        return TrashBreadcrumbsFontColor;
      case "trashBreadcrumbsColor":
        return TrashBreadcrumbsColor;
      case "metamodelBreadcrumbsFontColor":
        return MetamodelBreadcrumbsFontColor;
      case "metamodelBreadcrumbsColor":
        return MetamodelBreadcrumbsColor;
    }
  };
  const getTitle = () => {
    switch (action) {
      case "colorPrimary":
        return "Font & Icon Colors";
      case "colorSecondary":
        return "Breadcrumbs, relations area top border";
      case "background":
        return "Homepage background";
      case "bgLight":
        return "Homepage shortcuts background";
      case "bgAttributes":
        return "Attributes background";
      case "breadcrumbsColor":
        return "Breadcrumb font color";
      case "trashBreadcrumbsFontColor":
        return "Trash breadcrumb font color";
      case "trashBreadcrumbsColor":
        return "Trash Breadcrumb Background Color";
      case "metamodelBreadcrumbsFontColor":
        return "Metamodel breadcrumb font color";
      case "metamodelBreadcrumbsColor":
        return "Metamodel Breadcrumb Background Color";
    }
  };

  const handleReset = () => {
    setColors({ ...colors, [action]: DEFAULT_COLORS[action] });
    onCancel();
  };

  const footerContent = (
    <>
      <Button onClick={handleReset}>{t("Reset to default")}</Button>
      <Button
        type="primary"
        onClick={() => {
          setColors({ ...colors, [action]: selectedColor });
          onCancel();
        }}
      >
        {t("Save")}
      </Button>
    </>
  );

  return (
    <StyledModal
      open={open}
      onCancel={onCancel}
      title={t(getTitle())}
      footerContent={footerContent}
    >
      <ColorPicker>
        <SketchPicker
          color={selectedColor}
          onChange={(value) => setSelectedColor(value.hex)}
        />
        {getContent()}
      </ColorPicker>
    </StyledModal>
  );
};

const ColorPickerModal = withErrorBoundary(
  React.memo(ColorPickerModalBase),
  "error.generic"
);

export { ColorPickerModal };

const BgAttributesDiv = styled.div<{ bg: string; textColor: string }>`
  flex: 1;

  & h6 {
    background-color: ${({ bg }) => bg} !important;
    color: ${({ textColor }) => textColor} !important;
  }
  & span {
    color: ${({ textColor }) => textColor};
  }
`;
const BgLightDiv = styled.div<{ bg: string; textColor: string }>`
  & .card-container {
    display: flex;
    gap: 10px;
    & .cards {
      display: flex;
      min-height: 100px;
      min-width: 150px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 13px;
      color: ${({ textColor }) => textColor};
      gap: 2px;
      cursor: pointer;
      border-radius: 10px;
      background: ${({ bg }) => bg};

      & span {
        font-size: 20px;
      }
    }
  }
`;
const BackgroundDiv = styled.div<{ color: string }>`
  flex: 1;

  & > div {
    background-color: ${({ color }) => color};
    height: 300px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    & > div {
      background: #fff;
      height: 38px;
      background: #fff;
      width: 80%;
      border-radius: 5px;
    }
  }
`;

const TopBar = styled.div<{ color: any; bg: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
  min-height: 32px;
  background: ${({ bg }) => bg};

  & li,
  .ant-breadcrumb-separator {
    color: ${({ color }) => color};
  }
  & li:last-child {
    color: #fff100;
    font-weight: 600;
  }
`;

const SecondaryColorDiv = styled.div`
  & p {
    margin-bottom: 10px;
  }
`;
const PrimaryColorDiv = styled.div<{ color: string }>`
  color: ${({ color }) => color};
  & .icons {
    display: flex;
    gap: 30px;
    font-size: 24px;
  }
`;

const ColorPicker = styled.div`
  display: flex;
  gap: 20px;

  & > div {
    display: flex;
    flex-direction: column;
  }

  & .sketch-picker {
    min-width: 150px;
  }
  & > div:first-child {
    flex: 2;
  }
  & > div:last-child {
    flex: 5;
  }
`;
