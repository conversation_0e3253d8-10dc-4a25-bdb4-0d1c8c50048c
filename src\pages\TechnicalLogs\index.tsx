import { PageTableSkeleton } from "../../components/atoms/SkeletonComponents";
import { <PERSON>ton, Flex } from "antd";
import { useTheme } from "../../utils/useTheme";
import { Suspense, useEffect, useState } from "react";
import { <PERSON>rapper } from "./style";
import { BreadCrumb, MyTable } from "../../components";
import { GET_LOCAL_SETTINGS_KEY } from "../../constants";
import { useTranslation } from "react-i18next";
import { useMutation, useQueryClient } from "react-query";
import { useNotification } from "../../utils/functions/customHooks";
import { ILocalSettings } from "../../interfaces";
import { saveLocalSettings } from "../../services";
import { useDispatch, useSelector } from "react-redux";
import {
  setBreadcrumb,
  setMask,
  setParentBreadcrumbs,
} from "../../store/features";
import { RootState } from "../../store";

const TechnicalLogs = () => {
  const theme = useTheme();
  const PAGE_SIZE = 50;
  const [resetTrigger, setResetTrigger] = useState(0);
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [initialColumns, setInitialColumns] = useState([]);
  const [allColumnsRequest, setAllColumnsRequest] = useState([]);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  useEffect(() => {
    dispatch(setBreadcrumb([]));
    dispatch(setParentBreadcrumbs([...breadcrumb]));

    const newData = [];
    for (let i = data.length; i < PAGE_SIZE + data.length; i++) {
      newData.push({
        key: i,
        id: i,
        date: new Date("2022-03-01 08:26:17"),
        user: "defaultUser",
        activation: "Manual",
        duration: "10ms",
        name: "Node of type [Grupy z AD] added under parent Id: [ 38067 ] ",
        status: "Done",
      });
    }

    setData([...data, ...newData]);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.technicalLogs &&
      localSettingsData?.body[0]?.value?.technicalLogs?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.technicalLogs?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.technicalLogs?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.technicalLogs.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );
      setAllColumnsRequest(
        localSettingsData?.body[0]?.value?.technicalLogs?.columns
      );
      setPinned(localSettingsData?.body[0]?.value?.technicalLogs?.pinned);
      setSort(localSettingsData?.body[0]?.value?.technicalLogs?.sort);
      setFilters(localSettingsData?.body[0]?.value?.technicalLogs?.filters);
      setInitialColumns(allColumns);
    } else {
      setInitialColumns(COLUMNS);
      setAllColumnsRequest(COLUMNS?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger]);

  const detectChange = () => {
    if (!mask) {
      dispatch(setMask(true));
    }
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setMask(false));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        technicalLogs: {
          columns: allColumnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const mask = useSelector((state: RootState) => state.sidebar.mask);

  const handleCancel = () => {
    setResetTrigger((t) => t + 1);
    setTimeout(() => {
      dispatch(setMask(false));
    }, 200);
  };

  return (
    <Suspense fallback={<PageTableSkeleton />}>
      <Wrapper theme={theme}>
        {contextHolder}
        <BreadCrumb
          extra={
            <Flex gap={10}>
              {mask && (
                <>
                  <Button
                    className="breadcrumb-button cancel-button"
                    type="primary"
                    onClick={handleCancel}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    className="breadcrumb-button save-button"
                    type="primary"
                    onClick={handleSave}
                    loading={mutation.isLoading}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </Flex>
          }
        />

        {data.length === 0 || initialColumns.length === 0 ? (
          <PageTableSkeleton />
        ) : (
          <div
            className="content"
            style={{ border: mask ? "1px solid red" : "none" }}
          >
            <MyTable
              resetTrigger={resetTrigger}
              loading={loading}
              emptyMessage="No logs"
              height={"calc(100vh - 165px)"}
              data={data}
              columns={initialColumns}
              setColumnsRequest={setAllColumnsRequest}
              detectChange={detectChange}
              excelFileName="history"
              setFilters={setFilters}
              setSort={setSort}
              initialFilters={
                localSettingsData?.body[0]?.value?.technicalLogs?.filters || {}
              }
              setPinned={setPinned}
            />
          </div>
        )}
      </Wrapper>
    </Suspense>
  );
};

export default TechnicalLogs;

const COLUMNS = [
  {
    headerName: "Name",
    field: "name",
    minWidth: 200,
    flex: 1,
  },

  {
    headerName: "Date",
    field: "date",
    minWidth: 200,
    flex: 1,
  },

  {
    headerName: "User",
    field: "user",
    minWidth: 200,
    flex: 1,
  },
  {
    headerName: "Activation",
    field: "activation",
    width: 200,
  },
  {
    headerName: "Duration",
    field: "duration",
    width: 200,
  },
  {
    headerName: "Status",
    field: "status",
    width: 200,
  },
];

const breadcrumb = [
  {
    title: "Technical Log",
    to: "/technical-log",
    mockup: true,
  },
];
