import { styled } from "@linaria/react";
import { Popconfirm, Tag, Tooltip } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTheme } from "../../../../utils/useTheme";
import { MyTable } from "../../../organisms";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { useNotification } from "../../../../utils/functions/customHooks";
import { useDispatch, useSelector } from "react-redux";
import { setBottomDrawerMask } from "../../../../store/features";
import {
  deleteDQMTestResult,
  getDQMTestResults,
  saveLocalSettings,
} from "../../../../services";
import {
  GET_DQM_TEST_DATA,
  GET_LOCAL_SETTINGS_KEY,
  GET_NODE_ATTRIBUTES_DETAILS,
} from "../../../../constants";
import { ILocalSettings, INodeDetails } from "../../../../interfaces";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import { getNodeDetails } from "../../../../services/node";
import { RootState } from "../../../../store";
import { useTranslation } from "react-i18next";
import { ErrorDetailsModal } from "./ErrorDetailsModal";

import { debounce } from "lodash";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

// Display tests in bottom drawer for DQM
const TestExecutionContainerBase = ({ displaySaveCancel, fromTrashcan }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();

  const parentRef = useRef(null);

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const [triggerChange, setTriggerChange] = useState(0);
  const [height, setHeight] = useState(0);
  const [errorDetailsOpen, setErrorDetailsOpen] = useState(null);
  const [rows, setRows] = useState([]);

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const { data: bodyDetails } = useQuery<INodeDetails, any>(
    [GET_NODE_ATTRIBUTES_DETAILS, searchParams.get("nodeId")],
    () => getNodeDetails(searchParams.get("nodeId")),
    {
      staleTime: Infinity,
    }
  );

  const { data, refetch, isLoading, isFetching, isError } = useQuery<any, any>(
    [GET_DQM_TEST_DATA, searchParams.get("nodeId")],
    () => getDQMTestResults(searchParams.get("nodeId")),
    {
      enabled: !!bodyDetails,
    }
  );
  const selectedTemplateAttributes =
    templatesData[bodyDetails?.templateId]?.attributeTemplates;

  // find alarm threshold
  const ALARM_THRESHOLD =
    bodyDetails?.body?.find(
      (attr) =>
        attr.id ===
        selectedTemplateAttributes?.find(
          (attr) => attr.attributeValueType === "ALARM_THRESHOLD"
        )?.id
    )?.value * 100;

  // find warning threshold
  const WARNING_THRESHOLD =
    bodyDetails?.body?.find(
      (attr) =>
        attr.id ===
        selectedTemplateAttributes?.find(
          (attr) => attr.attributeValueType === "WARNING_THRESHOLD"
        )?.id
    )?.value * 100;

  // find precision
  const precision = bodyDetails?.body?.find(
    (attr) => attr.name === "Precyzja"
  )?.value;

  const COLUMNS = useMemo(() => {
    return [
      {
        headerName: "Date of execution",
        field: "testTimestamp",
        flex: 1,
        minWidth: 200,
        cellRenderer: ({ data }) => {
          return dayjs(data?.testTimestamp).format("YYYY-MM-DD HH:mm");
        },
      },
      {
        headerName: "Number of errors [Lbr]",
        field: "errorRecords",
        minWidth: 200,
        flex: 1,
      },
      {
        headerName: "Number of Tested [Lpr]",
        field: "sampleRecords",
        minWidth: 200,
        flex: 1,
      },

      {
        headerName: "[Lbr / Lpr] (%)",
        field: "lbr",
        width: 130,
        cellRenderer: ({ data }) => {
          const value = (data.errorRecords / data.sampleRecords) * 100;
          const color =
            value > ALARM_THRESHOLD
              ? "error"
              : value > WARNING_THRESHOLD && value < ALARM_THRESHOLD
              ? "warning"
              : "success";

          return <Tag color={color}>{value.toFixed(precision || 2)}%</Tag>;
        },
      },
      {
        field: "actions",
        isAction: true,
        width: 100,
        headerName: "Actions",
        cellRenderer: ({ data }) => {
          if (data?.errorRecords === 0) return <></>;
          return (
            <Actions>
              <Tooltip title={t("View Details")}>
                <div
                  className="details"
                  onClick={() => setErrorDetailsOpen(data?.resultId)}
                >
                  <i className="pi pi-eye" />
                </div>
              </Tooltip>
              <Popconfirm
                title={t("Delete?")}
                description={t("Are you sure to delete this record?")}
                onConfirm={() => deleteMutation.mutate(data?.resultId)}
                okText={t("Yes")}
                cancelText={t("No")}
              >
                <Tooltip title={t("Delete")}>
                  <div className="delete">
                    <i className="pi pi-trash" />
                  </div>
                </Tooltip>
              </Popconfirm>
            </Actions>
          );
        },
      },
    ];
  }, [ALARM_THRESHOLD, WARNING_THRESHOLD, precision, data]);

  const [columns, setColumns] = useState(COLUMNS);

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  // fetching table columns from blackbox
  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.testExecution &&
      localSettingsData?.body[0]?.value?.testExecution?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.testExecution?.pinned || [];
      const sort = localSettingsData?.body[0]?.value?.testExecution?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.testExecution.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field === column);
          allColumns.push({
            ...COLUMNS[index],
            pinned: pinned?.includes(column) ? "left" : null,
            sort: sort?.find((val) => val.colId === column)?.sort || null,
          });
        }
      );

      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData]);

  const handleCancel = () => {
    setTimeout(() => {
      setTriggerChange((prev) => (!prev ? 1 : prev + 1));
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        testExecution: {
          columns: newColumns,
          pinned: pinned,
          filters: filters,
          sort: sort,
        },
      },
    };
    mutation.mutate(request);
  };

  // delete test result
  const deleteMutation = useMutation(deleteDQMTestResult, {
    onSuccess: () => {
      refetch();
      showSuccessNotification("Record deleted successfully!");
    },
    onError: () => {
      showErrorNotification("Error in deletion!");
    },
  });

  useEffect(() => {
    if (!data) {
      return;
    }

    setRows(
      data?.map((record, index) => {
        return { ...record, id: index };
      })
    );
  }, [data, isFetching]);

  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <Wrapper
      theme={theme}
      ref={parentRef}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}
      <MyTable
        excelFileName="tests"
        data={rows}
        getRowClass={({ data }) => {
          if (searchParams.get("testId") == data?.resultId) {
            return "highlight-row";
          }
        }}
        columns={columns}
        resetTrigger={triggerChange}
        detectChange={detectChange}
        displaySaveCancel={displaySaveCancel}
        onCancelClick={handleCancel}
        saveLoading={mutation.isLoading}
        onSaveClick={handleSave}
        height={`${height - 50}px`}
        isError={isError}
        loading={isLoading || isFetching}
        initialFilters={
          localSettingsData?.body[0]?.value?.testExecution?.filters || {}
        }
      />
      {!!errorDetailsOpen && (
        <ErrorDetailsModal
          visible={!!errorDetailsOpen}
          id={errorDetailsOpen}
          onClose={() => setErrorDetailsOpen(null)}
        />
      )}
    </Wrapper>
  );
};

const TestExecutionContainer = withErrorBoundary(
  React.memo(TestExecutionContainerBase),
  "error.generic"
);

export { TestExecutionContainer };

const Actions = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  align-items: center;

  & > div {
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }

  & .pi-trash {
    color: #ff5151;
  }
  & i {
    color: #6c6c6c;
    font-size: 15px;
  }
`;
const Wrapper = styled.div<{ theme: any }>`
  height: 100%;
  overflow: hidden;
  display: flex;
  width: 100%;
  padding: 10px;

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & .ant-float-btn {
    margin-bottom: 12px;
    right: 10px;
  }
  & .ant-avatar {
    border-radius: 10px;
  }

  & .attachment {
    text-align: left;
    color: ${({ theme }) => theme.colorPrimary};
    & span {
      font-size: 15px;
      margin-right: 7px;
    }
  }
`;
