import { Tooltip } from "antd";
import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

// This component is used to create a tooltip with delay
interface ITooltip {
  title: string;
  children: ReactNode;
  placement?:
    | "top"
    | "left"
    | "right"
    | "bottom"
    | "topLeft"
    | "topRight"
    | "bottomLeft"
    | "bottomRight"
    | "leftTop"
    | "leftBottom"
    | "rightTop"
    | "rightBottom";
}

const MyTooltip = ({ title, children, placement }: ITooltip) => {
  const { t } = useTranslation();

  return (
    <Tooltip title={t(title)} mouseEnterDelay={0.7} placement={placement}>
      {children}
    </Tooltip>
  );
};

export { MyTooltip };
