import { styled } from "@linaria/react";
import { memo, useRef } from "react";
import { useQuery } from "react-query";
import { GET_NODE_ATTRIBUTES_DETAILS } from "../../../../constants";
import { SetPermissions } from "../../../organisms";
import { useTranslation } from "react-i18next";
import { getNodeDetails } from "../../../../services/node";
import { withErrorBoundary } from "../../../../components/withErrorBoundary";

const PermissionContainerBase = ({ id, fromTrashcan, displaySaveCancel }) => {
  const { t } = useTranslation();

  const parentRef = useRef(null);

  const { data: bodyData } = useQuery([GET_NODE_ATTRIBUTES_DETAILS, id], () =>
    getNodeDetails(id)
  );

  return (
    <Wrapper
      ref={parentRef}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {bodyData &&
        bodyData?.permissionsId !== 0 &&
        (Number(bodyData?.id) === Number(bodyData?.permissionsId) ? (
          <p className="info">{t("Permissions set for this Asset")}</p>
        ) : (
          <p className="info" style={{ color: "red" }}>
            {t("Permissions inherited")}
          </p>
        ))}

      <SetPermissions
        displaySaveCancel={displaySaveCancel}
        fromTrashcan={fromTrashcan}
        id={id}
      />
    </Wrapper>
  );
};
const PermissionContainer = withErrorBoundary(
  memo(PermissionContainerBase),
  "error.generic"
);

export { PermissionContainer };

const Wrapper = styled.div`
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  padding: 10px;

  & .info {
    color: var(--color-text);
    line-height: 40px;
    font-size: 13px;
  }

  & .no-border {
    border-bottom: none !important;
  }

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
    height: 100%;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & a:hover {
    text-decoration: underline;
  }
  & td {
    cursor: default !important;
  }

  & .title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    text-align: left;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }
  & a:hover {
    color: #094f8b;
  }
`;
