import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WorkspaceContainer } from ".";

const meta: Meta<typeof WorkspaceContainer> = {
  title: "Components/Organisms/WorkspaceContainer",
  component: WorkspaceContainer,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof WorkspaceContainer>;

export const Basic: Story = {
  args: {
    setEditingAttribute: () => null,
  },
};
