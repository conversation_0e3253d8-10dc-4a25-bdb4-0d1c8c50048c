import { <PERSON><PERSON>, <PERSON>a, <PERSON>s } from "@storybook/blocks";

import * as Stories from "./DeleteNodeModal.stories";

<Meta of={Stories} />

# Delete Node Modal

This component is used in following two cases:

- Deleting node from Sidebar
- Deleting nodes from MenuCreator

This modal supports both single delete as well as multiple delete option.

<Canvas of={Stories.Basic} />

## Criteria

- User cannot delete `asset` type node.
- In case of multiple delete, if any one of the selected node is of `asset` type, user shouldnot be allowed to perform delete operation.

```
  const checkForAssetNode = () => {
    let hasAssetNode = false;
    selectedItems.forEach((item) => {
      if (item.isAsset) {
        hasAssetNode = true;
      }
    });
    return hasAssetNode;
  };
```

## Flow

<ol>
  <li>Check if the deleted node is `asset` type or not</li>
  <li>If not, perform delete API call </li>
  <li>Once delete is successfull, update tree data state </li>
  <li>Also, update query cache data </li>

</ol>

```
const allData = [...treeData];
handleRecursiveDelete(allData, actionId);
setTreeData([...allData]);
const previousData = queryClient.getQueryData(["get-nodes", nodeId]);
handleRecursiveQueryClientDelete(previousData, actionId);
queryClient.setQueryData(["get-nodes", nodeId], previousData);

```

<Controls />
