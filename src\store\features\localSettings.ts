import { createSlice } from "@reduxjs/toolkit";

export interface LocalSettingsState {
  application_name: string;
  logo: string;
  homeLayoutType: "kanban" | "list";
  sections: string[];
  hasShortcut: boolean;
  showSearchHistory: boolean;
  showLogo: boolean;
  showDescription: boolean;
  footerText: string;
  footerTextAlignment: string;
  displayFooterText: boolean;
  customTheme: any;
  homeVersion: string;
}

const initialState: LocalSettingsState = {
  application_name: "CDO.tools",
  logo: "",
  homeLayoutType: "kanban",
  sections: [],
  hasShortcut: false,
  showSearchHistory: false,
  showLogo: false,
  showDescription: false,
  footerText: "",
  footerTextAlignment: "center",
  displayFooterText: false,
  customTheme: null,
  homeVersion: "v2",
};

export const localSettingsSlice = createSlice({
  name: "localSettings",
  initialState,
  reducers: {
    setShowDescription: (state, action) => {
      state.showDescription = action.payload;
    },
    setCustomTheme: (state, action) => {
      state.customTheme = action.payload;
    },
    setApplicationName: (state, action) => {
      state.application_name = action.payload;
    },
    setHomeVersion: (state, action) => {
      state.homeVersion = action.payload;
    },
    setLogo: (state, action) => {
      state.logo = action.payload;
    },
    setHomeLayout: (state, action) => {
      state.homeLayoutType = action.payload;
    },
    setHasShortcut: (state, action) => {
      state.hasShortcut = action.payload;
    },
    setShowLogo: (state, action) => {
      state.showLogo = action.payload;
    },
    setSections: (state, action) => {
      state.sections = action.payload;
    },
    addSection: (state, action) => {
      state.sections.push(action.payload);
    },
    removeSection: (state, action) => {
      const sections = state.sections.filter(
        (item: string) => item !== action.payload
      );
      state.sections = sections;
    },
  },
});

export const {
  setCustomTheme,
  setHomeVersion,
  setHasShortcut,
  setSections,
  addSection,
  removeSection,
  setHomeLayout,
  setShowLogo,
  setLogo,
  setApplicationName,
  setShowDescription,
} = localSettingsSlice.actions;

export default localSettingsSlice.reducer;
