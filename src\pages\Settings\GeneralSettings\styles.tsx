import { styled } from "@linaria/react";

export const Card = styled.div<{ theme: any }>`
  display: flex;
  gap: 12px;
  align-items: center;

  & .quill {
    max-width: unset;
  }
  & .footer-text-wrapper {
    margin-top: 20px;
    & h5 {
      margin-top: 20px;
    }

    & .ant-select {
      min-width: 200px;
    }
  }
  & h5 {
    color: ${({ theme }) => theme?.colorPrimary};
    font-size: 15px;
    font-weight: 500;
    width: fit-content;
  }

  & .content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
    margin-left: 20px;
  }

  .tox {
    height: 150px !important;
  }

  & .left-div {
    display: flex;
    align-items: baseline;
    gap: 10px;
  }
  & p {
    font-size: 15px;
  }

  & .flex {
    display: flex;
    gap: 20px;
    align-items: center;
    & p {
      font-size: 13px;
    }
  }
`;

export const Container = styled.div<{ theme: any }>`
  background: #fff;
  padding: 16px 20px;
  margin: 20px;
  border-radius: 10px;
  flex: 1;
  overflow-y: auto;
  position: relative;

  & .ant-upload-wrapper {
    margin-top: 10px;
  }
  & .ant-upload-select {
    width: 100% !important;
  }

  & .mask {
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
  }

  & .edit {
    color: ${({ theme }) => theme?.colorPrimary};
    cursor: pointer;
  }

  & h4 {
    color: ${({ theme }) => theme?.colorPrimary};
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
    width: fit-content;
  }
`;

export const Wrapper = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  & .breadcrumb-button {
    font-size: 13px;
    height: 24px;
    padding: 0px 15px;
    border-radius: 3px;
  }

  & > div:first-child {
    position: sticky;
    top: 0px;
    z-index: 1;
  }
`;
