import { IAttachments } from "../interfaces";
import { API } from "../utils/api";

export const uploadAttachment = (payload) => {
  return API.post(`/file/upload`, payload, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const getAttachment = (id) => {
  return API.get(`/file/get/${id}`);
};

export const getAttachmentsByNodeId = (id): Promise<IAttachments[]> => {
  return API.get(`/file/info/list/node/${id}/get`);
};

export const getAttachmentContent = (id): Promise<IAttachments> => {
  return API.get(`/file/content/${id}/get`);
};

export const getFileList = (type): Promise<IAttachments[]> => {
  return API.get(`/file/content/list/${type}/get`);
};

export const deleteAttachment = (id) => {
  return API.delete(`/file/${id}/delete`);
};

export const downloadAttachment = (id) => {
  return API.get(`/file/content/${id}/download`);
};

export const getFileContent = (id) => {
  return API.get(`/file/content/${id}/get`);
};
