import { INodeDetails, IRelations } from "../interfaces";
import { API } from "../utils/api";

export const getNodeRelations = (id: string): Promise<IRelations[]> => {
  return API.get(`/model/node/${id}/related-nodes/get?permissions=FALSE`);
};

export const getAssets = (id: string): Promise<INodeDetails[]> => {
  return API.post(`/model/node/get`, {
    templateIds: [id],
    addBody: false,
  });
};
