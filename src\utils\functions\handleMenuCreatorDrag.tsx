import { notification } from "antd";
import type { DataNode } from "antd/es/tree";
import { checkForUniqueness } from "./checkForUniqueness";

// used while dragging nodes in menu creator
let newParentNodeType = null;

const getNewParentNodeType = (
  data,
  parentId: number,
  newNodeMenuType: string
) => {
  const BreakException = {};
  try {
    data.forEach((item) => {
      if (item.key === parentId) {
        const itemType = item.type;

        if (itemType === "asset") {
          newParentNodeType = "self-asset";
          return newParentNodeType;
        } else if (item.children && item.children.length > 0) {
          newParentNodeType = item.children[0]?.type;
          return newParentNodeType;
        }
        throw BreakException;
      }
      if (!newParentNodeType && item?.children?.length > 0) {
        getNewParentNodeType(item.children, parentId, newNodeMenuType);
      }
    });
  } catch (e) {
    if (e !== BreakException) throw e;
  }
  return newParentNodeType;
};

const updateParentDropdown = (data, parentId: number, handleActionsSelect) => {
  const BreakException = {};
  try {
    data.forEach((item) => {
      if (item.key === parentId) {
        throw BreakException;
      }
      if (item?.children?.length > 0) {
        updateParentDropdown(item.children, parentId, handleActionsSelect);
      }
    });
  } catch (e) {
    if (e !== BreakException) throw e;
  }
  return newParentNodeType;
};

const getDragNodesTypes = (nodes) => {
  const types = [];
  nodes.forEach((node) => {
    types.push(node.type);
  });
  return types;
};

export const handleMenuCreatorDrag = (
  selected,
  info,
  treeData,
  setTreeData,
  handleActionsSelect,
  t,
  defaultExpanded,
  setDefaultExpanded
) => {
  const dropKey = info.node.key;

  const selectedKeys = selected.keys;

  const dropPos = info.node.pos.split("-");
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

  const loop = (
    data: DataNode[],
    key: React.Key,
    callback: (node: DataNode, i: number, data: DataNode[]) => void
  ) => {
    for (let i = 0; i < data.length; i++) {
      if (data[i].key === key) {
        return callback(data[i], i, data);
      }
      if (data[i].children) {
        loop(data[i].children!, key, callback);
      }
    }
  };

  const data = [...treeData];
  let newParentId = 0;
  if (!info.dropToGap) {
    newParentId = info.node.key;
  } else {
    newParentId = info.node.parentId;
  }

  const dragNodesType: string[] = getDragNodesTypes(selected.info);
  if (dragNodesType.includes("asset") && newParentId === 0) {
    notification.open({
      type: "error",
      message: t("Move operation restricted!"),
      description: t(`Asset type cannot have manu as a child`),
    });
    return false;
  }

  newParentNodeType = null;
  getNewParentNodeType(data, newParentId, info.dragNode.type);
  if (newParentNodeType) {
    if (newParentNodeType === "self-asset") {
      notification.open({
        type: "error",
        message: t("Move operation restricted!"),
        description: t(`Asset type menu cannot have child menus`),
      });
      return false;
    }
    let isDifferent = false;
    dragNodesType.forEach((nodeType) => {
      if (nodeType !== newParentNodeType) {
        isDifferent = true;
      }
    });
    if (isDifferent) {
      notification.open({
        type: "error",
        message: t("Move operation restricted!"),
        description: t(`Cannot have menu and asset type on same level`),
      });
      return false;
    }
  }

  if (info.dragNode.parentId !== newParentId) {
    const isUnique = checkForUniqueness(
      info.dragNode?.name,
      info?.dragNode?.templateId,
      data,
      newParentId
    );
    if (!isUnique) {
      notification.error({
        message: t("Node with name already exists!"),
        description: t("Please try again with different name"),
      });
      return false;
    }
  }
  if (!defaultExpanded.includes(newParentId)) {
    setDefaultExpanded([...defaultExpanded, newParentId]);
  }

  // Find dragObject
  const dragObj: any[] = [];
  selectedKeys?.forEach((dragKey) => {
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj.push(item);
    });
  });

  dragObj.forEach((dragObjItem) => {
    if (dragObjItem.parentId !== newParentId) {
      dragObjItem.parentId = newParentId;
    }
  });

  // updating parent id of drag obj

  if (dropKey === dragObj[0].id) {
    return false;
  }
  if (!info.dropToGap) {
    // Drop on the content
    dragObj.forEach((dragObjItem) => {
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObjItem);
      });
    });
  } else {
    let ar: DataNode[] = [];
    let i: number;
    loop(data, dropKey, (_item, index, arr) => {
      ar = arr;
      i = index;
    });
    if (dropPosition === -1) {
      dragObj.forEach((item) => {
        ar.splice(i!, 0, item!);
      });
    } else {
      dragObj.forEach((item) => {
        ar.splice(i! + 1, 0, item!);
      });
    }
  }
  updateParentDropdown(data, newParentId, handleActionsSelect);
  if (info.dragNode.parentId !== 0) {
    updateParentDropdown(data, info.dragNode.parentId, handleActionsSelect);
  }
  setTreeData(data);
  return true;
};
