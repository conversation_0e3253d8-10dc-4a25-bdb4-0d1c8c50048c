import { Tree } from "antd";
import React, { useEffect, useState } from "react";
import { useParentHeight } from "../../../utils/functions/customHooks";
import { ReactComponent as DownIcon } from "../../../assets/mdi_caret.svg";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { withErrorBoundary } from "../../../components/withErrorBoundary";

const ListOfValuesBase = ({ val, setVal, onEdit, allowedChildren }) => {
  const [treeData, setTreeData] = useState([]);
  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );
  const [checked, setChecked] = useState([]);
  const { ref: containerRef, height: containerHeight } = useParentHeight();

  useEffect(() => {
    if (val && checked.length === 0) {
      const keys = Object.keys(val);
      const allChecked = [];
      keys?.forEach((key) => {
        if (val[key]) {
          allChecked.push(...val[key]);
        }
      });
      setChecked([...allChecked, ...allowedChildren]);
    }
  }, [val, checked, allowedChildren]);

  useEffect(() => {
    if (allowedChildren && allowedChildren?.length > 0) {
      const treeData = [];
      allowedChildren?.forEach((allowedChild) => {
        const selectedTemplate = templatesData[Number(allowedChild?.id)];
        const attributes = [];
        if (
          selectedTemplate?.attributeTemplates &&
          selectedTemplate?.attributeTemplates?.length > 0
        ) {
          selectedTemplate?.attributeTemplates?.forEach((attr) => {
            attributes.push({
              key: attr.id,
              title: attr.name,
              parentId: allowedChild?.id,
            });
          });
        }
        treeData.push({
          key: allowedChild?.id,
          title: selectedTemplate?.name,
          name: selectedTemplate?.name,
          children: attributes,
          disabled: true,
        });
      });

      setTreeData(treeData);
      setExpandedKeys(allowedChildren?.map((attr) => attr.id));
    }
  }, [allowedChildren, templatesData]);

  const [expandedKeys, setExpandedKeys] = useState([]);

  return (
    <section
      onClick={(e) => e.stopPropagation()}
      ref={containerRef}
      className="tree-container"
    >
      <Tree
        treeData={treeData}
        defaultExpandAll
        showLine
        checkable
        virtual
        height={containerHeight}
        checkedKeys={checked}
        onCheck={(checked: number[], e) => {
          const parentId = e.node?.parentId;
          if (val) {
            let newValues = null;
            const existingValues = val[parentId] ? [...val[parentId]] : [];
            if (existingValues?.includes(e.node?.key)) {
              newValues = existingValues?.filter((item) => item != e.node?.key);
            } else {
              newValues = [e.node?.key];
            }

            const updatedComposite = { ...val };
            if (newValues.length === 0) {
              delete updatedComposite[parentId];
            } else {
              updatedComposite[parentId] = val[parentId]
                ? newValues
                : [e.node?.key];
            }
            setVal({
              ...updatedComposite,
            });
            onEdit({
              ...updatedComposite,
            });
          } else {
            setVal({ [parentId]: [e.node?.key] });
            onEdit({ [parentId]: [e.node?.key] });
          }

          setChecked(e.checked ? [e.node.key] : []);
        }}
        expandedKeys={expandedKeys}
        onExpand={(expanded) => {
          setExpandedKeys(expanded);
        }}
        switcherIcon={(val) => {
          return (
            <DownIcon
              style={{
                transform: val.expanded ? `rotate(0deg)` : "rotate(-90deg)",
              }}
            />
          );
        }}
      />
    </section>
  );
};

const ListOfValues = withErrorBoundary(
  React.memo(ListOfValuesBase),
  "error.generic"
);

export { ListOfValues };
