import { ILocalSettings } from "../interfaces";
import { API } from "../utils/api";

export const getSettingsVariables = () => {
  return API.get(`/settings/variables/get`);
};

export const saveUserProfile = (value) => {
  return API.patch("/settings/profile/local/update", {
    body: [
      {
        id: "-13",
        name: "newsletter",
        type: "text",
        value: value,
      },
    ],
  });
};

export const saveGlobalSettings = (values) => {
  return API.patch("/settings/ui/global/update", {
    body: [
      {
        id: "-11",
        name: "#sys.settings.group#",
        type: "blackbox",
        value: {
          ...values.value,
        },
      },
    ],
  });
};

export const saveLocalSettings = (values) => {
  return API.patch("/settings/ui/local/update", {
    body: [
      {
        id: "-11",
        name: "#sys.user.profile#",
        type: "blackbox",
        value: {
          ...values.value,
        },
      },
    ],
  });
};

export const getSettingsDetails = () => {
  return API.get(`/settings/ui/global/get`);
};

export const getTranslationsData = () => {
  return API.get(`/settings/translation/get`);
};

export const saveTranslationsData = (values) => {
  return API.patch("/settings/translation/update", {
    body: [
      {
        id: "-11",
        name: "#sys.settings.group#",
        type: "blackbox",
        value: {
          ...values.value,
        },
      },
    ],
  });
};

export const getLocalSettingsDetails = (): Promise<ILocalSettings> => {
  return API.get(`/settings/ui/local/get`);
};
