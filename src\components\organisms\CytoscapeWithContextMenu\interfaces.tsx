import { Dispatch, SetStateAction } from "react";

export interface ICytoscape {
  elements: any[];
  displayOptions: any;
  nodeId: string;
  filterOptions: any;
  setFilterOptions: any;
  setSelectedFilters: any;
  selectedFilters: any;
  setAllTemplates: any;
  allTemplates: any;
  setSelectedTemplateId: Dispatch<SetStateAction<number | null>>;
  selectedTemplateId: any;
  fromTrashcan: boolean;
}
