import { QueryClient } from "react-query";
import { getAttributeIcon } from "../../constants";
import { INodes, ITemplates } from "../../interfaces";

// ***************************************************************
//                        Rename functions
// ***************************************************************

export const searchRecursivelyByID = (arr, itemId) =>
  arr.reduce((a, item) => {
    if (a) return a;
    if (item.key == itemId) return item;
    if (item?.children) return searchRecursivelyByID(item?.children, itemId);
  }, null);

const renameRecursively = (
  data,
  newName,
  id,
  setAction,
  setDropdownOpen,
  breadcrumb,
  setBreadcrumb
) => {
  const BreakException = {};
  try {
    data.forEach((item) => {
      if (item.key === id) {
        item.name = newName;
        const breadcrumbs = [...item.breadcrumb];
        breadcrumbs[breadcrumbs.length - 1].name = newName;
        item.breadcrumb = breadcrumbs;
        const displayedBreadcrumb = [...breadcrumb];
        const index = displayedBreadcrumb.findIndex(
          (data) => data.id === item.key
        );
        if (index !== -1) {
          displayedBreadcrumb[index].name = newName;
          setBreadcrumb(displayedBreadcrumb);
        }
        throw BreakException;
      } else if (item.children && item.children.length > 0) {
        renameRecursively(
          item.children,
          newName,
          id,
          setAction,
          setDropdownOpen,
          breadcrumb,
          setBreadcrumb
        );
      }
    });
  } catch (e) {
    if (e !== BreakException) throw e;
  }
};

const updateChildRecursively = (
  data,
  id,
  name,
  icon,
  action,
  setAction,
  setDropdownOpen
) => {
  const BreakException = {};
  try {
    if (action.fromHeader) {
      data.unshift({
        key: id,
        name: name,
        parentId: Number(action.id),
        templateId: action.templateId,
        isLeaf: true,
        children: null,
        allowedChildrens: [],
        breadcrumb: [
          // ...item.breadcrumb,
          { id: id, name: name, parentId: action.id },
        ],
        icon: getAttributeIcon(icon),
      });
    } else {
      data.forEach((item) => {
        if (item.key == action.id) {
          item.isLeaf = false;
          item.children = [
            {
              key: id,
              name: name,
              parentId: action.id,
              templateId: action.templateId,
              isLeaf: true,
              breadcrumb: [
                ...item.breadcrumb,
                { id: id, name: name, parentId: action.id },
              ],
              icon: getAttributeIcon(icon),
            },
            ...(item?.children ? item.children : []),
          ];

          throw BreakException;
        } else if (item.children && item.children.length > 0) {
          updateChildRecursively(
            item.children,
            id,
            name,
            icon,
            action,
            setAction,
            setDropdownOpen
          );
        }
      });
    }
  } catch (e) {
    if (e !== BreakException) throw e;
  }
};

const updateQueryClientChildRecursively = (data, id, name, action) => {
  const BreakException = {};
  try {
    if (action.id == "-1") {
      data.unshift({
        body: [],
        id: id,
        last: true,
        name: name,
        parentId: action.id,
        templateId: 0,
      });
    } else
      data.forEach((item) => {
        if (item.id === action.id) {
          item.last = false;
          item.children = [
            ...(item.children || []),
            {
              body: [],
              id: id,
              last: true,
              name: name,
              parentId: action.id,
              templateId: 0,
            },
          ];
          throw BreakException;
        } else if (item.children && item.children.length > 0) {
          updateQueryClientChildRecursively(item.children, id, name, action);
        }
      });
  } catch (e) {
    if (e !== BreakException) throw e;
  }
};

const updateAllowedChildrensRecursively = (
  data,
  allowedChildrens,
  id,
  setAction,
  setDropdownOpen
) => {
  const BreakException = {};
  try {
    data.forEach((item) => {
      if (item.key === id) {
        item.allowedChildrens = allowedChildrens;

        throw BreakException;
      } else if (item.children && item.children.length > 0) {
        updateAllowedChildrensRecursively(
          item.children,
          allowedChildrens,
          id,
          setAction,
          setDropdownOpen
        );
      }
    });
  } catch (e) {
    if (e !== BreakException) throw e;
  }
};

export const updateAllowedChildrens = async (
  id,
  treeData,
  setTreeData,
  action,
  setAction,
  setDropdownOpen,
  allowedChildrens
) => {
  const allTreeData = [...treeData];
  updateAllowedChildrensRecursively(
    allTreeData,
    allowedChildrens,
    action?.id,
    setAction,
    setDropdownOpen
  );
  setTreeData([...allTreeData]);
};

export const updateChildNode = async (
  id,
  name,
  icon,
  treeData,
  setTreeData,
  action,
  setAction,
  queryClient,
  nodeId,
  expandedKeys,
  setExpandedKeys,
  setDropdownOpen,
  breadcrumb,
  setBreadcrumb
) => {
  if (action.key === "rename") {
    const allTreeData = [...treeData];
    renameRecursively(
      allTreeData,
      name,
      action?.id,
      setAction,
      setDropdownOpen,
      breadcrumb,
      setBreadcrumb
    );
    setTreeData([...allTreeData]);
    const selectedNode = searchRecursivelyByID(allTreeData, action?.id);

    const previousData = queryClient.getQueryData([
      "get-nodes",
      selectedNode?.parentId.toString(),
    ]);
    if (previousData) {
      const index = previousData?.findIndex(
        (item) => item?.id == selectedNode?.key
      );
      if (index !== -1) {
        previousData[index].name = name;

        queryClient.setQueryData(
          ["get-nodes", selectedNode?.parentId.toString()],
          previousData
        );
      }
    }
  } else {
    const allTreeData = [...treeData];
    setExpandedKeys([...expandedKeys, action.id]);

    updateChildRecursively(
      allTreeData,
      id,
      name,
      icon,
      action,
      setAction,
      setDropdownOpen
    );
    setTreeData([...allTreeData]);
    const previousData = queryClient.getQueryData(["get-nodes", nodeId]);
    if (previousData) {
      updateQueryClientChildRecursively(previousData, id, name, action);
      queryClient.setQueryData(["get-nodes", nodeId], previousData);
    }
  }
};

// ***************************************************************
//                        Delete functions
// ***************************************************************

// deleting from query client response
const handleRecursiveQueryClientDelete = (data, actionId, parent?: any) => {
  const index = data?.findIndex((item) => item?.id === actionId);
  if (index !== -1) {
    data.splice(index, 1);
    if (data.length === 0 && parent) {
      parent.last = true;
    }
    return data;
  }
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      handleRecursiveQueryClientDelete(item.children, actionId, item);
    }
  });
  return data;
};

// deleting from  treedata array
const handleRecursiveDelete = (
  data,
  actionId,
  expandedKeys,
  setExpandedKeys,
  parent?: any,
  isMultiple?: boolean
) => {
  const index = data?.findIndex((item) => item?.key === actionId);
  if (index !== -1) {
    data.splice(index, 1);
    if (data.length === 0 && parent) {
      if (!isMultiple) {
        let newExpandedKeys = [...expandedKeys];
        newExpandedKeys = newExpandedKeys.filter(
          (item) => item !== parent?.key
        );
        setExpandedKeys(newExpandedKeys);
      }

      parent.isLeaf = true;
    }
    return data;
  }
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      handleRecursiveDelete(
        item.children,
        actionId,
        expandedKeys,
        setExpandedKeys,
        item
      );
    }
  });
  return data;
};

// function to call after delete API is successful
export const handleAfterDelete = (
  treeData,
  setTreeData,
  queryClient,
  nodeId,
  actionId,
  expandedKeys,
  setExpandedKeys
) => {
  const allData = [...treeData];
  handleRecursiveDelete(allData, actionId, expandedKeys, setExpandedKeys);
  setTreeData([...allData]);
  const previousData = queryClient.getQueryData(["get-nodes", nodeId]);
  handleRecursiveQueryClientDelete(previousData, actionId);
  queryClient.setQueryData(["get-nodes", nodeId], previousData);
};

// function to delete multiple nodes
export const handleAfterDeleteMultiple = (
  treeData,
  setTreeData,
  queryClient,
  nodeId,
  selected: any,
  expandedKeys,
  setExpandedKeys
) => {
  const newTreeData = [...treeData];
  let data = [];
  const isMultiple = true;

  selected.info?.forEach((item) => {
    data = handleRecursiveDelete(
      newTreeData,
      item.id,
      expandedKeys,
      setExpandedKeys,
      item,
      isMultiple
    );
  });

  setTreeData([...data]);

  let allExpanded = [...expandedKeys];
  selected.info?.forEach((item) => {
    allExpanded = allExpanded.filter((expanded) => expanded !== item.id);
  });
  setExpandedKeys(allExpanded);

  let previousData = queryClient.getQueryData(["get-nodes", nodeId]);
  selected.info?.forEach((item) => {
    previousData = handleRecursiveQueryClientDelete(previousData, item.id);
  });
  queryClient.setQueryData(["get-nodes", nodeId], previousData);
};

export const generateDataRecursively = (
  data: INodes[],
  setAction,
  setDropdownOpen,
  templates: ITemplates[],
  queryClient: QueryClient,
  parentBreadcrumb?: any[]
) => {
  const parentNodes = [];
  data?.forEach((node) => {
    const allowedChildrens =
      node?.body && node.body?.length > 0
        ? node.body.find((item: any) => item?.type === "allowedChildren")
        : [];

    let icon = "folder";
    const childrenCache = queryClient.getQueryData([
      "get-nodes",
      node?.id?.toString(),
    ]) as INodes[];

    const selectedTemplate = templates.find(
      (item) => item.id == node.templateId
    );
    if (selectedTemplate) {
      icon = selectedTemplate.icon;
    }

    const breadcrumbItem = {
      id: node?.id,
      name: node?.name,
      parentId: node?.parentId || 0,
    };
    const breadcrumb = parentBreadcrumb
      ? [...parentBreadcrumb, breadcrumbItem]
      : [breadcrumbItem];

    parentNodes.push({
      body: node?.body,
      key: node?.id,
      templateId: node?.templateId,
      allowedChildrens: allowedChildrens?.value,
      name: node?.name,
      icon: getAttributeIcon(icon),
      breadcrumb: breadcrumb,
      parentId: node?.parentId,
      isLeaf: node?.countChildren === 0,
      children:
        (childrenCache &&
          childrenCache.length > 0 &&
          generateDataRecursively(
            childrenCache,
            setAction,
            setDropdownOpen,
            templates,
            queryClient,
            breadcrumb
          )) ||
        null,
    });
  });
  return parentNodes;
};
