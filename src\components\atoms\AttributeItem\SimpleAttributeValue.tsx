import React from "react";
import { ATTRIBUTE_ICONS } from "../../../constants";
import { COMPOUND_VISUALIZATION } from "../../../constants/dropdownOptions";
import { ViewAllowedChildren } from "./ViewAllowedChildren";
import { useTranslation } from "react-i18next";
import { Switch } from "antd";
import { DropdownItems } from "./DropdownItems";
import dayjs from "dayjs";
import i18next from "i18next";
import { ExpandableText } from "../ExpandableText";
import { withErrorBoundary } from "../../withErrorBoundary";

// Extracted components
const IconAttribute = ({ value }) => {
  if (value?.startsWith("http") || value?.startsWith("data:")) {
    return <img src={value} width={22} height={22} />;
  }
  const selectedIcon = ATTRIBUTE_ICONS.find((item) => item.value === value);
  return <span className="selected-icon">{selectedIcon?.icon}</span>;
};

const MultipleSelectAttribute = ({ value }) => (
  <span onClick={(e) => e.stopPropagation()}>
    {Object.entries(value || {}).map(([templateId, name], index, arr) => (
      <React.Fragment key={templateId}>
        <span className="dropdown-items">{String(name)}</span>
        {index < arr.length - 1 && ", "}
      </React.Fragment>
    ))}
  </span>
);

// Text-based attribute types that need truncation
const TEXT_TRUNCATION_TYPES = [
  "text",
  "textarea",
  "editor",
  "sql",
  "regex",
  "iframe",
];

// Helper function to determine if a type needs text truncation
const needsTextTruncation = (type: string, value: any): boolean => {
  if (!value) return false;
  if (typeof value === "string") {
    return TEXT_TRUNCATION_TYPES.includes(type) && value.length > 50;
  }
  // For link type, check the label length
  if (type === "link" && value?.label && typeof value.label === "string") {
    return value.label.length > 30; // Shorter threshold for link labels
  }
  return false;
};

const NumberAttribute = ({ value }) => {
  const locale = i18next.language === "pl" ? "pl-PL" : "en-US";
  return <span>{new Intl.NumberFormat(locale).format(Number(value))}</span>;
};

// Main component
const SimpleAttributeValueBase = ({ attributeType, attributeValue }) => {
  const { t } = useTranslation();

  const attributeRenderers = {
    String: () =>
      COMPOUND_VISUALIZATION?.find((val) => val.value === attributeValue)
        ?.label,
    icon: withErrorBoundary(
      () => <IconAttribute value={attributeValue} />,
      "error.generic"
    ),
    select: () => (attributeValue ? Object.values(attributeValue)[0] : ""),
    multipleSelect: withErrorBoundary(
      () => <MultipleSelectAttribute value={attributeValue} />,
      "error.generic"
    ),
    multiselect: withErrorBoundary(
      () => <ViewAllowedChildren value={attributeValue} />,
      "error.generic"
    ),
    allowedChildren: withErrorBoundary(
      () => <ViewAllowedChildren value={attributeValue} />,
      "error.generic"
    ),
    allowedLinks: withErrorBoundary(
      () => <ViewAllowedChildren value={attributeValue} />,
      "error.generic"
    ),
    // Text-based types with truncation support
    editor: withErrorBoundary(
      () =>
        needsTextTruncation("editor", attributeValue) ? (
          <ExpandableText value={attributeValue} isHtml={true} />
        ) : (
          <span
            className="editor-content content"
            dangerouslySetInnerHTML={{ __html: attributeValue }}
          />
        ),
      "error.generic"
    ),
    text: () =>
      needsTextTruncation("text", attributeValue) ? (
        <ExpandableText value={attributeValue} />
      ) : (
        attributeValue
      ),
    textarea: () =>
      needsTextTruncation("textarea", attributeValue) ? (
        <ExpandableText value={attributeValue} />
      ) : (
        attributeValue
      ),
    sql: () =>
      needsTextTruncation("sql", attributeValue) ? (
        <ExpandableText value={attributeValue} />
      ) : (
        attributeValue
      ),
    regex: () =>
      needsTextTruncation("regex", attributeValue) ? (
        <ExpandableText value={attributeValue} />
      ) : (
        attributeValue
      ),
    link: () =>
      needsTextTruncation("link", attributeValue) ? (
        <a href={attributeValue?.url} target="_blank" className="active-link">
          <ExpandableText
            value={attributeValue?.label || attributeValue?.url || ""}
          />
        </a>
      ) : (
        <a href={attributeValue?.url} target="_blank" className="active-link">
          {attributeValue?.label || attributeValue?.url || ""}
        </a>
      ),
    iframe: () =>
      needsTextTruncation("iframe", attributeValue)
        ? withErrorBoundary(
            () => <ExpandableText value={attributeValue} />,
            "error.generic"
          )
        : attributeValue,
    multiplicity: () => {
      const { text1, text2 } = attributeValue || {};
      return `${text1 || ""} ${text1 && text2 ? ".." : ""} ${
        text2 || ""
      }`.trim();
    },
    switch: () => <Switch checked={attributeValue} />,
    number: () => <NumberAttribute value={attributeValue} />,
    decimal: () => <NumberAttribute value={attributeValue} />,
    timeout: () => (
      <span>
        {attributeValue?.toLocaleString()} {t("seconds")}
      </span>
    ),
    password: () => "********",
    dropdownItems: () => <DropdownItems attributeValue={attributeValue} />,
    dateTime: () => dayjs(attributeValue).format("YYYY-MM-DD HH:mm"),
    date: () => dayjs(attributeValue).format("YYYY-MM-DD"),
    externalKeys: () => "-",
  };

  const renderer = attributeRenderers[attributeType];
  if (renderer) {
    return renderer();
  }

  // Fallback: if it's a string longer than 50 characters, provide truncation
  if (typeof attributeValue === "string" && attributeValue.length > 50) {
    return <ExpandableText value={attributeValue} />;
  }

  return attributeValue;
};

export const SimpleAttributeValue = withErrorBoundary(
  React.memo(SimpleAttributeValueBase),
  "error.generic"
);
