import { DetailsContainer, MyTable } from "../../organisms";
import { useEffect, useMemo, useState } from "react";
import { Dropdown, Flex } from "antd";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import { useHyperlinkActions } from "../../../utils/functions/customHooks";
import { DELETED_FLAG, ITemplates } from "../../../interfaces";
import { useQueryClient } from "react-query";
import {
  GET_ALL_TEMPLATES_KEY,
  TEMP_GROUPING_NODE_ID,
} from "../../../constants";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

const EditAllowedChildrens = ({
  value,
  setVal,
  onEdit,
  attributeMultiplicity,
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const workingVersionActive = useSelector(
    (state: RootState) => state.mask.workingVersion
  );

  const [data, setData] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [templateOptions, setTemplateOptions] = useState([]);
  const [initialSortDone, setInitialSortDone] = useState(false);
  const [filters, setFilters] = useState({});

  const templatesData = queryClient.getQueryData(
    GET_ALL_TEMPLATES_KEY
  ) as ITemplates[];

  // Load templates only once
  useEffect(() => {
    if (templatesData) {
      const templates = [];
      templatesData?.forEach((item) => {
        if (
          item.nodeType === "DATA" &&
          item.id !== TEMP_GROUPING_NODE_ID //HARD-CODED
        ) {
          templates.push({
            name: item?.name,
            id: item?.id,
            inTrash: item?.flag?.includes(DELETED_FLAG),
          });
        }
      });
      setTemplateOptions([...templates]);
    }
  }, []);

  // Sort templates ONLY on initial load with selected items
  useEffect(() => {
    if (templateOptions.length > 0 && value?.length > 0 && !initialSortDone) {
      const selectedTemplates = templateOptions.filter((t) =>
        value.some((d) => d.id === t.id)
      );
      const unselectedTemplates = templateOptions.filter(
        (t) => !value.some((d) => d.id === t.id)
      );
      setTemplateOptions([...selectedTemplates, ...unselectedTemplates]);
      setInitialSortDone(true);
    }
  }, [templateOptions, value, initialSortDone]);

  const HYPERLINKS_ACTIONS_TRASH = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("View in trashcan"),
      key: "view-in-trashcan",
    },
  ];

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        window.open(
          `${window.origin}${baseUrl}/settings/metamodel/-1?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const columns = useMemo(() => {
    return [
      {
        headerName: "Name",
        field: "name",
        flex: 1,
        cellRenderer: ({ data: record }) => {
          return (
            <Flex vertical gap={8}>
              <Dropdown
                menu={{
                  items: record.inTrash
                    ? HYPERLINKS_ACTIONS_TRASH
                    : HYPERLINKS_ACTIONS,
                  onClick: (e) =>
                    handleNodeClick(e.key, record.id, record.name),
                }}
                trigger={["contextMenu"]}
              >
                <p
                  className={`title-container ${
                    record.inTrash ? "trash-hyperlink" : ""
                  }`}
                  onClick={async (e) => {
                    e.stopPropagation();

                    handleHyperlinkAction({
                      id: record.id,
                      inTrash: record.inTrash,
                    });
                  }}
                >
                  {record.name}
                </p>
              </Dropdown>
            </Flex>
          );
        },
      },
    ];
  }, [workingVersionActive]);

  useEffect(() => {
    setData(value);
  }, [value]);

  return (
    <Wrapper>
      <MyTable
        excelFileName="allowedchildrens"
        noHeader
        columns={columns}
        data={templateOptions}
        noDownload
        multiplicity={attributeMultiplicity}
        defaultSelected={data}
        onSelect={(selected) => {
          setVal(selected);
          onEdit(selected);
        }}
        setFilters={setFilters}
        initialFilters={filters}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};

export { EditAllowedChildrens };

const Wrapper = styled.div`
  & .title-container {
    color: var(--color-text);
  }
  & .ant-dropdown-trigger {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
`;
