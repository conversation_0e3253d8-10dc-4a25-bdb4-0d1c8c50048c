import { createSlice } from "@reduxjs/toolkit";
import { IPinned } from "../../interfaces";

export interface PinnedState {
  pinned: IPinned[];
}

const initialState: PinnedState = {
  pinned: [],
};

export const pinnedSlice = createSlice({
  name: "pinned",
  initialState,
  reducers: {
    setPinned: (state, action) => {
      state.pinned = action.payload;
    },

    addToPinned: (state, action) => {
      state.pinned.push(action.payload);
    },
    removeFromPinned: (state, action) => {
      state.pinned = state.pinned.filter(
        (pinned) => pinned.id !== action.payload
      );
    },
  },
});

export const { setPinned, addToPinned, removeFromPinned } = pinnedSlice.actions;

export default pinnedSlice.reducer;
