import { Dropdown } from "antd";
import { ReactComponent as FolderIcon } from "../../../assets/folder.svg";
import { ReactComponent as FileIcon } from "../../../assets/file.svg";
import { getDropdownListForMenuCreator1 } from "../generateTreeData";

export const useMenuCreatorGenerator = () => {
  const defaultExpanded = [];

  const generateTreeDatas = (
    data,
    PERMISSIONS: string[],
    handleActionsSelect,
    // setDefaultExpanded?: any,
    parentId?: string,
    allowedChildren?: any[]
  ) => {
    const childArray = [];
    const allowedChildrenKeys = allowedChildren || [];
    if (allowedChildrenKeys.length !== 0) {
      allowedChildrenKeys?.forEach((allowedChild) => {
        childArray.push({
          id: `${parentId}-${allowedChild?.id}`,
          key: `${parentId}-${allowedChild?.id}`,
          parentId: parentId,
          type: "asset",
          templateId: allowedChild?.id,
          permissionsId: allowedChild?.permissionsId,
          name: allowedChild?.name || "",
          title: (
            <Dropdown
              menu={{
                items: getDropdownListForMenuCreator1(
                  allowedChild?.id,
                  PERMISSIONS,
                  "asset",
                  [],
                  false
                ),
                onClick: (e) =>
                  handleActionsSelect(
                    e.key,
                    `${parentId}-${allowedChild?.id}`,
                    allowedChildren[allowedChild?.id],
                    parentId,
                    []
                  ),
              }}
              trigger={["contextMenu"]}
            >
              <p>
                <FileIcon className="folder-icon" />
                {allowedChild?.name}
              </p>
            </Dropdown>
          ),
        });
      });

      // setDefaultExpanded && setDefaultExpanded([...defaultExpanded]);
      return childArray;
    }

    data?.forEach((item) => {
      defaultExpanded.push(item.id);
      const allowedChildrens = [];
      (item?.allowedChildren || [])?.forEach((allowedChild) => {
        allowedChildrens.push({
          label: allowedChild?.name,
          value: allowedChild?.id,
        });
      });
      childArray.push({
        id: item.id,
        key: item.id,
        parentId: item?.parentId,
        type: "menu",
        templateId: item.templateId,
        name: item?.name || "",
        permissionsId: item?.permissionsId,
        characteristic: item?.characteristic || "",
        nodeTypeId: item?.nodeTypeId,
        hasAllowedChildren: (item?.allowedChildren || []).length !== 0,
        title: (
          <Dropdown
            menu={{
              items: getDropdownListForMenuCreator1(
                item?.id,
                PERMISSIONS,
                "menu",
                item?.children,
                (item?.allowedChildren || []).length !== 0
              ),
              onClick: (e) =>
                handleActionsSelect(
                  e.key,
                  item.id,
                  item.name,
                  item?.computedParentId,
                  allowedChildrens
                ),
            }}
            trigger={["contextMenu"]}
          >
            <p>
              <FolderIcon className="folder-icon" />
              {item.name}
            </p>
          </Dropdown>
        ),
        children: generateTreeDatas(
          item?.children,
          PERMISSIONS,
          handleActionsSelect,
          // setDefaultExpanded,
          item?.id,
          item?.allowedChildren
        ),
      });
    });

    // setDefaultExpanded && setDefaultExpanded([...defaultExpanded]);
    return childArray;
  };

  return { generateTreeDatas };
};
