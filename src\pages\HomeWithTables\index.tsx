import { styled } from "@linaria/react";
import { But<PERSON>, Input, Tooltip } from "antd";
import { <PERSON><PERSON><PERSON>rum<PERSON>, <PERSON><PERSON>, SearchResult } from "../../components";
import { ReactComponent as SearchIcon } from "../../assets/search_menu.svg";
import { useEffect, useState } from "react";
import { useTheme } from "../../utils/useTheme";
import LogoImage from "../../assets/images/logo.png";
import { DndContext } from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { setHomeLayout, setSections } from "../../store/features/localSettings";
import { setBreadcrumb, setParentBreadcrumbs } from "../../store/features";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_LOCAL_SETTINGS_KEY,
  GET_SEARCH_ENGINE_INFO,
  GET_SEARCH_RESULTS,
  NO_USER_ASSOCIATED,
} from "../../constants";
import { ILocalSettings, ISearchEngineInfo } from "../../interfaces";
import { saveLocalSettings } from "../../services";
import { useNotification } from "../../utils/functions/customHooks";
import { getSearchResults } from "../../services/search";
import { SortableItem } from "./SortableItem";
import { CustomizeIcon } from "./CustomizeIcon";

const HomePage = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const [iconPosition, setIconPosition] = useState(null);
  const [search, setSearch] = useState("");
  const [searchText, setSearchText] = useState("");
  const [displaySaveButton, setDisplaySaveButton] = useState(false);

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );
  const profileId = useSelector((state: RootState) => state.auth.profileId);
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const {
    sections,
    homeVersion,
    showLogo,
    logo,
    showDescription,
    application_name,
    homeLayoutType,
  } = useSelector((root: RootState) => root.localSettings);

  const { data: searchResults, isLoading } = useQuery(
    [GET_SEARCH_RESULTS, search],
    () => getSearchResults(search),
    {
      enabled: !!search,
    }
  );

  const settingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const versionInfo = queryClient.getQueryData(
    GET_SEARCH_ENGINE_INFO
  ) as ISearchEngineInfo;

  useEffect(() => {
    if (settingsData && settingsData?.body[0]?.value?.homeIconPosition) {
      setIconPosition(settingsData?.body[0]?.value?.homeIconPosition);
    } else {
      setIconPosition({ x: 0, y: 0 });
    }
  }, [settingsData]);

  useEffect(() => {
    const searchText = localStorage.getItem("home-search-text");
    if (searchText) {
      setSearchText(searchText);
      setSearch(searchText);
    }

    dispatch(setBreadcrumb([]));
    dispatch(
      setParentBreadcrumbs([
        {
          title: "Home",
          to: "/",
        },
      ])
    );
  }, []);

  function handleDragEnd(event) {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = sections.indexOf(active.id);
      const newIndex = sections.indexOf(over.id);
      const newSections = arrayMove(sections, oldIndex, newIndex);
      dispatch(setSections(newSections));
      detectChange();
    }
  }

  const handleCancel = () => {
    if (settingsData && settingsData?.body[0]?.value?.homeIconPosition) {
      setIconPosition(settingsData.body[0].value.homeIconPosition);
    } else {
      setIconPosition({ x: 0, y: 0 });
    }

    if (settingsData && settingsData?.body[0]?.value?.homeSections) {
      dispatch(setSections(settingsData.body[0].value.homeSections));
    } else {
      dispatch(setSections(["messages", "history"]));
    }

    if (settingsData && settingsData?.body[0]?.value?.homeLayoutType) {
      dispatch(setHomeLayout(settingsData.body[0].value.homeLayoutType));
    } else {
      dispatch(setHomeLayout("kanban"));
    }

    setDisplaySaveButton(false);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      setDisplaySaveButton(false);
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      setDisplaySaveButton(false);
    },
  });

  const handlePublish = () => {
    if (settingsData) {
      mutation.mutate({
        value: {
          ...(settingsData?.body ? settingsData?.body[0]?.value || {} : {}),
          homeSections: sections,
          homeLayoutType: homeLayoutType,
          homeIconPosition: iconPosition,
        },
      });
    }
  };

  const detectChange = () => {
    setDisplaySaveButton(true);
  };

  const handleSearch = () => {
    localStorage.setItem("home-search-text", searchText);
    setSearch(searchText);
  };

  return (
    <>
      {displaySaveButton && <MainMask />}
      <Wrapper
        theme={theme}
        fixFooter={homeVersion === "v2"}
        style={
          displaySaveButton ? { border: "1px solid red", zIndex: 5006 } : {}
        }
      >
        {!!homeSectionMask && <div className="mask" />}

        {contextHolder}
        <BreadCrumb
          extra={
            displaySaveButton && (
              <div className="breadcrumbs-button">
                <Button
                  type="primary"
                  onClick={handleCancel}
                  className="cancel-button"
                >
                  {t("Cancel")}
                </Button>
                <Button
                  type="primary"
                  onClick={handlePublish}
                  loading={mutation.isLoading}
                  className="save-button"
                >
                  {t("Save")}
                </Button>
              </div>
            )
          }
        />

        {profileId !== NO_USER_ASSOCIATED && (
          <>
            <SearchWrapper theme={theme}>
              <div className="logo-wrapper">
                {showLogo && <img src={logo || LogoImage} width={150} />}
                {showDescription && <p>{application_name}</p>}
              </div>

              <Input
                placeholder={t("Search")}
                value={searchText}
                onChange={(e) => {
                  setSearchText(e.target.value);
                  if (e.target.value === "") {
                    localStorage.setItem("home-search-text", "");
                    setSearch("");
                  }
                  setSearchText(e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSearch();
                  }
                }}
                allowClear
                addonAfter={
                  <Button
                    type="primary"
                    onClick={handleSearch}
                    loading={isLoading}
                  >
                    <Tooltip
                      title="Search"
                      placement="bottom"
                      mouseEnterDelay={2}
                    >
                      <SearchIcon />
                    </Tooltip>
                  </Button>
                }
              />
              {versionInfo && (
                <p className="version-info">
                  Powered by {versionInfo?.engineName}(v
                  {versionInfo?.engineVersion})
                </p>
              )}
            </SearchWrapper>

            <Content layout={homeLayoutType} theme={theme}>
              <div>
                {searchResults && (
                  <SearchResult type="table" searchResults={searchResults} />
                )}

                <DndContext onDragEnd={handleDragEnd}>
                  <SortableContext items={sections}>
                    <GridContent
                      layout={homeLayoutType}
                      sectionsCount={sections.length}
                    >
                      {sections.map((id) => (
                        <SortableItem key={id} id={id} />
                      ))}
                    </GridContent>
                  </SortableContext>
                </DndContext>
              </div>
            </Content>
          </>
        )}

        <Footer />

        {iconPosition && profileId !== NO_USER_ASSOCIATED && (
          <CustomizeIcon
            detectChange={detectChange}
            iconPosition={iconPosition}
            setIconPosition={setIconPosition}
          />
        )}
      </Wrapper>
    </>
  );
};

export default HomePage;

const MainMask = styled.div`
  background-color: #505a6240 !important;
  inset: 0;
  position: fixed;
  z-index: 5005;
`;

const GridContent = styled.div<{ layout: string; sectionsCount: number }>`
  display: flex;
  flex-wrap: wrap;

  gap: 20px;
  padding-bottom: 10px;
  & > section {
    flex: 1;
    height: 400px;
    overflow-x: auto;
  }
  & > div {
    width: ${({ sectionsCount }) => {
      // If only one section, always use full width
      if (sectionsCount === 1) {
        return "100%";
      }
      // If multiple sections, always use two columns
      return "calc(50% - 10px)";
    }};
    min-height: 300px;
  }
`;

const SearchWrapper = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-top: 20px;
  margin-bottom: 25px;
  width: 60%;
  margin-left: auto;
  margin-right: auto;

  & .logo-wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;

    & > img {
      display: flex;
      background: #fff;
      padding: 7px;
      border-radius: 5px;
      margin-left: auto;
      margin-right: auto;
      object-fit: contain;
      max-height: 40px;
    }
    & p {
      color: #084375;
    }
  }

  & .ant-input {
    height: 41px;
    box-shadow: none;
    font-family: "Poppins", sans-serif;
  }

  & .ant-input-affix-wrapper {
    padding: 0 12px;
  }
`;

const Content = styled.div<{ theme: any; layout: string }>`
  padding-bottom: 30px;
  overflow: ${({ layout }) => (layout === "kanban" ? "hidden auto " : "unset")};
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  flex: 1;

  & > div {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }

  & .header-wrapper {
    margin-top: 16px;

    & button {
      color: #fff;

      &:hover {
        color: #fff !important;
      }
    }
  }
  & h4 {
    font-weight: 500;
    cursor: move;
    color: ${({ theme }) => theme.colorPrimary};
  }
  & .ant-divider {
    margin: 10px 0px 16px 0px;
  }
`;
const Wrapper = styled.div<{ theme: any; fixFooter: boolean }>`
  flex: 1;
  overflow-y: auto;

  & .data-table-wrapper {
    width: 100% !important;
  }
  height: 100%;
  display: flex;

  & .breadcrumbs-button {
    display: flex;
    gap: 10px;
    & button {
      font-size: 13px;
      height: 24px;
      padding: 0px 10px;
      border-radius: 3px;
    }
  }

  & .mask {
    inset: 0;
    position: fixed;
    z-index: 5005;
  }

  & .name-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
    text-align: left;

    & svg {
      min-width: 18px;
      height: 18px;
    }
  }

  & .table-wrapper {
    overflow: auto;
    margin-bottom: 12px;
  }
  & table {
    width: 100% !important;
  }
  flex-direction: column;
  & footer {
    width: -webkit-fill-available;
    margin-top: auto;
  }

  & .ant-table-wrapper {
    overflow: auto;
  }
  & .ant-input-group-wrapper {
    display: flex;

    & .ant-input-group-addon {
      padding: 0px;
      border: 0px;
    }

    & button {
      height: 41px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      background: ${({ theme }) => theme.colorPrimary};
      padding: 4px 12px;
      box-shadow: none;

      & svg {
        width: 26px;
      }
      & path {
        fill: #fff;
      }
    }
  }
`;
