/**
 * Utility functions for safely managing navigationItems data integrity
 * Prevents data loss during reordering operations in filtered views
 */

/**
 * Safely merges reordered visible navigation items with the complete navigation items array.
 * This ensures that ALL navigation items are preserved, even those that are currently filtered out.
 * 
 * Problem solved: When users reorder navigation tabs in filtered views (e.g., DQM context, 
 * working version context), only the visible items were being saved, causing permanent loss 
 * of filtered-out items from the navigationItems array.
 * 
 * Solution: This function preserves ALL items from the complete array while respecting 
 * the new order of visible items.
 * 
 * @param completeNavigationItems - The complete array of all navigation items from Redux store
 * @param reorderedVisibleKeys - The reordered array of only the currently visible items
 * @returns A new array with all items preserved and visible items in their new order
 * 
 * @example
 * // Original complete array: ["assets", "relation", "comment", "working-version", "history", "logs"]
 * // User sees filtered view: ["assets", "relation", "comment", "history"] (working-version, logs hidden)
 * // User reorders to: ["relation", "assets", "comment", "history"]
 * // Result: ["relation", "assets", "comment", "working-version", "history", "logs"]
 * // ✓ All items preserved, visible items reordered, hidden items maintain relative positions
 */
export const createSafeNavigationItemsOrder = (
  completeNavigationItems: string[],
  reorderedVisibleKeys: string[]
): string[] => {
  // Safety checks
  if (!Array.isArray(completeNavigationItems) || completeNavigationItems.length === 0) {
    return reorderedVisibleKeys || [];
  }
  
  if (!Array.isArray(reorderedVisibleKeys) || reorderedVisibleKeys.length === 0) {
    return completeNavigationItems;
  }
  
  // Create a copy of the complete navigation items
  const result = [...completeNavigationItems];
  
  // Find items that are currently visible (in reorderedVisibleKeys)
  const visibleItemsInOriginal = result.filter(item => reorderedVisibleKeys.includes(item));
  
  // If no visible items found in original array, return original array (safety fallback)
  if (visibleItemsInOriginal.length === 0) {
    return result;
  }
  
  // Remove all visible items from their current positions
  const nonVisibleItems = result.filter(item => !reorderedVisibleKeys.includes(item));
  
  // Find the position range where visible items should be placed
  // Use the position of the first visible item in the original array as the insertion point
  const firstVisibleItemIndex = result.findIndex(item => reorderedVisibleKeys.includes(item));
  
  // If no visible items found (shouldn't happen due to earlier check, but safety first)
  if (firstVisibleItemIndex === -1) {
    return result;
  }
  
  // Insert the reordered visible items at the correct position
  const finalResult = [
    ...nonVisibleItems.slice(0, firstVisibleItemIndex),
    ...reorderedVisibleKeys,
    ...nonVisibleItems.slice(firstVisibleItemIndex)
  ];
  
  return finalResult;
};
